<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="用户编号/姓名">
            <el-input size="small" v-model="querys.userName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="组织机构">
            <kade-dropdown-select-tree :action="departAction" style="width: 200px" idKey="deptId" queryKey="deptParentId" :opts="treeOpts" :text="deptName" @change="handleDepartChange" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="querys.userTel" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="证件号码">
            <el-input v-model="querys.userIdNo" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap>
        <template #extra>
          <el-button icon="el-icon-plus" @click="handleAdd" size="small" class="btn-deep-blue">新增</el-button>
          <el-upload style="margin:0 10px" ref="uploadRef" :headers="{ Authorization: `bearer ${state.uploadHeader}` }" class="upload-demo" :show-file-list="false" :action="state.uploadUrl" :on-success="uploadSuccess" :on-error="uploadError">
            <el-button class="btn-import" icon="el-icon-daoru" size="small">导入</el-button>
          </el-upload>
          <el-button class="btn-blue" icon="el-icon-daochu" size="small">导出</el-button>
          <el-button class="btn-green" icon="el-icon-picture" size="small">导入照片</el-button>
          <el-button class="btn-purple" icon="el-icon-edit" size="small">批量修改部门</el-button>
          <el-button class="btn-pink" icon="el-icon-edit" size="small">批量修改状态</el-button>
        </template>
        <el-table style="width: 100%" :data="options.dataList" height="55vh" v-loading="options.loading" border stripe>
          <el-table-column label="用户编号" prop="userCode" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="姓名" prop="userName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="性别" align="center" prop="userSex" show-overflow-tooltip>
            <template #default="scope"> {{ dictionaryFilter(scope.row.userSex) }}</template>
          </el-table-column>
          <el-table-column label="类别名称" align="center" prop="roleName" show-overflow-tooltip></el-table-column>
          <el-table-column label="组织机构" align="center" prop="deptName" show-overflow-tooltip></el-table-column>
          <el-table-column label="用户状态" align="center" prop="userAccountState" show-overflow-tooltip>
            <template #default="scope">{{ dictionaryFilter(scope.row.userAccountState) }}</template>
          </el-table-column>
          <el-table-column label="人员状态" align="center" show-overflow-tooltip>
            <template #default="scope">
              <el-tag size="small" :type=" scope.row.userState === 'STATE_IN' ? 'primary' : 'warning' ">{{ scope.row.userStateName }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="用户来源" align="center" show-overflow-tooltip>
            <template #default="scope"> {{ dictionaryFilter(scope.row.userSource) }} </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button @click="handleInfo(scope.row)" type="text" size="mini">详情</el-button>
              <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" :page-size="options.pageSize" @current-change="(val) => pageChange(val)" @size-change="(val) => sizeChange(val)">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElPagination,
  ElMessageBox,
  ElMessage,
  ElTag,
  ElUpload,
} from "element-plus";
import { useStore } from "vuex";
import {
  getUserInfoListByPage,
  deleteById,
  getDepartTree,
} from "@/applications/eccard-basic-data/api";
import { usePagination } from "@/hooks/usePagination";
import DropdownSelectTree from "@/components/dropdownSelectTree";
import { createGuid, getToken } from "@/utils";
import { useEvent } from "@/hooks";
import { reactive, ref } from "vue";
const getQuerys = () => ({
  userTel: "",
  userIdNo: "",
  userName: "",
  userDept: "",
});
export default {
  components: {
    "el-table": ElTable,
    "el-button": ElButton,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-tag": ElTag,
    ElUpload,
    "kade-dropdown-select-tree": DropdownSelectTree,
  },
  setup() {
    const deptName = ref(null);
    const store = useStore();
    const state = reactive({
      uploadHeader: getToken(),
      uploadUrl: `${CONFIG.BASE_API_PATH}eccard-basic-data/UserInfo/UserInfoImport`,
    })
    const { $on } = useEvent();
    const { options, search, querys, loadData, pageChange, sizeChange } = usePagination(
      getUserInfoListByPage,
      getQuerys()
    );
    $on("personal/refresh", () => {
      loadData();
    });
    const handleAdd = () => {
      store.dispatch("app/addTab", {
        id: createGuid(),
        payload: {
          menuName: "新增人员信息",
          menuEnName: "PersonnelEdit",
        },
      });
    };

    const uploadSuccess = ({ code, message }) => {
      if (code === 0) {
        ElMessage.success(message)
        loadData()
      } else {
        ElMessage.error(message)
      }
    }
    const uploadError = (err) => {
      console.log(err);
    }
    const handleInfo = (row) => {
      store.dispatch("app/addTab", {
        id: `personaledit${row.id}`,
        payload: {
          menuName: `${row.userName}`,
          menuEnName: "PersonnelEdit",
          query: {
            id: row.id,
          },
        },
      });
    };
    const handleDel = ({ userName, id }) => {
      ElMessageBox.confirm(`确认删除用户"${userName}"?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await deleteById({ id });
          if (code === 0) {
            ElMessage.success(message);
          } else {
            ElMessage.error(message);
          }
          loadData();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const departAction = async (params) => {
      const {
        data: { deptMenuList },
      } = await getDepartTree(params);
      return deptMenuList;
    };

    const handleDepartChange = (data) => {
      querys.userDept = data?.deptId || "";
      deptName.value = data?.deptName || "";
    };

    const handleReset = () => {
      Object.assign(querys, getQuerys());
      querys.userDept = "";
      deptName.value = "";
    };

    return {
      state,
      options,
      querys,
      pageChange,
      sizeChange,
      uploadSuccess,
      uploadError,
      handleAdd,
      loadData,
      handleSearch: search,
      handleReset,
      handleInfo,
      handleDel,
      departAction,
      deptName,
      handleDepartChange,
      treeOpts: {
        props: {
          children: "children",
          label: "deptName",
          isLeaf: (data) => {
            if (data["isLeaf"] === undefined) {
              return true;
            }
            return !!data.isLeaf;
          },
        },
      },
    };
  },
};
</script>
<style lang="scss" scoped>
.personnelInformation {
  width: 100%;
  height: 100%;
}
</style>