<template>
  <kade-route-card>
    <kade-table-filter @search="search" @reset="handleReset">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="商户名称:">
          <el-input style="width: 215px" v-model="querys.keyWord" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="商家类型:">
          <el-select clearable placeholder="请选择" v-model="querys.merchantType" style="width: 100%">
            <el-option v-for="item in types" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域:">
          <kade-area-select-tree style="width: 100%" :value="querys.areaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (querys.areaId = val.id)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-merchant-list :tableOptions="options" @on-select-change="handleSelectChange"
      @on-page-change="handlePageChange" :load-data="loadData" />
    <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
      <template #jsjl>
        <kade-settlement-record :id="state.selected?.merchantId" />
      </template>
      <template #jsfl>
        <kade-settlement-rate :selected="state.selected" :loadData="loadData" :id="state.selected?.merchantId" />
      </template>
    </kade-tab-wrap>
  </kade-route-card>
</template>
<script>
import { ElForm, ElFormItem, ElInput, ElOption, ElSelect } from "element-plus";
import { reactive, ref } from "vue";
import { usePagination } from "@/hooks/usePagination";
import { useDict } from "@/hooks/useDict";
import merchantList from "./components/merchantList";
import SettlementRate from "./components/settlementRate";
import SettlementRecord from "./components/settlementRecord";
import {
  getMerchantListByPage,
  printMerchantSettlementProof,
} from "@/applications/eccard-finance/api";

import { printTable } from "@/components/print";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
const tabs = [
  {
    name: "jsjl",
    label: "结算收支",
  },
  {
    name: "jsfl",
    label: "结算费率",
  },
];

export default {
  components: {
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "kade-merchant-list": merchantList,
    "kade-settlement-rate": SettlementRate,
    "kade-settlement-record": SettlementRecord,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const { options, search, querys, loadData } = usePagination(
      getMerchantListByPage,
      {
        keyWord: "",
        merchantType: "",
        areaId: "",
      },
      {},
      { currentPage: "currentPage", pageSize: "pageSize" }
    );
    const tableRef = ref(null);
    const types = useDict("MERCHANT_TYPE");
    const state = reactive({
      tab: "jsjl",
      areaName: "",
      selected: null,
    });
    const handleAreaChange = (data) => {
      state.areaName = data?.areaName || "";
      querys.areaId = data?.areaId || "";
    };
    const handleSelectChange = (v) => {
      state.selected = v;
    };
    const handlePageChange = () => {
      if (
        state.selected &&
        options.dataList.some(
          (it) => it.merchantId === state.selected.merchantId
        )
      ) {
        tableRef.value?.tableRef?.setCurrentRow?.(state.selected);
      }
    };
    const handleCreateVoucher = async () => {
      const { data } = await printMerchantSettlementProof({
        id: state.selected.merchantId,
      });
      printTable.show([], data || []);
    };
    const handleReset = () => {
      Object.assign(querys, {
        areaId: "",
        keyWord: "",
        merchantType: "",
      });
      state.areaName = "";
      querys.areaId = "";
    };
    return {
      state,
      options,
      search,
      querys,
      loadData,
      tabs,
      types,
      handleReset,
      handleAreaChange,
      handleSelectChange,
      handlePageChange,
      handleCreateVoucher,
    };
  },
};
</script>