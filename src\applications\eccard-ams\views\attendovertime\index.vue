<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="加班规则名称">
          <el-input v-model="state.form.ovrName" placeholder="加班规则名称搜索"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="加班规则列表（备注：系统预置前三个规则）">
      <template #extra>
        <el-button size="mini" type="success" icon="el-icon-plus" @click="edit('','add')">新增</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="规则名称" prop="ovrName" align="center"></el-table-column>
        <el-table-column label="使用考勤组" prop="atgNameList" align="center"></el-table-column>
        <el-table-column label="规则内容" prop="ovrName" align="center">
          <template #default="scope">
            <p v-if="scope.row.ovrWTimestype">{{ "工作日:" + scope.row.ovrWTimestype}}</p>
            <p v-if="scope.row.ovrRTimestype">{{ "休息日:" + scope.row.ovrRTimestype}}</p>
            <p v-if="scope.row.ovrHTimestype">{{ "节假日:" + scope.row.ovrHTimestype}}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="edit(scope.row , 'edit')" type="text" size="mini">编辑</el-button>
            <el-button @click="del(scope.row , 'edit')" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <attend-group-edit :modelValue="state.isEdit" :title="state.type == 'add' ? '新增规则' : '编辑规则'"
                       :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
  </kade-route-card>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { getAttentionOverTimeRulePage , delAttentionOverTimeRule } from "@/applications/eccard-ams/api";
  import attendGroupEdit from "./components/edit.vue"
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessage,
    ElMessageBox
  } from 'element-plus';

  export default {
    components: {
      attendGroupEdit,
      ElForm,
      ElFormItem,
      ElInput,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
    },
    setup() {
      const atgTypeList = useDict("ATTENTION_GROUP_TYPE");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          ovrName :'',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionOverTimeRulePage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }

      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          currentPage:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const del=async (row)=>{
        if(row.ovrId == '4' || row.ovrId == '11' || row.ovrId == '12'){
          ElMessage.warning("当前规则为系统内置规则，不能删除！");return;
        }
        ElMessageBox.confirm(`确定删除当前加班规则信息？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code,message}=await delAttentionOverTimeRule(row.ovrId)
          if(code===0){
            ElMessage.success(message)
            getList()
          }
        });
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        atgTypeList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        del,
        close
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
