<template>
  <div style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="选择月份">
          <el-date-picker v-model="state.form.amrMonth" type="month" placeholder="时间搜索" format="YYYY-MM"
            value-format="YYYY-MM" :clearable="true" />
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgId" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.orgId = val.deptPath)" />
        </el-form-item>
        <el-form-item label="所属考勤组">
          <el-select v-model="state.form.atgId" :clearable="true">
            <el-option v-for="(item, index) in state.groupList" :key="index" :label="item.atgName"
              :value="item.atgId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="请输入姓名或编号搜索" :clearable="true"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="每月汇总列表">
      <template #extra>
        <el-button size="mini" type="primary" icon="Download" @click="handleClick(1)">导出</el-button>
        <el-button size="mini" type="danger" icon="Printer" @click="handleClick(2)">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
        border stripe>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属考勤组" prop="atgName" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="考勤日期" prop="amrMonth" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="出勤天数" prop="amrWordDays" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="休息天数" prop="amrRestDays" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="工作时长" prop="armWorkTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.armWorkTimes ? scope.row.armWorkTimes + '分钟' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="迟到次数" prop="armLateNum" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="迟到时长" prop="armLateTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.armLateTimes ? scope.row.armLateTimes + '分钟' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="缺勤天数" prop="armAbseDays" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="早退次数" prop="armLeaveNum" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="早退时长" prop="armLeaveTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.armLeaveTimes ? scope.row.armLeaveTimes + '分钟' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="加班次数" prop="armOvertimeNum" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="加班时长" prop="armOvertimeTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.armOvertimeTimes ? scope.row.armOvertimeTimes + '分钟' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="请假次数" prop="armAbsenceNum" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="请假时长" prop="armAbsenceTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.armAbsenceTimes ? scope.row.armAbsenceTimes + '小时' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="上班缺卡次数" prop="armWorkmissNum" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="午休缺卡次数" prop="noonMissNum" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="下班缺卡次数" prop="armDownmissNum" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive, onMounted } from "vue";
import { useDict } from "@/hooks/useDict";
import { downloadXlsx, print } from "@/utils"
import { monthStr } from "@/utils/date.js"
import deptSelectTree from '@/components/tree/deptSelectTree'
import {
  // ElSwitch,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
  ElDatePicker,
  ElMessage,
  // ElMessageBox
} from 'element-plus';
import { getAttentionMonthReportPage, getAttentionGroupInfoPageAll, downLoadFile } from "@/applications/eccard-ams/api";

export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElDatePicker,
    ElButton,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const symbolList = useDict("ATTENTION_REPORT_ATTENDTYPE");
    const state = reactive({
      loading: false,
      isEdit: false,
      groupList: [],
      form: {
        attendMonthBegin: '',
        attendMonthEnd: '',
        orgId: '',
        atgId: '',
        keyWord: '',
        pageNum: 1,
        pageSize: 10,
        amrMonth: new Date(),
      },
      dataList: [],
      total: 0,
      rowData: "",
      type: "",
      columnList: [],
    });

    //分页
    const getList = async () => {
      state.loading = true
      let objs = state.form;
      if (objs.amrMonth) {
        objs.amrMonth = monthStr(objs.amrMonth)
      }
      let { data } = await getAttentionMonthReportPage(state.form)
      state.dataList = data.list
      state.total = parseInt(data.count)
      state.loading = false;
    }
    const edit = (row, type) => {
      state.type = type
      state.rowData = row
      state.isEdit = true
    }
    const reset = () => {
      state.form = {
        attendMonthBegin: '',
        attendMonthEnd: '',
        orgId: '',
        atgId: '',
        keyWord: '',
        pageNum: 1,
        pageSize: 10,
        amrMonth: new Date(),
      }
    }
    const search = () => {
      getList()
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }

    const handleClick = async (flag) => {
      let objs = state.form;
      if (objs.amrMonth) {
        objs.amrMonth = monthStr(objs.amrMonth)
      }

      if (flag == 1) {
        let res = await downLoadFile("/eccard-ams/api/ams/attention-month-report/export", state.form);
        downloadXlsx(res, "每月汇总报表.xlsx");
      } else {
        let { code, message } = await downLoadFile("/eccard-ams/api/ams/attention-month-report/export?type=2", state.form, 2);
        if (code == 0) {
          print(message, '每月汇总报表')
        } else {
          ElMessage.warning(message);
        }
      }
    }

    const handleSizeChange = (val) => {
      state.form.pageSize = val
      getList()
    }
    // const del=async (row)=>{
    //   let {code,message}=await delSysMessage(row.id)
    //   if(code===0){
    //     ElMessage.success(message)
    //     getList()
    //   }
    // }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }

    // const groupList = async () => {
    //   let {data}=await getAttentionGroupInfoPageAll();
    //   return data.list;
    // };

    onMounted(async () => {
      getList()
      let { data } = await getAttentionGroupInfoPageAll();
      state.groupList = data;
    })
    return {
      state,
      symbolList,
      edit,
      reset,
      search,
      handlePageChange,
      handleSizeChange,
      // del,
      close,
      // groupList,
      handleClick
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
