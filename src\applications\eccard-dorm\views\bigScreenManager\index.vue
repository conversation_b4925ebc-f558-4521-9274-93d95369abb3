<template>
  <kade-route-card>
    <kade-table-wrap title="宿舍大屏列表">
      <el-table border :data="state.data">
        <el-table-column show-overflow-tooltip prop="label" label="设备名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="area" label="区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="ip" label="ip" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="position" label="存放地址" align="center"></el-table-column>
        <el-table-column prop="" label="操作" align="center" width="80px">
          <template #default="scope">
            <el-button type="text" @click="handleSee(scope.row)" class="green" size="mini">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
  </kade-route-card>
</template>

<script>
import { ElTable, ElTableColumn, ElButton, } from "element-plus"
import { reactive } from '@vue/reactivity'
// import { useRouter } from "vue-router"
export default {
  components: {
    ElTable,
    ElTableColumn,
    ElButton,
  },
  setup() {
    // const router = useRouter()
    const state = reactive({
      data: [
        /* { label: "人脸分析盒", buildId: '40', area: "培训中心", ip: "**************", position: "培训中心入口机柜" }, */
        { label: "人脸分析盒", buildId: 1, area: "大专公寓一栋", ip: "**************", position: "1号宿舍机柜" },
        { label: "人脸分析盒", buildId: 2, area: "大专公寓二栋", ip: "**************", position: "2号宿舍机柜" },
        { label: "人脸分析盒", buildId: 3, area: "大专公寓三栋", ip: "**************", position: "3号宿舍机柜" },
        { label: "人脸分析盒", buildId: 4, area: "大专公寓四栋", ip: "**************", position: "4号宿舍机柜" },
        { label: "人脸分析盒", buildId: 5, area: "附中一栋公寓", ip: "**************", position: "附中1号宿舍机柜" },
        { label: "人脸分析盒", buildId: 6, area: "附中二栋公寓", ip: "**************", position: "附中2号宿舍机柜" },
        { label: "人脸分析盒", buildId: 8, area: "艺苑4号楼男生公寓", ip: "**************", position: "艺苑4号宿舍机柜" },
        { label: "人脸分析盒", buildId: 9, area: "艺苑6号楼男生公寓", ip: "**************", position: "艺苑6号宿舍机柜" },
      ],
    });
    const handleSee = row => {
      console.log(row);
      /* router.push({
        name: "AIScreen",
        params: {
          ip: row.ip,
          buildId: row.buildId,
          buildName:row.area
        }
      }) */
      window.open(
        `http://${row.ip}/#/structure`,
        "_blank"
      );
    }

    return {
      state,
      handleSee
    }
  }
}
</script>

<style lang="scss" scoped>
.kade-table-wrap {
  padding: 0;
  border-bottom: none;
}
</style>