import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import {
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
	...mixinState,
	dictionary: dictionary ? JSON.parse(dictionary) : [],
	applyTypes: [],
	componentMap: {
    /* 总览 */
    amsHome: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/overview'))),
    amsBasicUser: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/userattend'))),
    amsBasicDev: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attenddev'))),
    amsAmsGoupSet: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendgroup'))),
    amsAmsGoupClass: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendclass'))),
    amsAmsRule: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendovertime'))),
    amsAttendRule: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendrule'))),
    amsAttendAddinfo: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendaddinfo'))),
    amsAttendOverTime: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendovertimeaddinfo'))),
    amsLeaverecord: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/leaverecord'))),
    amsViewAddinfo: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/attendviewaddinfo'))),
    amsReportBasic: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/attendbasicinfo'))),
    amsReportDetail: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/attenddetailinfo'))),
    amsReportAbsence: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/attendabsenceinfo'))),
    amsReportSymbol: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/symbolreportinfo'))),
    amsReportDayAttend: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/attendreportinfo'))),
    amsReportDayTotal: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/dayreportsum'))),
    amsReportMonthSum: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/monthreportsum'))),
    amsReportDayCal: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/dayreportcal'))),
    amsReportDayDep: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/dayreportdep'))),
    amsReportMonthDep: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/report/monthreportdep'))),

		holidayConfig: markRaw(defineAsyncComponent(() => import( /* webpackChunkName: "dormDaily" */ '../../views/holidayConfig'))),
},
	customMenus: [],
};
const mutations = {
	updateState(state, { key, payload }) {
		state[key] = payload;
	},
};
const actions = {
	...mixinActions,
	async loadDictionary({ commit }) {
		const { data } = await getDictionary();
		localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
		commit('updateState', { key: 'dictionary', payload: data });
	},
};

const getters = {
	...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters,
}
