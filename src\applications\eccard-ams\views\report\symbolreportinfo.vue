<template>
  <div style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="查询时间">
          <el-date-picker v-model="state.form.attendMonth" type="month" placeholder="时间搜索"  format="YYYY-MM" value-format="YYYY-MM"  :clearable="true"/>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="请输入姓名或编号搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="显示内容" v-if="false">
          <el-select v-model="state.form.symbolFlag" multiple collapse-tags :clearable="true">
            <el-option v-for="(item,index) in symbolList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="符号报表">
      <template #extra>
        <el-button size="mini" type="primary" icon="Download" @click="handleClick(1)">导出</el-button>
        <el-button size="mini" type="danger" icon="Printer" @click="handleClick(2)">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column v-for="(item,index) in state.columnList" :key="index + '222'" :label="item" prop="monthDataList" align="center">
          <template #default="scope">
            {{ scope.row.monthDataList.filter((sitem)=>{return sitem.key == item})[0]["value"] }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { dateStr } from "@/utils/date.js"
  import { downloadXlsx , print } from "@/utils"
  import deptSelectTree from '@/components/tree/deptSelectTree'
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElDatePicker,
    ElMessage,
    // ElMessageBox
  } from 'element-plus';
  import { getSymbolReportPage , getSymbolReportMonthList , downLoadFile } from "@/applications/eccard-ams/api";

  export default {
    props: {
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    components: {
      ElForm,
      ElFormItem,
      ElInput,
      ElSelect,
      ElOption,
      ElTable,
      ElTableColumn,
      ElDatePicker,
      ElButton,
      ElPagination,
      "kade-dept-select-tree":deptSelectTree,
    },
    setup() {
      const symbolList = useDict("ATTENTION_REPORT_SYMBOL");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          attendMonth:'',
          orgId:'',
          keyWord:'',
          symbolFlag:'',
          pageNum:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        columnList:[],
      });

      //分页
      const getList=async ()=>{
        if(!state.form.attendMonth){
          ElMessage.warning("请选择查询月份！");return;
        }
        state.form.attendMonth = dateStr(state.form.attendMonth);
        state.loading=true;

        let objs = {};
        for(let a in state.form){
          if(a != 'symbolFlag'){
            objs[a] = state.form[a];
          }
        }

        let {data}=await getSymbolReportPage(objs)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false;

        if(state.dataList.length == 0){
          let {data}=await getSymbolReportMonthList(objs);
          state.columnList = data;
        }else{
          state.columnList = data.list[0].monthDataList.map(item=>{
            return item.key;
          })
        }
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          pageNum:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const handleClick=async (flag)=>{
        if (!state.form.attendMonth) {
          ElMessage.warning("请选择查询月份！");
          return;
        }
        state.form.attendMonth = dateStr(state.form.attendMonth);

        if(flag == 1) {
          let res = await
          downLoadFile("/eccard-ams/api/ams/attention-symbol-report/export", state.form);
          downloadXlsx(res, "符号报表.xlsx");
        }else{
          let {code , message} = await downLoadFile("/eccard-ams/api/ams/attention-symbol-report/export?type=2", state.form , 2);
          if(code == 0){
            print(message, '符号报表')
          }else{
            ElMessage.warning(message);
          }
        }
      }

      // const del=async (row)=>{
      //   let {code,message}=await delSysMessage(row.id)
      //   if(code===0){
      //     ElMessage.success(message)
      //     getList()
      //   }
      // }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      // groupList = computed(() => {
      //   let {data}=await getAttentionGroupInfoPageAll();
      //   return data.list;
      // });

      onMounted(()=>{
        state.form.attendMonth = new Date();
        getList()
      })
      return {
        state,
        symbolList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        // del,
        close,
        // groupList
        handleClick
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
