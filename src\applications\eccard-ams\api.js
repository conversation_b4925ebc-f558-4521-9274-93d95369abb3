import request from '@/service';

///api/eccard-ams/api/ams/attention-report/current
/**
 * 获取当天的考勤统计信息
 */
export function getCurrentDayAttentReport() {
  return request.get('/eccard-ams/api/ams/attention-report/current', {});
}
/*
获取最近7天的统计信息
 */
export function getCurrentSevenDayReport() {
  return request.get('/eccard-ams/api/ams/attention-report/getSevenDayReport', {});
}
/*
获取用户信息分页
 */
export function getAttentionUserAttendPage(params) {
  return request.get('/eccard-ams/api/ams/attention-user-attend/page', { params });
}

export function postAttentionUserAttendPage(params) {
  return request.post('/eccard-ams/api/ams/attention-user-attend/page', params );
}

export function postSelectAttentionUserAttendPage(params) {
  return request.post('/eccard-ams/api/ams/attention-user-attend/select', params );
}

/*
获取登录用户信息分页
 */
export function getAttentionAccountUserPage(params) {
  return request.get('/eccard-ams/api/ams/attention-user-attend/accountPage', { params });
}

/*
获取用户信息不分页
 */
export function getAttentionUserAttendAll(params) {
  return request.get('/eccard-ams/api/ams/attention-user-attend', { params });
}

/*
修改用户信息
 */
export function editAttentionUserAttend(params) {
  return request.put('/eccard-ams/api/ams/attention-user-attend', params );
}

//解绑设备分组
export function removeBindDevGroup(params){
  return request.put('/eccard-ams/api/ams/attention-user-attend/removeBindDevGroup',  params );
}

/*
得到考勤用户详细信息
 */
export function getAttentionUserAttendDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-user-attend/' + params);
}

/*
获取考勤记录信息分页
 */
export function getAttentionInfoPage(params) {
  return request.get('/eccard-ams/api/ams/attention-info/page', { params });
}

/*
获取全部考勤设备分组信息
 */
export function getAttentionAttendDevGroupAll(params) {
  return request.get('/eccard-ams/api/ams/attention-dev-group', { params });
}
/*
添加考勤设备分组信息
 */
export function addAttentionAttendDevGroupAll(params) {
  return request.post('/eccard-ams/api/ams/attention-dev-group',  params );
}
/*
修改考勤设备分组信息
 */
export function editAttentionAttendDevGroupAll(params) {
  return request.put('/eccard-ams/api/ams/attention-dev-group', params );
}
export function delAttentionAttendDevGroupAll(params) {
  return request.delete('/eccard-ams/api/ams/attention-dev-group/' + params);
}

/*
获取设备信息分页
 */
export function getDevInfoPage(params) {
  return request.get('/eccard-ams/api/ams/attention-dev/page', { params });
}

/*
绑定设备信息到分组
 */
export function batchBindDevInfo(ids , dgtId) {
  let params = { devIds : ids , atgId : dgtId };
  return request.get('/eccard-ams/api/ams/attention-dev/batchBindGroup', {params});
}
//batchbindDevInfo

/*
解绑定设备信息
 */
export function unBindDevInfo(params) {
  return request.put('/eccard-ams/api/ams/attention-dev/removeGroup', params);
}

/*
授权人员设备分组
 */
export function batchBindUserInfo(params) {
  return request.get('/eccard-ams/api/ams/attention-user-attend/batchBindDevGroup', {params});
}

/*
获取考勤分组信息分页
 */
export function getAttentionGroupInfoPage(params) {
  return request.get('/eccard-ams/api/ams/attention-group/page', { params });
}

export function getAttentionGroupInfoPageAll() {
  return request.get('/eccard-ams/api/ams/attention-group', { });
}

/*
删除考勤分组信息分页
 */
export function delAttentionGroupInfoPage(params) {
  return request.delete('/eccard-ams/api/ams/attention-group/' + params);
}

/*
获取全部考勤班次信息
 */
export function getAttentionClassAll(params) {
  return request.get('/eccard-ams/api/ams/attention-class', { params });
}

/*
修改考勤分组
 */
export function updateAttentGroup(params){
  return request.put('/eccard-ams/api/ams/attention-group',  params );
}

/*
添加考勤分组
 */
export function addAttentGroup(params){
  return request.post('/eccard-ams/api/ams/attention-group',  params );
}

/*
得到考勤分组详细信息
 */
export function getAttentionGroupDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-group/' + params);
}

/*
获取班次信息分页
 */
export function getAttentionClassInfoPage(params) {
  return request.get('/eccard-ams/api/ams/attention-class/page', { params });
}

/*
删除班次信息分页
 */
export function delAttentionClassInfoPage(params) {
  return request.delete('/eccard-ams/api/ams/attention-class/' + params );
}


/*
修改班次信息
 */
export function updateAttentClass(params){
  return request.put('/eccard-ams/api/ams/attention-class',  params );
}

/*
添加班次信息
 */
export function addAttentClass(params){
  return request.post('/eccard-ams/api/ams/attention-class',  params );
}

/*
得到班次信息详情 getAttentionClassDetail
 */
export function getAttentionClassDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-class/' + params);
}

/*
获取加班规则信息分页
 */
export function getAttentionOverTimeRulePage(params) {
  return request.get('/eccard-ams/api/ams/attention-overtime-rule/page', { params });
}

/*
删除加班规则信息
 */
export function delAttentionOverTimeRule(params) {
  return request.delete('/eccard-ams/api/ams/attention-overtime-rule/' + params );
}

/*
得到加班规则信息详情
 */
export function getAttentionOverTimeRuleDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-overtime-rule/' + params);
}

/*
修改加班规则信息
*/
export function updateAttentionOverTimeRule(params){
  return request.put('/eccard-ams/api/ams/attention-overtime-rule',  params );
}

/*
添加加班规则信息
 */
export function addAttentionOverTimeRule(params){
  return request.post('/eccard-ams/api/ams/attention-overtime-rule',  params );
}



/*
获取出勤补录信息分页
 */
export function getAttentionAddInfoPage(params) {
  return request.get('/eccard-ams/api/ams/attention-addinfo/page', { params });
}

/*
删除出勤补录信息
 */
export function delAttentionAddInfo(params) {
  return request.delete('/eccard-ams/api/ams/attention-addinfo/' + params );
}

/*
修改出勤补录信息
*/
export function updateAttentionAddInfo(params){
  return request.put('/eccard-ams/api/ams/attention-addinfo',  params );
}

/*
添加出勤补录信息
 */
export function addAttentionAddInfo(params){
  return request.post('/eccard-ams/api/ams/attention-addinfo',  params );
}

/*
得到出勤补录信息详情
 */
export function getAttentionAddInfoDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-addinfo/' + params);
}

//attention-overtime-addinfo
/*
获取加班补录信息分页
 */
export function getAttentionOvertimeAddInfo(params) {
  return request.get('/eccard-ams/api/ams/attention-overtime-addinfo/page', { params });
}

/*
删除加班补录信息
 */
export function delAttentionOvertimeAddInfo(params) {
  return request.delete('/eccard-ams/api/ams/attention-overtime-addinfo/' + params );
}

/*
修改加班补录信息
*/
export function updateAttentionOvertimeAddInfo(params){
  return request.put('/eccard-ams/api/ams/attention-overtime-addinfo',  params );
}

/*
添加加班补录信息
 */
export function addAttentionOvertimeAddInfo(params){
  return request.post('/eccard-ams/api/ams/attention-overtime-addinfo',  params );
}

/*
得到加班补录信息详情
 */
export function getAttentionOvertimeAddInfoDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-overtime-addinfo/' + params);
}

/*
获取考勤分组下的班次信息
 */
export function getDistinctAttentionClass(params ) {
  return request.get('/eccard-ams/api/ams/attention-group/getDistinctAttentionClass?atgId=' + params.atgId);
}



/*
获取请假申请信息分页
 */
export function getAttentionLeaveApplyPage(params) {
  return request.get('/eccard-ams/api/ams/attention-leave-apply/page', { params });
}
/*
获取请假申请信息详情
 */
export function getAttentionLeaveApplyDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-leave-apply/' + params);
}


/*
修改请假补录信息
*/
export function updateAttentionLeaveApplyAddInfo(params){
  return request.put('/eccard-ams/api/ams/attention-leave-apply',  params );
}

/*
添加请假补录信息
 */
export function addAttentionLeaveApplyAddInfo(params){
  return request.post('/eccard-ams/api/ams/attention-leave-apply',  params );
}

export function delAttentionLeaveApplyAddInfo(params) {
  return request.delete('/eccard-ams/api/ams/attention-leave-apply/' + params );
}


export function examAttentionLeaveApply(params){
  return request.put('/eccard-ams/api/ams/attention-leave-apply/exam',  params );
}

/*
获取人员出入记录信息
 */
export function getAttentionUserAccessPage(params) {
  return request.get('/eccard-ams/api/ams/attention-user-access/page', { params });
}


/*
获取全部补录信息分页
 */
export function getAttentionAllInfoPage(params) {
  return request.get('/eccard-ams/api/ams/attention-addinfo/viewPage', { params });
}

/*
获取全部补录信息分页
 */
export function getAttentionAllInfoStatis(params) {
  return request.get('/eccard-ams/api/ams/attention-addinfo/statisData', { params });
}

/*
加班补录审核
 */
export function examAttentionOvertimeAddInfo(params){
  return request.put('/eccard-ams/api/ams/attention-overtime-addinfo/exam',  params );
}

/*
考勤补录审核
 */
export function examAttentionAddInfo(params){
  return request.put('/eccard-ams/api/ams/attention-addinfo/exam',  params );
}



/*
考勤规则详情
 */
export function getAttentionRuleDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-rule/' + params);
}
/*
修改考勤规则信息
*/
export function updateAttentionRuleInfo(params){
  return request.put('/eccard-ams/api/ams/attention-rule',  params );
}

export function addAttentionRuleInfo(params){
  return request.post('/eccard-ams/api/ams/attention-rule',  params );
}


//缺勤记录表。
/*
获取缺勤信息分页
 */
export function getAttentionAbsencePage(params) {
  return request.get('/eccard-ams/api/ams/attention-absence-info/page', { params });
}

/*
删除缺勤信息
 */
export function delAttentionAbsence(params) {
  return request.delete('/eccard-ams/api/ams/attention-absence-info/' + params );
}

/*
修改缺勤信息
*/
export function updateAttentionAbsence(params){
  return request.put('/eccard-ams/api/ams/attention-absence-info',  params );
}

/*
添加缺勤信息
 */
export function addAttentionAbsence(params){
  return request.post('/eccard-ams/api/ams/attention-absence-info',  params );
}

/*
得到缺勤信息详情
 */
export function getAttentionAbsenceDetail(params) {
  return request.get('/eccard-ams/api/ams/attention-absence-info/' + params);
}


/*
获取符号报表信息分页
 */
export function getSymbolReportPage(params) {
  return request.get('/eccard-ams/api/ams/attention-symbol-report/page', { params });
}

/*
获取符号报表月份列表、
 */
export function getSymbolReportMonthList(params) {
  return request.get('/eccard-ams/api/ams/attention-symbol-report/MontList', { params });
}


/*
获取每日出勤报表信息分页
 */
export function getAttentionReportRecordPage(params) {
  return request.get('/eccard-ams/api/ams/attention-report-record/page', { params });
}

/*
获取每日出勤报表月份列表、
 */
export function getAttentionReportRecordMonthList(params) {
  return request.get('/eccard-ams/api/ams/attention-report-record/MontList', { params });
}


/*
获取每日汇总信息分页
 */
export function getAttentionDayReportPage(params) {
  return request.get('/eccard-ams/api/ams/attention-day-report/page', { params });
}

/*
获取每月汇总信息分页
 */
export function getAttentionMonthReportPage(params) {
  return request.get('/eccard-ams/api/ams/attention-month-report/page', { params });
}

/*
获取用户身份列表
*/
export function getIdeTypeList() {
  return request.get('/eccard-ams/api/ams/base-role' , {});
}

/*
查询考勤统计信息表
 */
export function getAttentionStatisInfoAll(params) {
  return request.get('/eccard-ams/api/ams/attention-statis-info', { params });
}

export function downLoadFile(url , params , type) {
  //格式化对象值
  for(let a in params){
    if(!params[a] || params[a].length == 0){
      params[a] = null;
    }
  }

  if (type == 2) {
    return request.post(url,  params );
  } else {
    // return request.post(url, params);
    return request.post(url, params, {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob'
    });
  }
}

export function getDayDepStatis(params) {
  return request.get('/eccard-ams/api/ams/attention-day-report/getDayDepStatis', { params });
}

export function getMonthDepStatis(params) {
  return request.get('/eccard-ams/api/ams/attention-day-report/getMonthDepStatis', { params });
}




//节假日配置
export function attentionholidayList(params) {
  return request.get('/eccard-ams/api/ams/attentionholiday', { params });
}
export function attentionholidayAdd(params) {
  return request.post('/eccard-ams/api/ams/attentionholiday', params);
}
export function attentionholidayEdit(params) {
  return request.put('/eccard-ams/api/ams/attentionholiday', params);
}
export function attentionholidayDel(params) {
  return request.delete(`/eccard-ams/api/ams/attentionholiday/${params}`);
}