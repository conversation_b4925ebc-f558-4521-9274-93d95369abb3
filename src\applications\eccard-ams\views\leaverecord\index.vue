<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="用户姓名或编号搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgId" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
        <el-form-item label="请假类型">
          <el-select v-model="state.form.leaType" :clearable="true">
            <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker v-model="state.form.leaStarttime" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD" value-format="YYYY-MM-DD"  :clearable="true"/>
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker v-model="state.form.leaEndtime" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="true" />
        </el-form-item>
      </el-form>
    </kade-table-filter>

    <kade-table-wrap title="请假明细列表">
      <template #extra>
        <el-button size="mini" type="success" :icon="Plus" @click="edit1('','add')">新增补录</el-button>
        <el-button size="mini" type="primary" :icon="Download" @click="handleClick(1)">导出</el-button>
        <el-button size="mini" type="primary" :icon="TakeawayBox" @click="handleClick(2)">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="请假类型" prop="leaType" align="center"></el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center"></el-table-column>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="手机号码" prop="userTel" align="center"></el-table-column>
        <el-table-column label="身份类别" prop="userIde" align="center"></el-table-column>
        <el-table-column label="开始时间" prop="leaStarttime" align="center">
          <template #default="scope">
            {{getTimeStr(scope.row.leaStarttime)}}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="leaEndtime" align="center">
          <template #default="scope">
            {{getTimeStr(scope.row.leaEndtime)}}
          </template>
        </el-table-column>
        <el-table-column label="请假时长" prop="leaTime" align="center">
          <template #default="scope">
            {{scope.row.leaTime ? scope.row.leaTime + '小时' : ''}}
          </template>
        </el-table-column>
        <el-table-column label="申请时间" prop="lastModifyTime" align="center">
          <template #default="scope">
            {{getTimeStr(scope.row.lastModifyTime)}}
          </template>
        </el-table-column>
        <el-table-column label="通过时间" prop="leaExamDate" align="center">
          <template #default="scope">
            {{scope.row.leaExamResult == '1' && scope.row.leaExamDate ? getTimeStr(scope.row.leaExamDate) : ''}}
          </template>
        </el-table-column>
        <el-table-column label="请假原因" prop="leaReson" align="center"></el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="edit1(scope.row , 'edit')" type="text" size="mini">修改</el-button>
            <el-button @click="edit(scope.row , 'info')" type="text" size="mini">详情</el-button>
            <el-button @click="del(scope.row , 'edit')" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <attend-leave-edit :modelValue="state.isEdit" :title="state.type == 'add' ? '新增请假补录' : (state.type == 'edit' ? '编辑请假补录' : '请假详情')"
                       :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
    <attend-leave-add :modelValue="state.isEdit1" :title="state.type == 'add' ? '新增请假补录' : (state.type == 'edit' ? '编辑请假补录' : '请假详情')"
                      :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
  </kade-route-card>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { downloadXlsx , print } from "@/utils"
  import { useDict } from "@/hooks/useDict";
  import { getAttentionLeaveApplyPage ,downLoadFile , delAttentionLeaveApplyAddInfo } from "@/applications/eccard-ams/api";
  import attendLeaveEdit from "./components/edit.vue"
  import attendLeaveAdd from "./components/addedit.vue"
  import { timeStr } from "@/utils/date.js"
  import deptSelectTree from '@/components/tree/deptSelectTree'
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElDatePicker,
    ElMessage,
    ElMessageBox
  } from 'element-plus';

  export default {
    components: {
      attendLeaveEdit,
      attendLeaveAdd,
      ElForm,
      ElFormItem,
      ElInput,
      ElSelect,
      ElOption,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      ElDatePicker,
      "kade-dept-select-tree":deptSelectTree,
    },
    setup() {
      const statusList = useDict("ATTENTION_LEAVE_TYPE");
      const state = reactive({
        loading: false,
        isEdit:false,
        isEdit1:false,
        form:{
          keyWord:'',
          userNum : '',
          userName: '',
          orgId: '',
          userPostStatus : '',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionLeaveApplyPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }

      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }

      const edit1=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit1=true
      }

      const reset=()=>{
        state.form={
          currentPage:1,
          pageSize:10
        }
      }
      const getTimeStr = (val) =>{
        if(!val){
          return "";
        }
        return timeStr(val);
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleClick=async (flag)=>{
        if(flag == 1) {
          let res = await downLoadFile("/eccard-ams/api/ams/attention-leave-apply/export?type=1", state.form);
          downloadXlsx(res, "请假记录列表.xlsx");
        }else{
          let {code , message} = await downLoadFile("/eccard-ams/api/ams/attention-leave-apply/export?type=2", state.form , 2);
          if(code == 0){
            print(message, '请假记录列表')
          }else{
            ElMessage.warning(message);
          }
        }
      }

      const del=async (row)=>{
        ElMessageBox.confirm(`确定删除当前请假补录信息？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code,message}=await delAttentionLeaveApplyAddInfo(row.leaId)
          if(code===0){
            ElMessage.success(message)
            getList()
          }
        });
      }

      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false;
        state.isEdit1 =false;
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        statusList,
        edit,
        edit1,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        getTimeStr,
        close,
        handleClick,
        del,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
