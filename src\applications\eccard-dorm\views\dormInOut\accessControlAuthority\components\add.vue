<template>
  <el-dialog :model-value="modelValue" title="添加门锁权限" width="1550px" :before-close="handleClose">
    <el-row :gutter="10">
      <el-col :span="9">
        <div class="select-access">
          <div class="title">选择房间</div>
          <el-divider></el-divider>
          <kade-select-table :isShow="modelValue" :value='[]' :column="accessColumn" :linkage="linkage" :selectCondition="[]" layout="prev, pager, next" :params="roomParams" :reqFnc="getRoomList" :isMultiple="true" :isCurrentSelect="true" @change="roomChange" />
        </div>
      </el-col>
      <el-col :span="15">
        <div class="select-user">
          <div class="title">选择用户</div>
          <el-divider></el-divider>
          <kade-select-table :isShow="modelValue" :value='[]' :column="userColumn" :selectCondition="userSelection" :params="userParams" :reqFnc="getUserInfoListByPage" :isMultiple="true" :isCurrentSelect="true" @change="personChange" />
        </div>
      </el-col>
    </el-row>
    <el-form label-width="100px" size="mini">
      <el-form-item label="授权时间">
        <el-date-picker v-model="state.dateTime" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" :loading="state.loading" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, onMounted, watch } from 'vue'
import { timeStr } from "@/utils/date.js"
import selectTable from '@/components/table/selectTable'
import { ElDialog, ElButton, ElRow, ElCol, ElDivider, ElForm, ElFormItem, ElDatePicker, ElMessage } from "element-plus"
import { getRoomList, oldRoomAuthLockAuthAdd } from "@/applications/eccard-dorm/api";
import { getUserInfoListByPage, getRolelist } from "@/applications/eccard-basic-data/api";
const accessColumn = [
  {
    label: '房间', prop: 'roomName', isRow: true, render: (val) => {
      return `${val.buildName}>${val.unitNum}单元>${val.floorNum}楼>${val.roomName}`
    }
  },
]
const userColumn = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "roleName", isDict: false, width: "" },
]
const linkage = {
  linkageData: {
    area: { label: '区域', valueKey: 'areaId', key: 'id' },
    building: { label: '楼栋', valueKey: 'buildId' },
    unit: { label: '单元', valueKey: 'unitNum' }
  }
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElDivider, ElForm, ElFormItem, ElDatePicker,
    "kade-select-table": selectTable
  },
  setup(props, context) {
    console.log("props", props);

    const state = reactive({
      loading: false,
      dateTime: [],
      personList: [],
      roomList: []
    })
    const userSelection = [
      { label: '组织机构', valueKey: 'userDept', dataKey: "deptPath", placeholder: '请选择', isTree: 'dept' },
      { label: '身份类别', valueKey: 'userRole', placeholder: '请选择', isSelect: true, select: { list: [], option: { label: 'roleName', value: 'id' } } },
      { label: '关键字', valueKey: 'userName', placeholder: '姓名或编号关键字搜索', isSelect: false },
    ]
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        userSelection[1].select.list = res.data
      });
    };



    const roomParams = {
      currentPageKey: "currentPage",
      pageSizeKey: "pageSize",
      resListKey: "list",
      resTotalKey: "total",
      value: {
        roomKey: props.type,
      },
      tagNameKey: "roomName",
      valueKey: "id",
    }



    const userParams = {
      currentPageKey: "beginPage",
      pageSizeKey: "rowNum",
      resListKey: "dataList",
      resTotalKey: "totalCount",
      value: {},
      tagNameKey: "userName",
      valueKey: "id",
    }

    const roomChange = val => {
      state.roomList = val.list

    }
    const personChange = val => {
      state.personList = val.list

    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    const submit = async () => {
      if (!state.roomList.length) return ElMessage.error("请选择房间")
      if (!state.personList.length) return ElMessage.error("请选择人员")
      if (!state.dateTime || !state.dateTime.length) return ElMessage.error("请选择授权时间")

      let params = {
        roomInfoList: state.roomList,
        userIdList: state.personList.map(item => item.id),
        authBeginTime: timeStr(state.dateTime[0]),
        authEndTime: timeStr(state.dateTime[1]),
        roomKey: props.type
      }
      try {
        state.loading = true
        let { code, message } = await oldRoomAuthLockAuthAdd(params)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    watch(() => props.modelValue, val => {
      if (val) {
        state.dateTime = []
        state.personList = []
        state.roomList = []
        roomParams.value.roomKey = props.type
      }
    })
    onMounted(() => {
      queryRolelist()
    })
    return {
      getRoomList,
      getUserInfoListByPage,
      state,
      submit,
      handleClose,
      accessColumn,
      userColumn,
      linkage,
      userSelection,
      roomParams,
      userParams,
      roomChange,
      personChange,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-row {
  margin: 20px 0;
  .title {
    margin: 10px 20px;
  }

  :deep(.select-access) {
    border: 1px solid #eeeeee;
    border-radius: 3px;

    .el-input__inner {
      width: 140px;
    }

    .el-form-item__content {
      width: 170px !important;
    }

    .select-trigger {
      width: 140px;
    }

    .el-select .el-select--mini {
      width: 140px !important;
    }

    .el-form-item__content .el-input {
      width: 140px;
    }

    .el-form-item__label {
      width: 70px !important;
    }
  }

  :deep(.select-user) {
    border: 1px solid #eeeeee;
    border-radius: 3px;
  }
}
</style>