<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="100px" size="mini" inline>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="院级">
          <el-select clearable v-model="state.deptForm.deptFaculty" placeholder="请选择" @change="deptFacultyChange">
            <el-option v-for="(item, index) in state.deptFacultyList" :key="index" :label="item.deptName"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select clearable v-model="state.deptForm.deptGrade" placeholder="请选择" @change="deptGradeChange">
            <el-option v-for="(item, index) in state.deptGradeList" :key="index" :label="item.deptName"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="班级">
          <el-select clearable v-model="state.deptForm.deptGradeClass" placeholder="请选择">
            <el-option v-for="(item, index) in state.deptGradeClassList" :key="index" :label="item.deptName"
              :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="楼栋入住列表">
      <template #extra>
        <el-button icon="el-icon-daochu" size="mini" class="btn-purple" @click="handleExport">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading" height="55vh">
        <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
          :prop="item.prop" :width="item.width" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="180px">
          <template #default="scope">
            <el-button type="text" size="mini" @click="handleDetails(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 50, 100, 200]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>

  </kade-route-card>
</template>

<script>
import { onMounted, reactive } from "vue";
import linkageSelect from "../../../components/linkageSelect.vue";
import { buildingStayInfoList, buildingStayInfoExport } from '@/applications/eccard-dorm/api.js'
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination
} from "element-plus";
import { useStore } from 'vuex';
import { userDeptFacultyList, userDeptGradeNewList, userDeptGradeClassList } from '@/applications/eccard-sys/api.js'

const linkageData = {
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  building: { label: "楼栋", valueKey: "buildId" },
};
const column = [
  { label: "区域", prop: "areaName" },
  { label: "楼栋名称", prop: "buildName" },
  { label: "总床位数", prop: "bedCount" },
  { label: "入住人数", prop: "checkInPersonCount" },
  { label: "入住男生人数", prop: "checkInMaleCount" },
  { label: "入住女生人数", prop: "checkInFeMaleCount" },
  { label: "空闲床位数", prop: "surplusBedCount" },
  { label: "空闲男生床位数", prop: "surplusMaleBedCount" },
  { label: "空闲女生床位数", prop: "surplusFeMaleBedCount" },

];
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      deptForm: {
        deptFaculty: "",
        deptGrade: "",
        deptGradeClass: ""
      },
      deptFacultyList: [],
      deptGradeList: [],
      deptGradeClassList: [],
      data: [],
      total: 0,
      loading: false,
    });

    const getUserDeptFacultyList = async () => {
      let { data } = await userDeptFacultyList()
      state.deptFacultyList = data
    }
    const getUserDeptGradeNewList = async (params) => {
      let { data } = await userDeptGradeNewList(params)
      state.deptGradeList = data
    }
    const getUserDeptGradeClassList = async (params) => {
      let { data } = await userDeptGradeClassList(params)
      state.deptGradeClassList = data
    }



    const getList = async () => {

      let params = { ...state.form }
      if (state.deptForm.deptGradeClass) {
        params.deptPath = state.deptGradeClassList.find(item => item.id == state.deptForm.deptGradeClass).deptPath
      } else if (state.deptForm.deptGrade) {
        params.deptPath = state.deptGradeList.find(item => item.id == state.deptForm.deptGrade).deptPath
      } else if (state.deptForm.deptFaculty) {
        params.deptPath = state.deptFacultyList.find(item => item.id == state.deptForm.deptFaculty).deptPath
      }

      state.loading = true
      try {
        let { data: { dataList, totalCount } } = await buildingStayInfoList(params)
        state.data = dataList
        state.total = totalCount
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const deptFacultyChange = (val) => {
      state.deptGradeList = []
      state.deptGradeClassList = []
      state.deptForm.deptGrade = ""
      state.deptForm.deptGradeClass = ""
      if (val) {
        getUserDeptGradeNewList(val)
      }
    }
    const deptGradeChange = (val) => {
      state.deptGradeClassList = []
      state.deptForm.deptGradeClass = ""
      if (val) {
        getUserDeptGradeClassList(val)
      }
    }

    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }

    const handleDetails = async (row) => {
      await store.dispatch('app/removeTab', "roomMoveIn");
      store.dispatch('app/addTab', {
        id: "roomMoveIn",
        payload: {
          menuName: "宿舍入住查询",
          menuEnName: "roomMoveIn",
          query: row,
        },
      });
    }

    const handleSizeChange = (val) => {
      state.form.currentPage = 1,
        state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.deptForm = {
        deptFaculty: "",
        deptGrade: "",
        deptGradeClass: ""
      }
      getList()
    };
    const handleExport = async () => {
      let res = await buildingStayInfoExport(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url
      link.setAttribute("download", '楼栋入住列表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
      getUserDeptFacultyList()
    })
    return {
      state,
      deptFacultyChange,
      deptGradeChange,
      handleReset,
      handleSearch,
      linkageData,
      handleDetails,
      column,
      linkageChange,
      handleSizeChange,
      handleCurrentChange,
      handleExport
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
  width: 192px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
</style>