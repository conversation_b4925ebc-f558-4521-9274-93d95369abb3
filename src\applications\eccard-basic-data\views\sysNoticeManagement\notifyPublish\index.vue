<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="通知标题关键字搜索"></el-input>
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="state.form.msgType">
            <el-option v-for="item in messTypeList" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker v-model="state.requestDate" type="daterange" unlink-panels range-separator="~"
            start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="发布通知消息">
      <template #extra>
        <el-button size="mini" type="success" icon="el-icon-plus" @click="edit('', 'add')">发布</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
        border stripe>
        <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :label="item.label"
          :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop],) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button size="mini" type="text" @click="edit(scope.row, 'detail')">查看详情</el-button>
            <el-button size="mini" type="text" @click="edit(scope.row, 'edit')">编辑</el-button>
            <el-button size="mini" type="text" @click="del(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-notify-publish-edit :isShow="state.isEdit" :type="state.type" :data="state.rowData" @close="close"
      @edit="state.type = 'edit'" />
  </kade-route-card>
</template>
<script>
import { reactive } from "@vue/reactivity";
import { timeStr } from "@/utils/date"
import { useDict } from "@/hooks/useDict";
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElTable, ElTableColumn, ElPagination, ElButton, ElMessage } from "element-plus"
import { getSysMessageSendListPage, delSysMessage } from "@/applications/eccard-basic-data/api";
import { onMounted } from '@vue/runtime-core';
import notifyPublishEdit from "./components/edit.vue"
const column = [
  { label: "消息类型", prop: "messTypeName", },
  { label: "发布状态", prop: "status", width: "" },
  { label: "接收用户", prop: "messReceiveRoleName", width: "" },
  { label: "消息标题", prop: "messTitle", width: "" },
  { label: "发布人", prop: "publishUser", width: "" },
  { label: "发布时间", prop: "publishTime", width: "" },
]
export default {
  components: {
    ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElTable, ElTableColumn, ElPagination, ElButton, ElInput,
    "kade-notify-publish-edit": notifyPublishEdit
  },
  setup() {
    const messTypeList = useDict("SYS_MESSAGE_TYPE"); //消息类型
    const state = reactive({
      loading: false,
      isEdit: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      requestDate: [],
      dataList: [],
      total: 0,
      rowData: "",
      type: "",
    });
    const getList = async () => {
      state.loading = true
      let { data } = await getSysMessageSendListPage(state.form)
      state.dataList = data.list
      state.total = data.total
      state.loading = false
    }
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.beginDate = timeStr(val[0]);
        state.form.endDate = timeStr(val[1]);
      } else {
        delete state.form.beginDate;
        delete state.form.endDate;
      }
    }

    const edit = (row, type) => {
      state.type = type
      state.rowData = row
      state.isEdit = true
    }

    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
    }
    const search = () => {
      getList()
    }

    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageSize = val
      getList()
    }
    const del = async (row) => {
      let { code, message } = await delSysMessage(row.id)
      if (code === 0) {
        ElMessage.success(message)
        getList()
      }
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }

    onMounted(() => {
      getList()
    })
    return {
      column,
      messTypeList,
      state,
      changeDate,
      edit,
      reset,
      search,
      handlePageChange,
      handleSizeChange,
      del,
      close
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>