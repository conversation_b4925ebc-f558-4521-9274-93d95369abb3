<template>
  <el-dialog :model-value="dialogVisible" title="账户交易明细" width="1500px" :before-close="handleClose"
    :close-on-click-modal="false">
    <!-- 用户编号、用户姓名、组织机构、充值钱包、充值单号、交易来源、交易方式、对账结果， -->
    <div class="search-box" style="margin-top: 20px">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="交易时间:">
          <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="defaultTime"
            range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="交易钱包:">
          <el-select clearable v-model="state.form.walletCode" placeholder="请选择" multiple collapse-tags>
            <el-option v-for="(item, index) in state.allWalletList" :key="index" :label="item.walletName"
              :value="item.walletCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型:">
          <el-select clearable v-model="state.form.costType" placeholder="请选择" multiple collapse-tags
            @change="costTypeChange">
            <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName"
              :value="item.costCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易方式:">
          <el-select clearable v-model="state.form.tradeMode" placeholder="请选择" multiple collapse-tags>
            <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易来源:">
          <el-select clearable multiple collapse-tags v-model="state.form.tradeSource" placeholder="请选择"
            @change="tradeSourceChange">
            <el-option v-for="(item, index) in state.tradeSourceList" :key="index" :label="item.tradeName"
              :value="item.tradeCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易区域:">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="true"
            @valueChange="areaChange" />
        </el-form-item>
        <el-form-item label="交易终端:">
          <el-select clearable filterable v-model="state.form.deviceId" placeholder="请选择">
            <el-option v-for="(item, index) in state.deviceByTerminalTypeList" :key="index" :label="item.label"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button @click="getList()" icon="el-icon-sousuo" size="mini" class="shop-upload" type="primary">搜索</el-button>
      </el-form>
    </div>
    <kade-table-wrap title="账户交易明细" v-loading="state.loading">
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" highlight-current-row border stripe>
        <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :label="item.label"
          :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.datailTotal"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { onMounted, reactive, watch } from "vue";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { requestDate,defaultTime } from "@/utils/reqDefaultDate";
import {
  tradeSource,
  tradeMode,
  getWalletActiveList,
  getPersonalAccountTradeList,
  costType,
  getTerminalTypeList,
} from "@/applications/eccard-finance/api";
import { getDeviceStatus } from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "交易钱包", prop: "walletName", width: "" },
  { label: "交易前余额", prop: "tradeBeforeBalance", width: "" },
  { label: "交易金额", prop: "tradeAmount", width: "" },
  { label: "交易后余额", prop: "tradeAfterBalance", width: "" },
  { label: "交易时间", prop: "tradeDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "入账时间", prop: "createDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "交易类型", prop: "costTypeName", width: "" },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
  { label: "操作员", prop: "operatorName", width: "" },
  { label: "交易区域", prop: "areaName", width: "" },
  { label: "交易终端", prop: "deviceName", width: "" },
]
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: [String, Object],
      default: "",
    },
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 6,
        timeType: "BY_CREATE_DATE"
      },
      requestDate: requestDate(),
      detailList: [],
      datailTotal: 0,
      costTypeList: [],
      allWalletList: [],
      tradeSourceList: [],
      tradeModeList: [],
      terminalTypeList: [],
      deviceByTerminalTypeList: []
    });

    watch(
      () => props.dialogVisible,
      (val) => {
        if (val) {
          state.form = {
            currentPage: 1,
            pageSize: 6,
            timeType: "BY_CREATE_DATE",
            userCode: props.rowData.userCode
          }
          state.requestDate = requestDate()
          getList();
        }
      }
    );
    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data;
      });
    };
    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data;
      });
    };
    //获取交易来源
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data;
      });
    };
    //获取交易方式
    const getTradeModeList = (val) => {
      tradeMode({ costType: val }).then((res) => {
        state.tradeModeList = res.data;
      });
    };
    //获取终端类型
    const queryTerminalTypeList = () => {
      getTerminalTypeList().then((res) => {
        state.terminalTypeList = res.data;
      });
    };
    //获取终端设备
    const queryDeviceByTerminalTypeList = () => {
      let params = {
        areaPath: state.form.areaPath,
      };
      getDeviceStatus(params).then((res) => {
        state.deviceByTerminalTypeList = res.data.map((item) => {
          return {
            ...item,
            label: item.deviceNo + "-" + item.deviceName,
          };
        });
      });
    };
    const costTypeChange = (val) => {
      if (val && val.length) {
        getTradeModeList(val)
      } else {
        state.tradeModeList = []
      }
    }
    const tradeSourceChange = (val) => {
      console.log(val);
      state.form.terminalDeviceId = "";
      if (val.includes(3) || val.includes(4)) {
        queryDeviceByTerminalTypeList();
      }
    };

    const areaChange = (val) => {
      state.form.areaPath = val;
      state.form.terminalDeviceId = "";
      if (val && val.length) {
        queryDeviceByTerminalTypeList();
      }
    };
    const getList = async () => {
      console.log(props.rowData);
      if (state.requestDate && state.requestDate.length) {
        state.form.startTime = timeStr(state.requestDate[0]);
        state.form.endTime = timeStr(state.requestDate[1]);
      } else {
        delete state.form.startTime;
        delete state.form.endTime;
      }
      state.loading = true
      try {
        let { code, data } = await getPersonalAccountTradeList(state.form);
        if (code === 0) {
          let {
            page: { total, list },
          } = data;
          state.detailList = list;
          /*           if (state.detailList.length) {
                      state.detailList.push({
                        tradeAmount: totalAmount,
                        userCode: "合计金额",
                      });
                    } */
          state.datailTotal = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }

    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    const handleClose = () => {
      context.emit("close");
      state.form.currentPage = 1;
      state.form.pageSize = 6;
    };
    onMounted(() => {
      getCostTypeList()
      getTradeSource();
      getTradeModeList();
      queryWalletActiveList();
      queryTerminalTypeList()
      queryDeviceByTerminalTypeList()
    });
    return {
      defaultTime,
      timeStr,
      column,
      state,
      costTypeChange,
      tradeSourceChange,
      areaChange,
      getList,
      handlePageChange,
      handleSizeChange,
      handleClose,
    };
  },
};
</script>