<template>
  <kade-modal v-bind="attrs" :modelValue="modelValue" @update:modelValue="update">
    <el-tabs :model-value="'record1'" style="margin:10px 20px 10px 15px">
      <el-tab-pane :label="'全部(' + statis1 + ')'" name="record1">
        <view-add-info :type="''"></view-add-info>
      </el-tab-pane>
      <el-tab-pane :label="'待审核(' + statisType(0) + ')'" name="record2">
        <view-add-info :type="0"></view-add-info>
      </el-tab-pane>
      <el-tab-pane :label="'已通过(' + statisType(1) + ')'" name="record3">
        <view-add-info :type="1"></view-add-info>
      </el-tab-pane>
      <el-tab-pane :label="'未通过(' + statisType(2) + ')'" name="record4">
        <view-add-info :type="2"></view-add-info>
      </el-tab-pane>
    </el-tabs>
  </kade-modal>
</template>
<script>
  //getAttentionAllInfoStatis
  import { reactive ,ref , computed , watch , onMounted} from "vue";
  import {
    ElTabs,
    ElTabPane
  } from 'element-plus';
  import { getAttentionAllInfoStatis } from "@/applications/eccard-ams/api";
  import viewAddInfo from "./viewaddinfo.vue"

  // const getDefaultModel = () => ({
  //   data:{},
  // });
  export default {
    emits: ["update:modelValue", "change"],
    props: {
    },
    components: {
      ElTabs,
      ElTabPane,
      viewAddInfo
    },
    setup() {
      const formRef = ref(null);
      const btnLoading = ref(false);
      const state = reactive({
        model: [],
      });
      const getList = async () => {
        state.loading=true
        let {data}=await getAttentionAllInfoStatis(state.form)
        state.model=data;
      };
      const statis1 = computed(() => {
        let t = 0;
        state.model.forEach((item) =>{
          t += parseInt(item["COUNT"]);
        });
        return t;
      });

      const statisType = (type) => {
        let list = state.model.filter((item)=>{
          return item["exm_result"] == type;
        });
        let t = 0;
        list.forEach((item) =>{
          t += parseInt(item["COUNT"]);
        });
        return t;
      };

      watch(
      );
      onMounted(()=>{
        getList();
      })
      return {
        statis1,
        getList,
        statisType,
        formRef,
        state,
        btnLoading,
      };
    },
  };
</script>
