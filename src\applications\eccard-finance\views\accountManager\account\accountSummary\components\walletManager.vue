<template>
  <div class="box">
    <div style="text-align: center">
      <el-radio-group v-model="state.radio">
        <el-radio-button :label="index" v-for="(item, index) in personWalletInfo" :key="index">{{ item.walletName
        }}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="box-flex">
      <div class="box-main">
        <div class="wallet-box" v-loading="walletManagerLoading">
          <div class="title">{{ title }}</div>
          <div class="balance-text">余额</div>
          <div class="balance">
            <span>{{
              personWalletInfo && personWalletInfo.length
              ? personWalletInfo[state.radio].walletBalance
              : 0
            }}</span><span style="font-size: 14px; color: #666; margin-left: 10px">{{
  title == "次数钱包" ? "次" : "元"
}}</span>
          </div>
          <div class="line"></div>
          <div class="yesterday" v-if="state.radio == 0">
            <div class="yesterday-item">
              <div class="text">昨日充值</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].rechargeAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
            <div class="yesterday-item">
              <div class="text">昨日消费</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].consumeAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
            <div class="yesterday-item">
              <div class="text">昨日转出</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].transferAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
          </div>

          <div class="yesterday" v-if="state.radio == 1">
            <div class="yesterday-item">
              <div class="text">昨日收入</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].rechargeAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
            <div class="yesterday-item">
              <div class="text">昨日消费</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].consumeAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
          </div>

          <div class="yesterday" v-if="state.radio == 2">
            <div class="yesterday-item">
              <div class="text">昨日充值</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].rechargeAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
            <div class="yesterday-item">
              <div class="text">昨日消费</div>
              <div class="money">
                +{{
                  personWalletInfo && personWalletInfo.length
                  ? personWalletInfo[state.radio].consumeAmount
                  : 0
                }}<span style="font-size: 14px; color: #666; margin-left: 10px">{{ title == "次数钱包" ? "次" : "元" }}</span>
              </div>
            </div>
          </div>

          <div class="date">
            <el-icon> <alarm-clock /> </el-icon><span>钱包有效期：{{
              personWalletInfo && personWalletInfo.length
              ? timeStrDate(
                personWalletInfo[state.radio].walletValidityDate
              )
              : "-- : -- : --"
            }}</span>
          </div>
        </div>
        <div class="recharge-btn">
          <el-button
            v-if="personWalletInfo && personWalletInfo.length && (personWalletInfo[state.radio].rechargeStatus == 'TRUE')"
            @click="rechargeFnc()" size="small" type="primary">充值</el-button>
          <el-button
            v-if="personWalletInfo && personWalletInfo.length && (personWalletInfo[state.radio].clearStatus == 'TRUE') && personWalletInfo[state.radio].walletCode != 1"
            @click="clearWalletFnc()" size="small" class="btn-purple">清零</el-button>
        </div>
      </div>

      <div class="pay-box" v-if="state.recharge" v-loading="state.rechargeLoading">
        <div class="form-text">
          {{ title == "次数钱包" ? "充值次数:" : "充值金额:" }}
        </div>
        <div class="money-box">
          <input type="number" class="money-input" v-model="state.amount" max="1000" min="0"
            :placeholder="title == '次数钱包' ? '自定义次数' : '自定义金额'" />
          <div class="money-btn" @click="selectAmount(index)" :class="state.bgcolor === index ? 'bgcolor' : ''"
            v-for="(item, index) in state.priceList" :key="index">
            {{ item.actualAmount
            }}<span style="font-size: 13px; font-weight: 400">{{
  title == "次数钱包" ? "次" : "元"
}}</span>
          </div>
        </div>
        <div class="balance" v-if="title == '现金钱包'">
          充值完成金额为<span style="color: #3399ff">{{ successAmount }}</span>元
        </div>
        <div class="balance" v-if="title == '次数钱包'">
          充值完成次数为<span style="color: #3399ff">{{ successAmount }}</span>次
        </div>
        <div class="pay-type">
          <div class="form-text">充值方式：</div>
          <div class="radio-box">
            <el-radio-group v-model="state.form.tradeMode">
              <el-radio v-for="(item, index) in state.payTypeList" :key="index" :label="item.tradeMode">{{
                item.tradeModeName }}</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="explain">
          <div class="form-text">交易说明：</div>
          <el-input type="textarea" v-model="state.form.remark" placeholder="请输入交易说明" />
        </div>
        <div class="recharge">
          <el-button @click="off()" size="small" class="shop-upload">取消</el-button>
          <el-button type="primary" @click="rechargeCuccessFuc('recharge')" size="small"
            class="shop-upload">确认充值</el-button>
        </div>
      </div>

      <div class="pay-success-box" v-if="state.rechargeSuccess">
        <div class="icon">
          <img src="@/assets/paySuccess.png" />
        </div>
        <div class="tips">{{ state.tips }}成功</div>
        <div class="balance" v-if="state.tips == '充值'">
          ¥{{ state.amount }}
        </div>
        <div class="recharge">
          <el-button v-if="state.tips == '充值'" @click="
            state.recharge = true;
          state.rechargeSuccess = false;
          state.amount = '';
                                                                                                                        "
            type="primary" size="mini" class="shop-upload">继续充值</el-button>
          <el-button @click="off()" size="mini" class="shop-upload">关闭</el-button>
        </div>
      </div>

      <div class="clear-box" v-if="state.clearWallet" v-loading="state.rechargeLoading">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="清零钱包:">
            <el-radio v-model="state.clearForm.walletCode" :label="title == '次数钱包' ? 3 : (title == '补助钱包' ? 2 : 1)">{{
              title
            }}</el-radio>
          </el-form-item>
          <span class="balance">
            当前{{ title }}余额为<span style="color: #3399ff">{{
              personWalletInfo.length
              ? personWalletInfo[state.radio].walletBalance
              : 0
            }}</span>{{ title == "次数钱包" ? "次" : "元" }}
          </span>
          <el-form-item label="操作人员:">
            <el-input v-model="state.clearForm.operatorAccount" placeholder="当前登录账号"></el-input>
          </el-form-item>
        </el-form>
        <el-form inline size="small" label-width="100px">
          <el-form-item label="清零说明:">
            <el-input v-model="state.clearForm.remark" type="textarea" placeholder="请输入清零说明" />
          </el-form-item>
        </el-form>
        <div class="recharge">
          <el-button @click="off()" size="small" class="shop-upload">取消</el-button>
          <el-button type="primary" @click="rechargeCuccessFuc('clear')" size="small" class="shop-upload">确认</el-button>
          <span style="color: #999999; margin-left: 20px">点击确认后钱包余额为0</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  ElRadioGroup,
  ElRadioButton,
  ElIcon,
  ElButton,
  ElRadio,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { dateStr } from "@/utils/date.js";
import { computed, onMounted, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
import {
  getWalletRechargeModeList,
  getRechargeDenomination,
  personAccountRecharge,
  personResetWallet,
} from "@/applications/eccard-finance/api";
export default {
  components: {
    ElRadioGroup,
    ElRadioButton,
    ElIcon,
    ElButton,
    ElRadio,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup() {
    const state = reactive({
      radio: 0,
      recharge: false, //控制充值
      rechargeSuccess: false, //控制确认充值
      bgcolor: "",
      rechargeLoading: false,
      clearWallet: false, //控制清零
      tips: "",
      walletList: [], //钱包列表
      priceList: [10, 20, 30, 50, 80, 100, 200, 300],
      amount: "", //自定义金额
      payTypeList: [], //支付列表
      form: {
        rechargeAmount: "",
        remark: "",
        tradeMode: "",
        userId: "",
        walletCode: "",
        walletName: "",
      },
      clearForm: {
        userId: "",
        walletCode: "",
        remark: "",
        operatorAccount: "",
      },
    });
    const store = useStore();

    const walletManagerLoading = computed(
      () => store.state.data.walletManagerLoading
    );

    const personWalletInfo = computed(() => store.state.data.personWalletInfo);

    const title = computed(() => {
      if (store.state.data.personWalletInfo && store.state.data.personWalletInfo.length) {
        return store.state.data.personWalletInfo[state.radio].walletName;
      } else {
        return "";
      }
    });

    const successAmount = computed(() => {
      if (store.state.data.personWalletInfo.length) {
        return (
          (Number(store.state.data.personWalletInfo[state.radio].walletBalance) +
            Number(state.amount)).toFixed(2)
        );
      } else {
        return 0 + state.amount;
      }
    });

    watch(
      () => state.amount,
      (val) => {
        if (val > 1000) {
          state.amount = 1000;
        } else if (val < 0) {
          state.amount = 0;
        }
      }
    );

    const timeStrDate = computed(() => dateStr);

    //切换钱包关闭弹框
    watch(
      () => state.radio,
      () => {
        off();
        state.bgcolor = ""
        state.amount = ""
      }
    );
    //切换用户关闭弹框
    watch(
      () => store.state.data.allFrame,
      (val) => {
        if (val) {
          state.radio = 0;
          store.commit("data/updateState", { key: "allFrame", payload: false });
          off();
        }
      }
    );
    //选择充值金额
    const selectAmount = (index) => {
      state.bgcolor = index;
      state.amount = state.priceList[index].actualAmount;
    };
    // 充值
    const rechargeFnc = () => {
      getWalletRechargeModeList({
        walletCode: store.state.data.personWalletInfo[state.radio].walletCode,
        costType: 101
      }).then((res) => {
        state.payTypeList = res.data;
      });
      getRechargeDenomination().then((res) => {
        console.log(res);
        state.priceList = res.data.cashDenominationDtoList;
      });
      state.recharge = true;
    };

    //确认
    const rechargeCuccessFuc = async (val) => {
      if (val == "clear") {
        state.clearForm.userId = store.state.data.selectPerson.userId;
        state.rechargeLoading = true;
        try {
          let { code, message } = await personResetWallet(state.clearForm);
          if (code === 0) {
            ElMessage.success(message);
            state.tips = "清零";

            store.dispatch("data/queryPersonWalletInfo", {
              userId: store.state.data.selectPerson.userId,
            });
            state.recharge = false;
            state.clearWallet = false;
            state.rechargeSuccess = true;
            state.bgcolor = "";
            state.clearForm = {
              userId: "",
              walletCode: "",
              remark: "",
              operatorAccount: "",
            };
          }
          state.rechargeLoading = false;
        }
        catch {
          state.rechargeLoading = false;
        }
      } else if (val == "recharge") {
        if (!state.amount) {
          return ElMessage.error("请选择或输入充值金额！");
        }
        if (!state.form.tradeMode) {
          return ElMessage.error("请选择支付方式！");
        }
        state.form.rechargeAmount = state.amount;
        state.form.userId = store.state.data.selectPerson.userId;
        state.form.walletCode =
          store.state.data.personWalletInfo[state.radio].walletCode;
        state.form.walletName =
          store.state.data.personWalletInfo[state.radio].walletName;
        console.log(state.form);
        state.rechargeLoading = true;
        personAccountRecharge(state.form)
          .then(({ code }) => {
            setTimeout(() => {
              if (code === 0) {
                state.tips = "充值";

                store.dispatch("data/queryPersonWalletInfo", {
                  userId: store.state.data.selectPerson.userId,
                });
                state.recharge = false;
                state.clearWallet = false;

                state.rechargeSuccess = true;
                state.bgcolor = "";
                state.form = {
                  rechargeAmount: "",
                  remark: "",
                  tradeMode: "",
                  userId: "",
                  walletCode: "",
                  walletName: "",
                };
              }
              state.rechargeLoading = false;
            }, 1500);
          })
          .catch(() => {
            state.rechargeLoading = false;
          });
      }
    };
    // 清零
    const clearWalletFnc = () => {
      state.recharge = false;
      state.rechargeSuccess = false;
      state.clearWallet = true;
      state.clearForm.walletCode =
        store.state.data.personWalletInfo[state.radio].walletCode;
      console.log(state.clearForm);
    };

    //关闭所有弹框
    const off = () => {
      state.recharge = false;
      state.rechargeSuccess = false;
      state.clearWallet = false;
      state.amount = "";
      state.form = {
        rechargeAmount: "",
        remark: "",
        tradeMode: "",
        userId: "",
        walletCode: "",
        walletName: "",
      };
      state.clearForm = {
        userId: "",
        walletCode: "",
        remark: "",
      };
    };
    onMounted(() => {
      store.dispatch("data/queryPersonWalletInfo", {
        userId: store.state.data.selectPerson.userId,
      });
    });
    return {
      state,
      title,
      walletManagerLoading,
      personWalletInfo,
      timeStrDate,
      successAmount,
      selectAmount,
      rechargeFnc,
      rechargeCuccessFuc,
      clearWalletFnc,
      off,
    };
  },
};
</script>
<style lang="scss" scoped>
.box {
  min-height: 650px;

  // overflow-y: scroll;
  .box-flex {
    display: flex;

    .box-main {
      .wallet-box {
        width: 400px;
        padding: 20px;
        border: 1px solid rgba(238, 238, 238, 1);
        border-radius: 5px;
        margin-top: 20px;

        .title {
          text-align: center;
          font: 16px arial;
        }

        .balance-text {
          color: #666;
          margin: 10px 0;
        }

        .balance {
          font-family: "BebasNeue-Regular", "Bebas Neue", sans-serif;
          font-size: 40px;
        }

        .line {
          height: 1px;
          background: rgba(238, 238, 238, 1);
          margin: 10px 0;
        }

        .yesterday {
          display: flex;
          justify-content: space-around;
          align-items: center;

          .yesterday-item {
            .text {
              color: #666;
              margin: 10px 0;
            }

            .money {
              font-family: "BebasNeue-Regular", "Bebas Neue", sans-serif;
              font-size: 32px;
            }
          }
        }

        .date {
          margin-top: 10px;
          text-align: right;
          font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
          font-weight: 400;
          font-style: normal;
          color: #999999;
        }
      }

      .recharge-btn {
        margin-top: 20px;
        width: 400px;
        text-align: center;
      }
    }

    .pay-box {
      transition: all 0.4s;
      margin-top: 20px;
      margin-left: 20px;
      flex: 1;
      border: 1px solid rgba(238, 238, 238, 1);
      border-radius: 5px;
      padding: 20px;

      .form-text {
        width: 80px;
        font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
        font-weight: 400;
        font-style: normal;
        color: #000000;
      }

      .money-box {
        display: flex;
        justify-content: space-around;
        margin-top: 20px;

        .money-input {
          width: 120px;
          height: 64px;
          border: 0;
          padding: 10px;
          box-sizing: border-box;
          background: rgba(51, 153, 255, 0.0588235294117647);
          border-radius: 4px;
          margin: 0 5px;
          font-weight: 650;
          font-size: 16px;
        }

        .money-btn {
          text-align: center;
          margin: 0 5px;
          width: 108px;
          height: 64px;
          line-height: 64px;
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: 650;
          font-size: 18px;
          background: rgba(51, 153, 255, 0.0588235294117647);
          color: #1482e6;
          border-radius: 4px;
        }
      }

      .balance {
        font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 12px;
        margin: 10px 0;
      }

      .pay-type {
        display: flex;
        align-items: center;
        margin: 20px 0;
      }

      .explain {
        display: flex;
      }

      .recharge {
        text-align: center;
        margin-top: 10px;
      }
    }

    .pay-success-box {
      text-align: center;
      transition: all 0.4s;
      margin-top: 20px;
      margin-left: 20px;
      flex: 1;
      border: 1px solid rgba(238, 238, 238, 1);
      border-radius: 5px;
      padding: 20px;

      .icon {
        margin: 20px auto;
        height: 40px;
        width: 40px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .tips {
        margin-bottom: 30px;
      }

      .balance {
        margin-bottom: 50px;
        font-family: "BebasNeue-Regular", "Bebas Neue", sans-serif;
        font-size: 40px;
      }
    }

    .clear-box {
      transition: all 0.4s;
      margin-top: 20px;
      margin-left: 20px;
      flex: 1;
      border: 1px solid rgba(238, 238, 238, 1);
      border-radius: 5px;
      padding: 20px;

      .balance {
        line-height: 32px;
        margin: 0 30px;
        font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 12px;
        color: #999999;
      }

      .el-radio {
        margin-left: 0;
      }

      .el-textarea__inner {
        width: 800px;
      }

      .recharge {
        text-align: center;
      }
    }
  }
}

input:focus {
  outline: none;
}

.bgcolor {
  background: rgba(51, 153, 255, 1) !important;
  color: #fff !important;
}
</style>
