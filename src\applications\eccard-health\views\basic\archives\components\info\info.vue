<template>
  <el-form label-width="100px" inline size="mini" ref="formRef" :model="state.form" :rules="rules">
    <kade-route-card>
      <template #header>基础健康信息</template>
      <el-form-item label="姓名">
        <el-input :model-value="rowData.userName" readonly />
      </el-form-item>
      <el-form-item label="学号">
        <el-input :model-value="rowData.userCode" readonly />
      </el-form-item>
      <el-form-item label="班级">
        <el-input :model-value="rowData.deptName" readonly />
      </el-form-item>
      <!--       <el-form-item label="健康状态">
        <el-input :model-value="rowData.deviceName" clearable placeholder="请输入" />
      </el-form-item> -->
      <el-form-item label="当前状态">
        <el-input v-if="isEdit" v-model="state.form.currentState" clearable placeholder="请输入" />
        <el-input v-else :model-value="rowData.currentState" readonly />

      </el-form-item>

    </kade-route-card>
    <kade-route-card style="margin-top:20px">
      <template #header>身体信息</template>
      <el-form-item label="身高">
        <el-input v-if="isEdit" v-model="state.form.height" clearable placeholder="请输入" />
        <el-input v-else :model-value="rowData.height" readonly />
      </el-form-item>
      <el-form-item label="体重">
        <el-input v-if="isEdit" v-model="state.form.weight" clearable placeholder="请输入" />
        <el-input v-else :model-value="rowData.weight" readonly />
      </el-form-item>
      <el-form-item label="视力">
        <el-input v-if="isEdit" v-model="state.form.vision" clearable placeholder="请输入" />
        <el-input v-else :model-value="rowData.vision" readonly />
      </el-form-item>

    </kade-route-card>
  </el-form>
  <div class="edit">
    <el-button v-if="!isEdit" type="primary" @click="handleEdit()" size="mini">编辑</el-button>
    <el-button v-if="isEdit" @click="handleCancel()" size="mini">取消</el-button>
    <el-button v-if="isEdit" type="primary" @click="handleSave()" size="mini">保存</el-button>
  </div>
</template>
 
<script> 
import { reactive, ref } from "vue"
import {
  ElButton, ElForm, ElFormItem, ElInput, ElMessage
} from "element-plus";
import { healthRecordsAdd, healthRecordsUpdate } from "@/applications/eccard-health/api/archives.js";

export default {
  components: {
    ElButton, ElForm, ElFormItem, ElInput
  },
  props: {
    rowData: {
      types: Object,
      default: null
    },
  },
  setup(props, context) {
    const formRef = ref(null)
    const isEdit = ref(false)
    const rules = {
      /* currentState: [
        { required: true, message: "请输入当前状态", trigger: "blur", },
      ], */
    }
    const state = reactive({
      form: {}
    })
    const handleEdit = () => {
      state.form = { ...props.rowData }
      isEdit.value = true
    }
    const handleCancel = () => {
      state.form = { ...props.rowData }
      isEdit.value = false
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          let fn = state.form.recordId ? healthRecordsUpdate : healthRecordsAdd
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("success")
              isEdit.value = false
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    return {
      formRef,
      isEdit,
      rules,
      state,
      handleEdit,
      handleCancel,
      handleSave
    }
  }
}
</script>
<style lang="scss" scoped>
.padding-box {
  height: 100%;

  .flex-box {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.edit {
  text-align: center;
  margin-top: 20px;
}
</style>