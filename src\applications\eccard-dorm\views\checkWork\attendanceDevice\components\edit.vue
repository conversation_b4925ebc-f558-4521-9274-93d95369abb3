<template>
  <el-dialog
    :model-value="dialogVisible"
    title="添加考勤设备"
    width="960px"
    :before-close="handleClose"
  >
    <el-form inline label-width="90px" size="mini">
      <el-form-item label="设备类型">
         <el-select clearable placeholder="全部" v-model="state.form.model">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="所属权限">
        <el-select clearable placeholder="全部" v-model="state.form.model">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="关键字">
        <el-input placeholder="设备机号或设备名称" v-model="state.form.key"></el-input>
      </el-form-item>
      <el-form-item>
      <el-button icon="el-icon-search" type="primary" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <div class="table-box">
      <el-table border>
        <el-table-column label="设备型号" align="center"></el-table-column>
        <el-table-column label="所属区域" align="center"></el-table-column>
        <el-table-column label="设备机号" align="center"></el-table-column>
        <el-table-column label="设备名称" align="center"></el-table-column>
        <el-table-column label="设备类型" align="center"></el-table-column>
        <el-table-column label="设备位置" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50, 100, 500]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="100"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <div class="footer-box">
        <div class="text-box">绑定单元</div>
        <el-divider></el-divider>
        <el-form inline label-width="90px" size="mini">
            <el-form-item label="所属区域：">
                <el-select clearable placeholder="全部" v-model="state.form.model">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
            </el-form-item>
            <el-form-item label="所属楼栋：">
                <el-select clearable placeholder="全部" v-model="state.form.model">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
            </el-form-item>
            <el-form-item label="所属单元：">
                <el-select clearable placeholder="全部" v-model="state.form.model">
            <el-option v-for="(item,index) in 10" :key="index" :label="item" :value="index"></el-option>
          </el-select>
            </el-form-item>
        </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose"  size="mini">取消</el-button>
        <el-button type="primary" @click="handleClose" size="mini"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDivider,
} from "element-plus";
import { reactive } from "@vue/reactivity";
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDivider,
  },
  setup(props, context) {
    const state = reactive({
      form:{},
    });
    const handleClose = () => {
      context.emit("close", false);
    };
    const search=()=>{};
    const handleSizeChange = (val) => {
      console.log(val);
    };
    const handleCurrentChange = (val) => {
      console.log(val);
    };
    return {
      state,
      search,
      handleClose,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 10px;
}
.table-box {
  border: 1px solid #eeeeee;
  border-radius: 0 0 8px 8px;
  .pagination {
    margin: 10px;
  }
}
.footer-box{
    margin-top: 10px;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    .text-box{
        margin: 15px;
    }
    .el-form{
        margin-top: 20px;
    }
}
</style>