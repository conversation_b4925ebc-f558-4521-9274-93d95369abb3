<template>
    <kade-route-card>
        <kade-table-filter @search="handleSearch" @reset="handleReset">
            <el-form inline label-width="90px" size="mini">
                <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
            </el-form>
        </kade-table-filter>
        <div class="total-box">
            <div class="total-item">
                <span>水费总余额：{{ state.totalData.totalWaterBalance }}</span>
                <div></div>
            </div>
            <div>
                <span>电费总余额：{{ state.totalData.totalElectricBalance }}</span>
                <div></div>
            </div>
        </div>
        <kade-table-wrap title="记录列表">
            <el-table :data="state.dataList" border>
                <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
                    :prop="item.prop" :width="item.width" align="center">
                    <template #default="scope" v-if="item.isDate">
                        {{ timeFilter(scope.row[item.prop]) }}
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination background :current-page="state.form.currentPage"
                    layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]"
                    :total="state.total" :page-size="state.form.pageSize" @current-change="handlePageChange"
                    @size-change="handleSizeChange">
                </el-pagination>

                <!--                 <el-pagination background :current-page="state.form.currentPage"
                    layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]"
                    :total="state.total" :page-size="state.form.pageSize" @current-change="handlePageChange"
                    @size-change="handleSizeChange">
                </el-pagination> -->
            </div>
        </kade-table-wrap>
    </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
    ElForm,
    ElTable,
    ElTableColumn,
    ElPagination,
} from "element-plus";
import { onMounted } from "@vue/runtime-core";
import { getHydropowerBalance, getBalanceTotal } from '@/applications/eccard-dorm/api.js'

import linkageSelect from "../../../components/linkageSelect.vue";
const linkageData = {
    area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
    buildingType: { label: "楼栋类型", valueKey: "buildType" },
    building: { label: "楼栋", valueKey: "buildId" },
    unit: { label: "单元", valueKey: "unitNum" },
    floor: { label: "楼层", valueKey: "floorNum" },
    room: { label: "房间", valueKey: "roomId" },
};

const column = [
    { label: "楼栋", prop: "buildName" },
    { label: "房间", prop: "roomName", width: "300px" },
    { label: "水费余额", prop: "waterBalance" },
    { label: "水费余额更新时间", prop: "waterLastModifyTime", isDate: true },
    { label: "电费余额（度）", prop: "electricBalance" },
    { label: "电费余额更新时间", prop: "electricLastModifyTime", isDate: true },
];

export default {
    components: {
        ElForm,
        ElTable,
        ElTableColumn,
        ElPagination,
        "kade-linkage-select": linkageSelect,
    },
    setup() {
        const state = reactive({
            loading: false,
            form: {
                pageSize: 10,
                currentPage: 1,
            },
            dataList: [],
            total: 0,
            totalData: {}
        });
        const getList = async () => {
            state.loading = true
            let { data: { list, total } } = await getHydropowerBalance(state.form)
            state.dataList = list
            state.total = total
            getBalance()
            state.loading = false
        };

        const getBalance = async () => {
            let { data } = await getBalanceTotal(state.form)
            state.totalData = data
        }
        const linkageChange = (val) => {
            state.form = { ...state.form, ...val }
        }
        const handleSearch = () => {
            getList();
        };
        const handleReset = () => {
            state.form = {
                currentPage: 1,
                pageSize: 10,
            };
        };
        const handleSizeChange = (val) => {
            state.form.currentPage = 1;
            state.form.pageSize = val;
            getList;
        };
        const handlePageChange = (val) => {
            state.form.currentPage = val;
            getList();
        };
        onMounted(() => {
            getList();
        });
        return {
            linkageData,
            state,
            column,
            linkageChange,
            handleSearch,
            handleReset,
            handlePageChange,
            handleSizeChange,
        };
    },
};
</script>

<style lang="scss" scoped>
.total-box {
    display: flex;
    align-items: center;
    padding: 0 0 15px 10px;

    .total-item {
        margin-right: 60px;
    }
}

:deep(.el-input--mini .el-input__inner) {
    width: 182px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
    width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
    width: 46px;
}
</style>