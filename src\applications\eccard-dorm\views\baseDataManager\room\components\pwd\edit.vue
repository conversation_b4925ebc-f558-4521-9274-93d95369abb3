<template>
  <el-dialog :model-value="modelValue" :title="rowData.roomGoodsId?'编辑密码':'新增密码'" width="500px" :before-close="handleClose">
    <el-form label-width="150px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <el-form-item label="密码:" prop="pwd">
        <el-input placeholder="请输入" v-model="state.form.pwd" show-password></el-input>
      </el-form-item>
    </el-form>
    <template #footer v-if="editType!=='details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" :loading="state.loading" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElMessage } from "element-plus"
import { reactive, watch, ref, nextTick } from 'vue'
import { roomPwdUpdate } from "@/applications/eccard-dorm/api";
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {
        resourceUrl: ""
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = { ...props.rowData }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      pwd: [
        {
          required: true,
          message: "请输入密码",
        },
        {
          pattern: /^\d{6,10}$/,
          message: '密码为6-10为数字',
          trigger: 'blur'
        }
      ],
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          try {
            let { code, message } = await roomPwdUpdate(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      rules,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
:deep(.el-input-number) {
  width: 192px;
}
:deep(.el-input) {
  width: 192px;
}
:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;
  .el-upload {
    width: 100px;
    height: 100px;
  }
  .element-icons {
    font-size: 40px !important;
  }
}
</style>