<template>
  <div class="login-details">
    <el-dialog :model-value="isShow" :title="rowData.holidayId ? '修改配置名称' : '新增配置'" width="800px"
      :before-close="handleClose">
      <el-form size="mini" label-width="120px" ref="formRef" :model="state.form" :rules="state.rules"
        label-position="top">
        <el-row>
          <el-col :span="24" v-if="!rowData.holidayId">
            <el-form-item label="">
              <el-tag style="margin-right: 20px;" v-for="item in tagList" :key="item.label" :color="item.color"
                effect="dark">
                {{ item.label }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!rowData.holidayId">
            <el-form-item label="开始日期:" prop="startDate">
              <el-calendar v-model="state.form.startDate">
                <template #dateCell="{ data }">
                  <div class="day" v-if="data.isSelected" :style="{ backgroundColor: 'rgb(18, 174, 10)' }">
                    {{ dateFnc(data.day).day }}
                  </div>
                  <div class="day" v-else :style="{ backgroundColor: dateFnc(data.day).color }">
                    {{ dateFnc(data.day).day }}
                  </div>
                </template>
              </el-calendar>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!rowData.holidayId">
            <el-form-item label="结束日期:" prop="endDate">
              <el-calendar v-model="state.form.endDate">
                <template #dateCell="{ data }">
                  <div class="day" v-if="data.isSelected" :style="{ backgroundColor: 'rgb(18, 174, 10)' }">
                    {{ dateFnc(data.day).day }}
                  </div>
                  <div class="day" v-else :style="{ backgroundColor: dateFnc(data.day).color }">
                    {{ dateFnc(data.day).day }}
                  </div>
                </template>
              </el-calendar>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="配置名称:" prop="holidayName">
              <el-input v-model="state.form.holidayName" placeholder="请输入名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="!rowData.holidayId">
            <el-form-item label="配置类型:" prop="holidayType">
              <el-select clearable placeholder="请选择类型" v-model="state.form.holidayType" style="width: 100%;">
                <el-option v-for="item in typeList" :label="item.label" :value="item.value" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose" size="small">取消</el-button>
          <el-button type="primary" @click="handleSubmit" size="small" :loading="state.loading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { nextTick, reactive, ref, watch } from "vue";
import {
  ElTag,
  ElButton,
  ElDialog,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElMessage,
  ElSelect,
  ElOption,
  ElInput,
  ElCalendar
} from "element-plus";
import { dateStr } from "@/utils/date.js"
import { attentionholidayAdd, attentionholidayEdit } from "@/applications/eccard-ams/api.js";
const tagList = [
  { label: "节假日", color: "#e91e63" },
  { label: "工作日", color: "#2196f3" },
  { label: "周末", color: "#cef592" },
]
export default {
  components: {
    ElDialog,
    ElTag,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElButton,
    ElSelect,
    ElOption,
    ElInput,
    ElCalendar
  },
  props: {
    isShow: {
      types: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => { },
    },
    everyDayList: {
      type: Array,
      default: () => []
    }
  },
  setup(props, context) {
    const formRef = ref(null);
    const typeList = [
      { label: "节假日", value: "节假日" },
      { label: "工作日", value: "工作日" },
      { label: "周末", value: "周末" },
    ]
    const state = reactive({
      loading: false,
      rules: {
        startDate: [{ required: true, message: "请选择开始日期", trigger: "change" }],
        endDate: [{ required: true, message: "请选择结束日期", trigger: "change" }],
        holidayName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        holidayType: [{ required: true, message: "请选择类型", trigger: "change" }],
      },
      form: {},
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = {
            ...props.rowData,
          };
          nextTick(() => {
            formRef.value.clearValidate()
          })
        }
      }
    );

    const dateFnc = (date) => {
      console.log(props.everyDayList, new Date(date).getTime());
      let day = new Date(date).getDate()
      let timeStamp = new Date(date).getTime()
      let data = props.everyDayList.find(item => item.timeStamp === timeStamp)
      let str = "日一二三四五六".charAt(new Date(date).getDay());
      let color = ""
      if (data) {
        if (data.type == "节假日") {
          color = "#e91e63"
        } else if (data.type == "工作日") {
          color = "#2196f3"
        } else {
          color = "#cef592"
        }
      } else if (str == '日' || str == '六') {
        color = "#cef592"
      }
      return {
        color,
        day,
      }
    }
    const handleClose = () => {
      context.emit("close", false);
    };
    //新增提交
    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          if (new Date(state.form.startDate).getTime() > new Date(state.form.endDate).getTime()) {
            return ElMessage.error("开始日期不能大于结束日期")
          }
          state.loading = true;
          let params = { ...state.form }

          params.startDate = dateStr(params.startDate)
          params.endDate = dateStr(params.endDate)
          console.log(params);
          try {
            let fn = state.form.holidayId ? attentionholidayEdit : attentionholidayAdd
            let { code, message } = await fn(params);
            if (code === 0) {
              ElMessage.success(message);
              context.emit("close", true);
            }
            state.loading = false;
          } catch {
            state.loading = false;
          }
        }
      });
    };
    return {
      tagList,
      formRef,
      typeList,
      state,
      dateFnc,
      handleClose,
      handleSubmit,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

::v-deep(.el-calendar-day) {
  height: 40px;
  padding: 0
}

:deep(.is-selected) {
  background-color: rgb(18, 174, 10) !important;
}

:deep(.el-tag--dark) {
  border-color: #fff;
}

.day {
  height: 100%;
  text-align: center;
  line-height: 40px
}
</style>