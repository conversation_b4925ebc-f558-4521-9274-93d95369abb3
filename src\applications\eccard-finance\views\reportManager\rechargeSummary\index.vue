<template>
  <!-- 充值汇总报表 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="汇总方式:">
          <el-select v-model="state.form.settlementMethod" placeholder="请选择" size="small">
            <el-option v-for="(item, index) in sumMethodList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="汇总月份:" v-if="state.form.settlementMethod == 'DAY'">
          <el-date-picker v-model="state.month" type="month" range-separator="至" placeholder="请选择月份" />
        </el-form-item>
        <el-form-item label="汇总月份:" v-if="state.form.settlementMethod == 'MONTH'">
          <el-date-picker v-model="state.monthList" type="monthrange" range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="充值汇总报表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>·
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
} from "element-plus";
import { downloadXlsx } from "@/utils"
import { timeStr, monthStr, getMonthFirst, getMonthLast } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { useDict } from "@/hooks/useDict.js";
import {
  rechargeSum,
  exportRechargeSumList,
} from "@/applications/eccard-finance/api";
import { onMounted, } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "汇总部门", prop: "deptName", width: "" },
  { label: "汇总时间", prop: "tradeDate", width: "" },
  { label: "现金充值笔数", prop: "rechargeCount", width: "" },
  { label: "现金充值金额", prop: "rechargeAmount", width: "" },
  { label: "补助充值笔数", prop: "subsidyCount", width: "" },
  { label: "补助充值金额", prop: "subsidyAmount", width: "" },
  { label: "合计笔数", prop: "totalCount", width: "" },
  { label: "合计金额", prop: "totalAmount", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    ElDatePicker,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const sumMethodList = useDict("CONSUMPTION_SUMMARY_TYPE");
    const state = reactive({
      loading: false,
      column,
      monthList: [
        new Date(),
        new Date()
      ],
      month: new Date(),
      form: {
        pageNum: 1,
        pageSize: 10,
        settlementMethod: "MONTH",
      },
      detailList: [],
      total: 0,
      systemUserList: [],
    });

    const getParams = () => {
      let params = { ...state.form }
      if (params.settlementMethod === 'MONTH') {
        if (state.monthList && state.monthList.length) {
          params.startMonth = monthStr(state.monthList[0])
          params.endMonth = monthStr(state.monthList[1])
        } else {
          ElMessage.error("请选择汇总月份")
          return false
        }
      } else if (params.settlementMethod === 'DAY') {
        if (state.month) {
          params.startDay = getMonthFirst(state.month)
          params.endDay = getMonthLast(state.month)
        } else {
          ElMessage.error("请选择汇总月份")
          return false
        }
      }
      return params
    }


    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data: { list, total } } = await rechargeSum(params);
        if (code === 0) {
          state.detailList = list;
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };

    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await exportRechargeSumList(params);
        downloadXlsx(res, '充值汇总报表.xlsx')
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
        settlementMethod: "MONTH"
      };
      state.monthList = [
        new Date(),
        new Date()
      ]
    };
    onMounted(() => {
      // getList();
    });
    return {
      column,
      sumMethodList,
      state,
      timeStr,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>