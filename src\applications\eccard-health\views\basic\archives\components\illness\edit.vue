<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" :title="state.form.id?'编辑':'新增'" width="600px" :before-close="handleClose">
    <div class="padding-box">
      <el-form label-width="100px" size="mini" ref="formRef" :model="state.form" :rules="rules">
        <el-form-item label="类型">
          <el-radio-group v-model="state.form.type">
            <el-radio label="病史">病史</el-radio>
            <el-radio label="手术史">手术史</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="state.form.pastMedicalHistory" type="textarea" :rows="6" clearable placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary"  @click="handleSave()" size="mini">确
          认</el-button>
        <el-button @click="handleClose" size="mini">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio, ElButton, ElMessage } from "element-plus"
import { reactive, ref, watch } from 'vue'
import { healthMedicalHistoryAdd, healthMedicalHistoryUpdate } from "@/applications/eccard-health/api/archives.js";


export default {
  components: {
    ElDialog,
    ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio,
    ElButton,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      form: {},
    });
    watch(() => props.modelValue, async val => {
      if (val) {
        state.form = { ...props.rowData }
      }
    })
    const handleSave = async () => {
      state.loading = true
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = state.form.id ? healthMedicalHistoryUpdate : healthMedicalHistoryAdd
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("success")
              handleClose()
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      formRef,
      state,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  margin: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.img-box {
  display: flex;

  img {
    width: 100px;
    height: 100px;
    margin-right: 10px;
  }
}
</style>