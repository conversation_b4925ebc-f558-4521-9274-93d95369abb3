<template>
  <kade-route-card>
    <kade-table-filter @search="getList" @reset="reset">
      <el-form label-width="100px" inline size="mini">
        <el-form-item label="手机号">
          <el-input v-model="state.form.keyWord" placeholder="手机号" clearable></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="手机号列表">
      <template #extra>
        <el-button @click="handleEdit({})" size="mini" type="success">新增</el-button>
        <el-button @click="state.isImport=true" size="mini" type="primary">导入</el-button>
      </template>
      <el-table v-loading="state.loading" height="55vh" :data="state.dataList" border>
        <el-table-column label="手机号" prop="tel" align="center"></el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)" type="text" size="mini">编辑</el-button>
            <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-phone-edit :isShow="state.isEdit" :rowData="state.rowData" @close="close" />
    <kade-phone-import v-model="state.isImport" @success="getList" />
  </kade-route-card>
</template>
<script>
import { ref, reactive, onMounted } from "vue";
import {
  baseTelLibPage,
  baseTelLibDel,
} from "@/applications/eccard-basic-data/api";
import stationEdit from "./components/edit.vue";
import importPhone from "./components/import.vue"
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
  ElMessageBox,
  ElMessage,
} from "element-plus";
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    "kade-phone-edit": stationEdit,
    "kade-phone-import": importPhone,
  },
  setup() {
    const showCreateModal = ref(false);
    const state = reactive({
      loading: false,
      isEdit: false,
      isImport: false,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
      rowData: "",
    });
    const getList = async () => {
      state.loading = true;
      let {
        data: { list, total },
      } = await baseTelLibPage(state.form);
      state.dataList = list;
      state.total = total;
      state.loading = false;
    };
    const handleEdit = (row) => {
      state.rowData = row;
      state.isEdit = true;
    };
    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await baseTelLibDel(row.id);
          if (code === 0) {
            ElMessage.success(message);
            getList();
          }
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      }
    }
    const close = (val) => {
      if (val) {
        getList();
      }
      state.isEdit = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      showCreateModal,
      state,
      getList,
      handleEdit,
      handleDel,
      handleCurrentChange,
      handleSizeChange,
      reset,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
.role {
  width: 100%;
  height: 100%;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>