<template>
  <el-dialog append-to-body="true" width="1200px" :model-value="modelValue" :title="'选择人员'"  :before-close="update" :close-on-click-modal="false" :modal="true">
    <p style="margin:10px auto 0px 10px;">
      <el-form inline size="mini">
        <el-form-item label="所属考勤组">
          <el-select v-model="state.form.atgId" :clearable="true">
            <el-option v-for="(item,index) in state.atgIdList" :key="index" :label="item.atgName" :value="item.atgId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
        <el-form-item label="身份类别">
          <el-select v-model="state.form.userIde" :clearable="true">
            <el-option v-for="(item,index) in state.ideTypeList" :key="index" :label="item.roleName" :value="item.roleName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字" :clearable="true">
          <el-input v-model="state.form.keyWord" placeholder="用户编号或名称搜索"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </p>
    <span v-for="(sitem) in getListKeys()" :key="sitem + 'ddd'"><el-tag class="ml-2" type="success" v-for="(item , index) in state.selectData[sitem]" :key="index" closable @close="closeselected(item)">{{item.userName}}</el-tag></span>
    <el-table style="width: 100%"  size="small" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" @select="handleSelectionChange"  @select-all="handleSelectAll" border stripe>
      <el-table-column type="selection" width="55" />
      <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
      <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
      <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
      <el-table-column label="性别" prop="userSex" align="center">
        <template #default="scope">
          {{ scope.row.userSex == 'SEX_MALE' ? '男' : '女' }}
        </template>
      </el-table-column>
      <el-table-column label="手机号码" prop="userTel" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.userTel ?  scope.row.userTel.toString().substring(0 , 3) + '****' + scope.row.userTel.toString().substring(7) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="人员状态" prop="userPostStatus" align="center" show-overflow-tooltip>
        <template #default="scope">
          <el-tag size="small" :type=" scope.row.userPostStatus === 'STATE_IN' ? 'primary' : 'warning' ">{{
            dictionaryFilter(scope.row.userPostStatus) }}</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <p style="text-align: center;margin: 20px">
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
    </p>
  </el-dialog>

</template>
<script>
  import { reactive, watch , onMounted , nextTick , ref} from "vue";
  // import {timeStr} from "@/utils/date"
  // import { useDict } from "@/hooks/useDict";
  import { getAttentionUserAttendPage , batchBindUserInfo , getAttentionGroupInfoPageAll , getIdeTypeList } from "@/applications/eccard-ams/api";
  import deptSelectTree from "@/components/tree/deptSelectTree.vue";
  // import userEdit from "@/applications/eccard-ams/views/userattend/components/edit.vue"

  import {
    ElSelect,
    ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessage,
    ElMessageBox,
    ElDialog,
    ElTag,
  } from 'element-plus';

  export default {
    components: {
      "kade-dept-select-tree": deptSelectTree,
      // userEdit,
      ElSelect,
      ElOption,
      ElInput,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      ElDialog,
      ElTag,
    },
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      type:{
        type: String,
        default: "",
      },
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    setup(props , context) {
      const multipleTable = ref(null);
      // const ideTypeList = useDict("ATTENTION_USER_IDE");
      const state = reactive({
        loading: false,
        isEdit:false,
        isFresh:'0',
        isEdit1:false,
        form:{
          atgId:'',
          degId:'',
          userDisType:1,
          orgId:'',
          keyWord:'',
          currentPage:1,
          pageNum:1,
          pageSize:10
        },
        ideTypeList:[],
        atgIdList:[],
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        selectData:{},
      });

      //分页
      const getList=async ()=>{
        state.form["selectedFlag"] = '-1';
        state.loading=true
        let {data}=await getAttentionUserAttendPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false

        nextTick(() => {
          state.selectData[state.form.pageNum].forEach((item)=>{
            multipleTable.value.toggleRowSelection(state.dataList.filter((sitem)=>{return sitem.userId == item.userId})[0], true)
          });
        })
      }
      const add=(row,type)=>{
        state.type=type
        state.rowData = props.data;
        state.edit=true
      }
      const closeselected = (val)=>{
        let keys = getListKeys();
        keys.forEach((keyitem)=>{
          let li = state.selectData[keyitem].filter((item) => {
            return item.userId != val.userId
          });
          state.selectData[keyitem] = li;
        });
        nextTick(() => {
          state.selectData[state.form.pageNum].forEach((item)=>{
            multipleTable.value.toggleRowSelection(state.dataList.filter((sitem)=>{return sitem.userId == item.userId})[0], true)
          });
        })
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.edit1=true
      }
      const reset=()=>{
        state.form={
          atgId:'',
          degId:props.data.degId,
          userDisType:1,
          orgId:'',
          keyWord:'',
          currentPage:1,
          pageSize:10
        };
        state.form.pageNum=1;
        state.selectData = {};
        getList();
      }
      const search=()=>{
        state.form.pageNum=1;
        state.selectData = {};
        getList()
      }
      const getListKeys = ()=>{
        let list = new Array();
        for(let key in state.selectData){
          list.push(key);
        }
        return list;
      }
      const getListValues = ()=>{
        let list = new Array();
        for(let key in state.selectData){
          list = list.concat(state.selectData[key]);
        }
        return list;
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSelectionChange = (selection)=>{
        if(state.form.pageNum && selection) {
          state.selectData[state.form.pageNum] = selection;
        }
      }
      const handleSelectAll = (selection)=>{
        if(state.form.pageNum && selection) {
          state.selectData[state.form.pageNum] = selection;
        }
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const cancel = () => {
        context.emit("close", state.isFresh);
      };
      const update = (v) => {
        context.emit("update:modelValue", v);
      };
      const submit = () => {
        let list1 = getListValues();
        if(list1.length == 0){
          return ElMessage.error('请先选择授权人员信息!')
        }
        ElMessageBox.confirm(`确定授权选择的人员？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let param = list1.map((item)=>item.userId).join(',');
          let param1 = { devIds : param , atgId : state.form.degId }
          let {code,message} = await batchBindUserInfo(param1);
          if(code===0){
            state.isFresh = '1';
            ElMessage.success(message)
            close()
          }
        });
      };

      const close=(val)=>{
        if(val){
          getList()
        }
        context.emit("close", state.isFresh);
      }

      onMounted(async ()=>{
        let {data} = await getAttentionGroupInfoPageAll('');
        state.atgIdList = data;
        if(props.data){
          state.degId = props.data.degId;
        }

        let objs = await getIdeTypeList();
        state.ideTypeList = objs.data;

        getList()
      })

      watch(
        () => props.modelValue,
        (n) => {
          if (n) {
            state.form.degId = props.data.degId;
            state.selectData = {};
            state.form.pageNum = 1;
            getList();
          }
        }
      );

      return {
        state,
        // ideTypeList,
        add,
        edit,
        reset,
        search,
        handleSelectionChange,
        closeselected,
        multipleTable,
        handleSelectAll,
        getListKeys,
        getListValues,
        cancel,
        submit,
        handlePageChange,
        handleSizeChange,
        close,
        update,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
