<template>
  <el-dialog :model-value="isShow" :title="title" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="type!=='detail'&&rules" :model="state.form">
        <el-row>
          <el-col :span="8">
            <el-form-item label="消息类型:" prop="messType">
              <el-select :disabled="type=='detail'" v-model="state.form.messType" placeholder="请选择">
                <el-option v-for="item in messTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
<!--           <el-col :span="6">
            <el-form-item label="接收用户:" prop="roleIds">
              <el-select :disabled="type=='detail'" v-model="state.form.roleIds" multiple collapse-tags placeholder="请选择">
                <el-option v-for="item in state.roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label=" 发布人:" prop="publishUser">
              <el-input :disabled="type=='detail'" placeholder="请输入" v-model="state.form.publishUser"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布时间:" prop="publishTime">
              <el-date-picker :disabled="type=='detail'" v-model="state.form.publishTime" type="datetime" placeholder="请选择时间"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否立即发布:" prop="publishNow">
          <el-radio-group :disabled="type=='detail'" v-model="state.form.publishNow">
            <el-radio label="TRUE">是</el-radio>
            <el-radio label="FALSE">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="消息标题:" prop="messTitle">
          <el-input :disabled="type=='detail'" placeholder="请输入不超过30字" v-model="state.form.messTitle" :maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="消息内容:" prop="messContent">
          <div v-if="type=='detail'" class="news-text" v-html="state.form.messContent"></div>
          <kade-wangeditor v-else :htmlData="state.form.messContent" @change="(val) => (state.form.messContent = val)" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="type=='detail'" type="primary" @click="edit()" size="mini">编辑</el-button>
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button v-if="type!=='detail'" type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { computed, onMounted, reactive, ref, watch,nextTick } from "vue";
import { useDict } from "@/hooks/useDict";
import { timeStr } from "@/utils/date.js";
import { getRolelist,getSysMessageDetails,addSysMessage,editSysMessage, } from "@/applications/eccard-basic-data/api";
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElRadioGroup,
  ElRadio,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElMessage,
} from "element-plus";
import wangeditor from "@/components/wangeditor.vue";

const rules = {
  messType: [
    {
      required: true,
      message: "请选择消息类型",
      trigger: "change",
    },
  ],
  roleIds: [
    {
      type:"array",
      required: true,
      message: "请选择接收用户",
      trigger: "change",
    },
  ],
  publishUser: [
    {
      required: true,
      message: "请输入发布人",
      trigger: "blur",
    },
  ],
  publishTime: [
    {
      required: true,
      message: "请输入发布时间",
      trigger: "blur",
    },
  ],
  publishNow: [
    {
      required: true,
      message: "请选择是否立即发布",
      trigger: "change",
    },
  ],
  messTitle: [
    {
      required: true,
      message: "请输入消息标题",
      trigger: "blur",
    },
  ],
  messContent: [
    {
      required: true,
      message: "请输入消息内容",
      trigger: "blur",
    },
  ],
};

export default {
  components: {
    ElDialog,
    ElRow,
    ElCol,
    ElButton,
    ElRadioGroup,
    ElRadio,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    "kade-wangeditor": wangeditor,
  },
  props:{
    isShow:{
      type:Boolean,
      default:false
    },
    data:{
      type:Object,
      default:null,
    },
    type:{
      type:String,
      default:"",
    },
  },
  setup(props,context) {
    const formDom = ref(null);
    const messTypeList = useDict("SYS_MESSAGE_TYPE"); //消息类型
    const state = reactive({
      isTailor: false,
      imgFile: "",
      form: {
        roleIds:[],
        publishNow:'TRUE'
      },
      roleList:[]
    });

    const title=computed(()=>{
      if(props.type=="add"){
        return "发布消息通知"
      }else if(props.type=="edit"){
        return "编辑消息通知"
      }else {
        return "消息通知详情"
      }
    })

    watch(()=>props.isShow,async val=>{
      nextTick(()=>{
        formDom.value.clearValidate()
      })
      if(val&&props.type!=="add"){
        let {data}=await getSysMessageDetails(props.data.id)
        state.form=data
        state.form.roleIds=data.messReceiveRoleId.split(",").map(item=>Number(item))
      }else{
        state.form={
          roleIds:[],
          publishNow:'TRUE'
        }
      }
    })

    const queryRolelist=async ()=>{
      let {data,code}=await getRolelist()
      if(code===0){
        state.roleList=data
      }
    }

    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let fn=props.type=='add'?addSysMessage:editSysMessage
          let params = { ...state.form };
          // params.roleIds=params.roleIds.join(",")
          params.roleName=state.roleList.filter(item=>params.roleIds.includes(item.id)).map(item=>item.roleName)
          params.publishTime = timeStr(params.publishTime);
          let { code,message } = await fn(params);
          if(code===0){
            ElMessage.success(message)
            context.emit("close",true)
          }
        } else {
          return false;
        }
      });
    };
    const edit=()=>{
      context.emit('edit')
    }

    

    const beforeClose = () => {
      context.emit("close",false)
    };
    onMounted(()=>{
      queryRolelist()
    })
    return {
      rules,
      formDom,
      messTypeList,
      state,
      title,
      submit,
      edit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>

:deep(.el-form-item__content) {
  .el-select {
    width: 100%;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }
  .el-date-editor.el-input {
    width: 100%;
  }
}
.news-text {
  box-sizing: border-box;
  width: 100%;
  height: 300px;
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow-y: scroll;
}
</style>