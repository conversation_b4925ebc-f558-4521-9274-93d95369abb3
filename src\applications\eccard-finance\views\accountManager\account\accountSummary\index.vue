<template>
  <kade-tab-wrap :tabs="tabs" @active="active" style="margin-top: 20px" v-model="state.tab">
    <template #qbgl>
      <kade-wallet-manager />
    </template>
    <template #jymx>
      <kade-transaction-details />
    </template>
    <template #kpgl>
      <kade-card-manager />
    </template>
    <template #grzh>
      <kade-personal-summary />
    </template>
    <template #js>
      <kade-family-members />
    </template>

    <template #extra>
      {{userInfo&&userInfo.userId?'当前选择人员：'+userInfo.userName:'未选择用户'}}
    </template>
  </kade-tab-wrap>

</template>
<script>
import { computed, reactive } from "vue";
import { requestDate } from "@/utils/reqDefaultDate.js"
import cardManager from "./components/cardManager";
import familyMembers from "./components/familyMembers";
import personalSummary from "./components/personalSummary";
import transactionDetails from "./components/transactionDetails";
import walletManager from "./components/walletManager";
import { useStore } from "vuex"
const tabs = [
  {
    name: "qbgl",
    label: "钱包管理",
  },
  {
    name: "jymx",
    label: "交易明细",
  },
  {
    name: "kpgl",
    label: "卡片管理",
  },
  {
    name: "grzh",
    label: "个人汇总",
  },
  {
    name: "js",
    label: "家属",
  },
];
export default {

  components: {
    "kade-card-manager": cardManager,
    "kade-family-members": familyMembers,
    "kade-transaction-details": transactionDetails,
    "kade-personal-summary": personalSummary,
    "kade-wallet-manager": walletManager,
  },
  setup() {
    const store = useStore()
    const state = reactive({
      tab: "qbgl",
      departCheckList: [],
      costTypelist: [],
    });
    const userInfo = computed(() => {
      return store.state.data.selectPerson
    })
    const active = (val) => {
      console.log(val.index);
      store.commit("data/updateState", { key: "activeItem", payload: Number(val.index) })
      store.commit("data/updateState", {
        key: "watchExportData",
        payload: {
          beginDate: requestDate()[0],
          endDate: requestDate()[1],
          currentPage: 1,
          pageSize: 6,
        },
      });
    }
    return {
      state,
      tabs,
      userInfo,
      active
    };
  },
};
</script>
<style lang="scss">
.box {
  min-height: 650px;
  // overflow-y: scroll;
}
</style>