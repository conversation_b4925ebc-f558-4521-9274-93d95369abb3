 import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import { 
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
	componentMap: {
		archives: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/basic/archives/index.vue'))),
		declare: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/basic/declare/index.vue'))),
		dict: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/basic/dict/index.vue'))),
		infect: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/basic/infect/index.vue'))),
		patientLeave: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/basic/patientLeave/index.vue'))),
		resumeAudit: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/basic/resumeAudit/index.vue'))),
		
		healthData: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/report/healthData/index.vue'))),
		healthDeclare: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/report/healthDeclare/index.vue'))),
		infectData: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/report/infectData/index.vue'))),
		patientLeaveRecord: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/report/patientLeaveRecord/index.vue'))),

	},
	customMenus: [],  
};
const mutations = {
	updateState(state, { key, payload }) { 
		state[key] = payload;
	},
};
const actions = {
  ...mixinActions,
	async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data?data:[]));
    commit('updateState', { key: 'dictionary', payload: data?data:[] });
  },
};

const getters = {
  ...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
  getters,
}
