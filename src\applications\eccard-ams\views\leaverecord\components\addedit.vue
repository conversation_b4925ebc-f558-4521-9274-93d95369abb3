<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="选择人员" prop="atgUsers" v-if="state.model.disType!='info' && state.model.disType!='exam'">
        <div class="divSelectUser">
          <el-tag class="ml-2" type="success" v-if="state.model.manageUser">{{state.model.manageUser.userNum + ' ' + state.model.manageUser.userName}}</el-tag>
        </div>
        <el-button type="primary" link @click="state.model.isShowSelectedManagerUser = true;">选择</el-button>
      </el-form-item>

      <el-row :gutter="5" v-if="state.model.disType=='info' || state.model.disType=='exam'">
        <el-col :sm="12">
          <el-form-item label="用户编号">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.userNum : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="用户名称">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.userName : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="请假原因">
        <el-select v-model="state.model.leaType">
          <el-option v-for="(item, index) in directionList" :key="index" :label="item.label" :value="item.label">
          </el-option>
        </el-select>
      </el-form-item>

      <el-row :gutter="0">
        <el-col :sm="12">
          <el-form-item label="开始日期" prop="leaStarttime">
            <el-date-picker v-model="state.model.leaStarttime" :value-format="'YYYY-MM-DD HH:mm:ss'" type="datetime" placeholder="选择开始日期"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" :size="size" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="结束日期" prop="leaEndtime">
            <el-date-picker v-model="state.model.leaEndtime" :value-format="'YYYY-MM-DD HH:mm:ss'" type="datetime" placeholder="选择结束日期"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" :size="size" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="请假原因" prop="leaReson">
        <el-input v-model="state.model.leaReson"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" :rows="5" type="textarea" placeholder="请输入补录原因" />
      </el-form-item>

      <!--<el-form-item label="是否自动审核" prop="atoExam" v-if="state.model.disType!='info' && state.model.disType!='exam'">-->
        <!--<el-switch v-model="state.model.atoExam" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"-->
                   <!--inactive-value="0" inactive-text="关" active-text="开">-->
        <!--</el-switch>-->
      <!--</el-form-item>-->


      <!--<el-row :gutter="0"  v-if="state.model.disType=='info' || state.model.disType=='exam'">-->
        <!--<el-col :sm="12">-->
          <!--<el-form-item label="审核方式">-->
          <!--<el-input :modelValue="state.model.atoExam == '1' ? '自动审核' : '领导审核'"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" />-->
          <!--</el-form-item>-->
        <!--</el-col>-->
        <!--<el-col :sm="12">-->
          <!--<el-form-item label="提交时间">-->
          <!--<el-input v-model="state.model.submitDate"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" />-->
          <!--</el-form-item>-->
        <!--</el-col>-->
      <!--</el-row>-->

      <el-row :gutter="0"  v-if="state.model.disType=='info'">
        <el-col :sm="24">
          <el-row :gutter="0">
            <el-col :sm="24">
              审核信息
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :sm="12">
              <el-form-item label="审核结果">
                <el-input :modelValue="constExameList[state.model.exmResult]" />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item label="审核时间" prop="atoExam">
                <el-input :modelValue="state.model.exmDate"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :sm="24">
              <el-form-item label="未通过原因" prop="exmDesc" v-if="state.model.exmResult == 2">
                <el-input v-model="state.model.exmDesc" :readonly="state.model.disType=='info'" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <!--<el-row :gutter="0"  v-if="state.model.disType=='exam'">-->
        <!--<el-col :sm="24">-->
          <!--<el-row :gutter="0">-->
            <!--<el-col :sm="24">-->
              <!--审核-->
            <!--</el-col>-->
          <!--</el-row>-->
          <!--<el-row :gutter="0">-->
            <!--<el-col :sm="24">-->
              <!--<el-form-item label="是否通过">-->
                <!--<el-radio-group v-model="state.model.exmResult" class="ml-4">-->
                  <!--<el-radio label="1" size="large">通过</el-radio>-->
                  <!--<el-radio label="2" size="large">不通过</el-radio>-->
                <!--</el-radio-group>-->
              <!--</el-form-item>-->
              <!--<el-form-item label="未通过原因" prop="exmDesc" v-if="state.model.exmResult == 2">-->
                <!--<el-input v-model="state.model.exmDesc" :readonly="state.model.disType=='info'" />-->
              <!--</el-form-item>-->
            <!--</el-col>-->
          <!--</el-row>-->
        <!--</el-col>-->
      <!--</el-row>-->

    </el-form>
    <template #footer>
      <p style="text-align: center" v-if="state.model.disType!='info'">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>
  <selected-manageuser :modelValue="state.model.isShowSelectedManagerUser" :title="'选择人员'" @update:modelValue="selectedManagerUserClose" />
</template>
<script>
  import { reactive, watch , ref , computed , onMounted} from "vue";
import {
  // getAttentionGroupInfoPage ,
  // delAttentionGroupInfoPage ,
  examAttentionLeaveApply, updateAttentionLeaveApplyAddInfo , addAttentionLeaveApplyAddInfo ,
  // getAttentionGroupDetail  ,
  getAttentionLeaveApplyDetail , getDistinctAttentionClass}
  from "@/applications/eccard-ams/api";
  import { timeStr } from "@/utils/date.js"
  import selectedManageuser from "@/applications/eccard-ams/views/attendaddinfo/components/selectedmanageuser.vue"
  import { useDict } from '@/hooks/useDict.js'
  import {
    ElRow,
    ElCol,
    ElTag,
    // ElSwitch,
    // ElLink,
    ElSelect,
    ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    // ElTable,
    // ElTableColumn,
    ElButton,
    // ElPagination,
    ElMessage,
    ElDatePicker,
    // ElMessageBox,
    // ElRadioGroup,
    // ElRadio,
    ElDialog,
  } from 'element-plus';

const getDefaultModel = () => ({
  manageUser:null,
  leaType:null,     //选择的班次信息
  leaReson: "",
  leaStarttime: "",
  leaEndtime:"",
  leaTime:'',
  atoExam:0,
  disType:'info',
});
export default {
  emits: ["close"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    type :{
      type: String,
      default: "",
    },
  },
  components: {
    selectedManageuser,
    // ElTimePicker,
    ElRow,
    // ElLink,
    ElCol,
    ElTag,
    // ElSwitch,
    ElSelect,
    ElOption,
    // ElRadioGroup,
    // ElRadio,
    ElInput,
    ElForm,
    ElFormItem,
    // ElTable,
    // ElTableColumn,
    ElDatePicker,
    ElButton,
    // selectedClassAll,
    ElDialog,
  },
  setup(props, context) {
    const weekDays = ['一','二','三','四','五','六','日'];
    const free_tableRef = ref(null);
    const fixed_tableRef = ref(null);
    const formRef = ref(null);
    const btnLoading = ref(false);
    const constClassList = ref(["一次班" , "二次班" , "三次班"]);
    const constExameList = ref(["待审核" , "已通过" , "未通过"]);
    const disType = ref(props.type);
    const state = reactive({
      model: getDefaultModel(),
    });
    const directionList = useDict("DORM_LEAVE_REASON")
    const rules = {
      leaStarttime: [
        { required: true, message: "请输入开始时间" }
      ],
      leaEndtime: [
        { required: true, message: "请输入结束时间" }
      ],
      ataType: [
        { required: true, message: "请输入请假类型" }
      ],
      leaReson: [
        { required: true, message: "请输入请假原因" }
      ],
    };
    const selectedManagerUserClose = async (val)=>{
      if(val){
        console.log(JSON.stringify(val))
        state.model.manageUser = {};
        state.model.manageUser["userId"] = val.userId;
        state.model.manageUser["userNum"] = val.userNum;
        state.model.manageUser["userName"] = val.userName;
        state.model.manageUser["atgId"] = val.atgId;
        state.model.manageUser["atgName"] = val.atgName;
        state.model.manageUser["orgNameList"] = val.orgNameList;

        //查询此用户的班次信息
        if(val.atgId) {
          const {code, message, data} = await getDistinctAttentionClass({atgId: val.atgId});
          if (code != 0) {
            ElMessage.error(message);
          }
          state.model.manageUser["attendClassObjList"] = data;
          // state.model.manageUser.attendClassObj = data;
        }else{
          state.model.manageUser["attendClassObjList"] = [];
        }
      }else{
        state.model.manageUser = null;
      }
      state.model.isShowSelectedManagerUser = false;
    };
    const handleSelectionFreeChange = (val)=>{
      state.model.selectedFreeClassData = val;
    };
    const handleSelectionFixedChange = (val)=>{
      state.model.selectedFixedClassData = val;
    }
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const checkStr = (substring)=> {
      if(substring){
        var reg = new RegExp("[~#^$@%&!?%*]", 'g');
        if (substring.match(reg)) {
          return true;
        }
        for ( var i = 0; i < substring.length; i++) {
          var hs = substring.charCodeAt(i);
          if (0xd800 <= hs && hs <= 0xdbff) {
            if (substring.length > 1) {
              let ls = substring.charCodeAt(i + 1);
              let uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
              if (0x1d000 <= uc && uc <= 0x1f77f) {
                return true;
              }
            }
          } else if (substring.length > 1) {
            let ls = substring.charCodeAt(i + 1);
            if (ls == 0x20e3) {
              return true;
            }
          } else {
            if (0x2100 <= hs && hs <= 0x27ff) {
              return true;
            } else if (0x2B05 <= hs && hs <= 0x2b07) {
              return true;
            } else if (0x2934 <= hs && hs <= 0x2935) {
              return true;
            } else if (0x3297 <= hs && hs <= 0x3299) {
              return true;
            } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030
              || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b
              || hs == 0x2b50) {
              return true;
            }
          }
        }
      }
    };
    const submit = async () => {
      if(checkStr(state.model.leaReson)){
        ElMessage.error('请假原因不能包含特殊字符和表情图标！');
        return;
      }
      if(state.model.disType=='exam'){
        if (state.model.exmResult == '2' && (!state.model.exmDesc || state.model.exmDesc.toString().trim() == '')) {
          ElMessage.error('审核描述信息不能为空！');
          return;
        }
        try {
          btnLoading.value = true;
          const {message, code} = await examAttentionLeaveApply(state.model);
          if (code === 0) {
            ElMessage.success(message);
          } else {
            ElMessage.error(message);
          }
          context.emit("update:modelValue", true);
        } catch (e) {
          throw new Error(e.message);
        } finally {
          btnLoading.value = false;
        }
      }else {
        formRef.value?.validate(async (valid) => {
          if (valid) {
            try {
              btnLoading.value = true;
              const fn = props.data?.leaId ? updateAttentionLeaveApplyAddInfo  : addAttentionLeaveApplyAddInfo;
              state.model["userId"] = state.model.manageUser.userId;
              let constObjs = Object.assign({} , state.model);
              if(!state.model["userId"]){
                ElMessage.error('请假人员不能为空！');
                return;
              }
              // constObjs["leaStarttime"] = dateStr(constObjs["leaStarttime"]);
              // constObjs["leaEndtime"] = dateStr(constObjs["leaEndtime"]);
              if(constObjs["atoExam"] == '1') {
                constObjs["ataExamType"] = "自动审核";
              }else{
                constObjs["ataExamType"] = "领导审核";
              }
              constObjs["submitDate"] = constObjs["submitDate"] ? new Date(constObjs["submitDate"]) : null;
              constObjs["exmDate"] = constObjs["exmDate"] ? new Date(constObjs["exmDate"]) : null;
              const {message, code} = await fn(constObjs);
              if (code === 0) {
                ElMessage.success(message);
              } else {
                ElMessage.error(message);
              }
              context.emit("update:modelValue", true);
            } catch (e) {
              throw new Error(e.message);
            } finally {
              btnLoading.value = false;
            }
          }
        });
      }
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, data, ...attrs } = props;
      return attrs;
    });

    onMounted(() => {
      //编辑状态，选中表格
    });

    watch(
      () => props.modelValue,
      async (n) => {
        if (n) {
          if (props.data?.leaId) {
            const { code, message , data } = await getAttentionLeaveApplyDetail(props.data.leaId);
            if(code != 0){
              ElMessage.error(message);return;
            }
            data["manageUser"] = {userId:data.userId , userNum:data.userNum , userName:data.userName , atgId:data.atgId , atgName : data.atgName , orgNameList:data.orgNameList};

            let objbase = getDefaultModel();
            // data["ataTimeStartDate"] = new Date(2022, 11, 4, parseInt(data.ataTimeStartDate.toString().substr(0 , 2)), parseInt(data.ataTimeStartDate.toString().substr(3 , 2)));
            // data["ataTimeEndDate"] = new Date(2022, 11, 4, parseInt(data.ataTimeEndDate.toString().substr(0 , 2)), parseInt(data.ataTimeEndDate.toString().substr(3 , 2)));

            if(data["exmDate"]) {
              data["exmDate"] = timeStr(data["exmDate"]);
            }
            if(data["submitDate"]) {
              data["submitDate"] = timeStr(data["submitDate"]);
            }
            state.model = Object.assign(objbase, data);
          } else {
            state.model = getDefaultModel();
          }

          state.model.disType = props.type;
        }
      }
    );
    return {
      weekDays,
      attrs,
      // update,
      formRef,
      cancel,
      submit,
      free_tableRef,
      fixed_tableRef,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      disType,
      btnLoading,
      themes: THEMEVARS,
      // editFreeClass,
      // selectedClassAllClose,
      // selectedUsersClose,
      selectedManagerUserClose,
      handleSelectionFreeChange,
      handleSelectionFixedChange,
      constClassList,
      constExameList,
      checkStr,
      directionList,
    };
  },
};
</script>
