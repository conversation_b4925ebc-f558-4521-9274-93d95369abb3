<template>
  <!-- 消费分类汇总 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="汇总时间:">
          <el-date-picker v-model="state.dayList" type="datetimerange" :default-time="defaultTime" unlink-panels range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="消费分类汇总" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        <el-button @click="handlePrint()" icon="el-icon-printer" size="mini" class="btn-blue">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
} from "element-plus";
import { downloadXlsx, print } from "@/utils"
import { timeStr, dateStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { requestDate, defaultTime } from "@/utils/reqDefaultDate";
import {
  consumptionCategorySummaryPage,
  consumptionCategorySummaryExport,
  consumptionCategorySummaryPrint
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
const column = [
  {
    label: "汇总时间", prop: "summaryDate", width: "180", render(val) {
      return val !== '合计' ? dateStr(val) : ''
    }
  },
  { label: "现金消费笔数", prop: "xjCount", width: "" },
  { label: "现金消费金额", prop: "xjAmount", width: "" },
  { label: "补助消费笔数", prop: "bzCount", width: "" },
  { label: "补助消费金额", prop: "bzAmount", width: "", },
  { label: "纠错总笔数", prop: "jcCount", width: "" },
  { label: "纠错总金额", prop: "jcAmount", width: "" },
  { label: "消费总笔数（现金+补助）", prop: "actualCount", width: "" },
  { label: "消费总金额（现金+补助-纠错）", prop: "actualAmount", width: "" },

]


export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    ElDatePicker,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      dayList: requestDate(),
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
    });
    const getParams = () => {
      let params = { ...state.form }
      if (state.dayList && state.dayList.length) {
        params.startDate = timeStr(state.dayList[0])
        params.endDate = timeStr(state.dayList[1])
      } else {
        ElMessage.error("请选择汇总时间")
        return false
      }
      return params
    }
    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data: { page: { list, total }, total: amount } } = await consumptionCategorySummaryPage(params);
        if (code === 0) {

          state.dataList = list;
          state.total = total;
          if (total) {
            state.dataList.push({
              ...amount,
              summaryDate: "合计"
            })
          }
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };

    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let res = await consumptionCategorySummaryExport(params);
        downloadXlsx(res, "消费分类汇总.xlsx")
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handlePrint = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let { data, code } = await consumptionCategorySummaryPrint(params)
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
      state.dayList = requestDate()
    };
    onMounted(() => {
      // getList();
    });
    return {
      column,
      state,
      timeStr,
      defaultTime,
      exportClick,
      handlePrint,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
