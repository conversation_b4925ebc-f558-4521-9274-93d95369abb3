<template>
  <el-dialog :model-value="isShow" :title="data ? '调配人员' : '分配人员'" width="1300px" :before-close="beforeClose" :close-on-click-modal="false">
    <div style="margin-top:20px" v-loading="state.loading">
      <kade-select-table :isMultiple="false" :isShow="isShow" :isDefaultRequest="false" :value="[]" :reqFnc="getUserInfoListByPage" :selectCondition="state.selectCondition" :column="column" :params="params" @rowChange="rowChange" @change="personChange" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">取&nbsp;&nbsp;消</el-button>
        <el-button @click="submit()" type="primary" size="mini">确&nbsp;&nbsp;认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElMessage } from "element-plus"
import { reactive, onMounted } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import {
  getUserInfoListByPage, getRolelist
} from "@/applications/eccard-basic-data/api";
import { userBedAllocateToBedNum } from "@/applications/eccard-dorm/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "roleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "beginPage",
  pageSizeKey: "rowNum",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {
    userState: "STATE_IN"
  },
  tagNameKey: "userName",
  valueKey: "id",
};
export default {
  components: {
    ElDialog,
    ElButton,
    "kade-select-table": selectTable,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: String,
      default: ""
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      selectCondition: [
        {
          label: "组织机构",
          valueKey: "userDept",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRole",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: [],
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
        {
          label: "性别",
          valueKey: "userSex",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: useDict("SYS_SEX"),
            option: {
              label: "label",
              value: "value",
            },
          },
        },
        { label: '关键字', valueKey: 'userName', placeholder: '姓名或编号关键字搜索', isSelect: false },
      ],
      form: {
        userIds: []
      }
    })
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        state.selectCondition[1].select.list = res.data
      });
    };
    const rowChange = (val) => {
      console.log(val);
      state.form.user = val;
    };
    const submit = async () => {
      let params = {
        userId: state.form.user.id,
        bedNum: props.data.bedNum,
        roomId: props.data.roomId,
      };
      state.loading = true
      try {
        let { message, code } = await userBedAllocateToBedNum(params)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("close", props.data)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeClose = () => {
      context.emit("close", false)
    }
    onMounted(() => {
      queryRolelist()
    })
    return {
      getUserInfoListByPage,
      column,
      params,
      state,
      rowChange,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0;
}
</style>