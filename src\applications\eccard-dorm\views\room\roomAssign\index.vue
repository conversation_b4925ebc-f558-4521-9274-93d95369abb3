<template>
  <div class="padding-box" style="overflow-x: auto;">
    <kade-table-wrap>
      <div class="main-box">
        <div class="area">
          <el-tree class="area-tree" ref="treeRef" :data="state.areaCheckTreeList" :props="defaultProps" node-key="id"
            @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-map-location"></i>
                  <span style="margin-left: 5px; color: #333">{{ node.label }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
        <div class="room" v-loading="state.loading">
          <div class="room-filter">
            <el-form inline label-width="90px" size="mini">
              <el-form-item label="组织机构">
                <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath"
                  :multiple="false" @valueChange="deptChange" />
              </el-form-item>
              <el-form-item label="分配状态">
                <el-select clearable v-model="state.form.allocState" placeholder="请选择">
                  <el-option v-for="(item, index) in allocStateList" :key="index" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div>
              <el-button type="primary" size="mini" icon="el-icon-search" @click="search" :disabled="state.isBuilding">
                搜索</el-button>
              <el-button type="success" size="mini" icon="el-icon-edit" @click="handleRoom"
                :disabled="!state.form.deptPath">分配房间</el-button>
              <el-button class="btn-blue" size="mini" icon="el-icon-top" @click="handleExport">导入分配</el-button>
            </div>
          </div>
          <div class="allocate" v-if="state.isBuilding">
            <div class="allocate-item">
              <div class="move"></div>
              <span class="move-item">已入住</span>
            </div>
            <div class="allocate-item">
              <div class="nomove"></div>
              <span class="nomove-item">未入住</span>
            </div>
            <div class="allocate-item">
              <div class="full"></div>
              <span class="full-item">已住满</span>
            </div>
          </div>
          <div class="allocate" v-else>
            <div class="allocate-item">
              <div class="full"></div>
              <span class="full-item">已住满</span>
            </div>
            <div class="allocate-item">
              <div class="move"></div>
              <span class="move-item">未住满</span>
            </div>
            <div class="allocate-item">
              <div class="nomove"></div>
              <span class="nomove-item">未入住</span>
            </div>
          </div>
          <div class="detailed" v-if="state.isBuilding">
            楼栋总数：{{ state.statisticsData.buildTotalCount ? state.statisticsData.buildTotalCount : 0 }}栋 房间总数：{{
                state.statisticsData.roomTotalCount ? state.statisticsData.roomTotalCount : 0
            }}间
            空置房间：{{ state.statisticsData.emptyRoomTotalCount ? state.statisticsData.emptyRoomTotalCount : 0 }}间 未住满房间：{{
                state.statisticsData.notFullRoomTotalCount ? state.statisticsData.notFullRoomTotalCount : 0
            }}间
            空余床位数：{{ state.statisticsData.emptyBedTotalCount ? state.statisticsData.emptyBedTotalCount : 0 }}个
          </div>
          <div class="detailed" v-else>
            房间总数：{{ state.statisticsData.roomTotalCount ? state.statisticsData.roomTotalCount : 0 }}间 空置房间：{{
                state.statisticsData.emptyRoomTotalCount ? state.statisticsData.emptyRoomTotalCount : 0
            }}间
            未住满房间：{{
                state.statisticsData.notFullRoomTotalCount ? state.statisticsData.notFullRoomTotalCount : 0
            }}间 空余床位数：{{ state.statisticsData.emptyBedTotalCount ? state.statisticsData.emptyBedTotalCount : 0 }}个
          </div>
          <component :is="state.comName" :dataList="state.list" />
        </div>
      </div>
    </kade-table-wrap>
    <kade-room-export v-model="state.isExport" @update:modelValue="closeExport" />
    <kade-assign-room v-model="state.isAssign" :data="state.form" @update:modelValue="state.isAssign = false" />
  </div>
</template>
<script>
import { ElTree, ElForm, ElFormItem, ElSelect, ElOption, ElButton } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { makeTree } from "@/utils/index.js";
import { useDict } from "@/hooks/useDict.js";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { roomAllocationBuildList, roomAllocationRoomInfoList, roomAllocationBuildingStatistics, roomAllocationRoomStatistics } from "@/applications/eccard-dorm/api";
import deptSelectTree from "@/components/tree/deptSelectTree";
import roomExport from "./components/export";
import assignRoom from "./components/assignRoom";
import building from './building'
import room from './room'
const defaultProps = {
  children: "children",
  label: "areaName",
};
export default {
  components: {
    ElTree,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
    "kade-room-export": roomExport,
    "kade-assign-room": assignRoom,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const allocStateList = useDict("DORM_ALLOC_TYPE")
    const treeRef = ref(null)
    const state = reactive({
      loading: false,
      isExport: false,
      isAssign: false,
      comName: building,
      isBuilding: true,
      areaCheckTreeList: [],
      form: {},
      list: [],
      statisticsData: {}
    });
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = res.data.map(item => {
          return {
            ...item,
            type: 'AREA',
            areaId: item.id
          }
        })
        let arrTree = makeTree(arr, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arrTree];
      });
    };

    const handleNodeClick = async (val) => {
      console.log(val)
      state.form.areaId = val.id
      state.loading = true
      state.list = []
      state.statisticsData = {}
      try {
        if (val.type == 'AREA') {
          state.isBuilding = true

          roomAllocationBuildingStatistics({ areaId: val.id }).then(({ data }) => {
            state.statisticsData = data ? data : {}
          })

          roomAllocationBuildList({ areaId: state.form.areaId }).then((res) => {
            state.list = [...res.data]
            if (state.list.length) {
              let arr = res.data.map((item, index) => {
                let childArr1 = []
                for (let i = 0; i < item.unitCount; i++) {
                  let childArr2 = []
                  for (let j = 0; j < item.floorCount; j++) {
                    childArr2.push({
                      ...item,
                      areaId: state.form.areaId,
                      areaParentId: (i + 1) + new Date().getTime(),
                      id: (j + 2) + new Date().getTime(),
                      floorNum: j + 1,
                      unitNum: i + 1,
                      areaName: j + 1 + '楼',
                      type: 'FLOOR'
                    })
                  }
                  childArr1.push({
                    ...item,
                    type: 'UNIT',
                    areaId: state.form.areaId,
                    id: (i + 1) + new Date().getTime(),
                    areaParentId: +new Date().getTime(),
                    areaName: i + 1 + '单元',
                    unitNum: i + 1,
                    children: childArr2
                  })
                }
                let obj = {
                  ...item,
                  type: 'BUILD',
                  areaName: item.buildName,
                  id: (index + 1) + new Date().getTime(),
                  areaId: state.form.areaId,
                  areaParentId: state.form.areaId,
                }
                if (childArr1.length > 0) {
                  obj.children = childArr1
                }
                return obj
              })
              console.log(arr, val.children);

              if (val.children) {
                arr.push(...val.children.filter(item => item.type == 'AREA'))
              }
              console.log(arr, val);
              val.children = arr
              // treeRef.value.updateKeyChildren(val.id, [...arr]);
            } else {
              return []
            }
          })
          state.comName = building
        } else {
          state.isBuilding = false
          //请求房间列表
          state.form.buildId = val.buildId
          if (val.unitNum) {
            state.form.unitNum = val.unitNum
          } else {
            delete state.form.unitNum
          }
          if (val.floorNum) {
            state.form.floorNum = val.floorNum
          } else {
            delete state.form.floorNum
          }
          getRoomList()
          getRoomStatistics()
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getRoomList = async () => {
      state.loading = true
      try {
        let { data } = await roomAllocationRoomInfoList(state.form)
        state.list = data.map(item => {
          return {
            ...item,
            isPopover: true
          }
        })
        state.comName = room
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getRoomStatistics = () => {
      roomAllocationRoomStatistics(state.form).then(({ data }) => {
        state.statisticsData = data ? data : {}
      })
    }

    const deptChange = (val) => {
      state.form.deptPath = val.deptPath
      state.form.deptId = val.id
      state.form.deptName = val.deptName

    }

    const search = () => {
      state.statisticsData = {}
      getRoomList()
      getRoomStatistics()
    }
    const handleExport = () => {
      state.isExport = true;
    };
    const handleRoom = () => {
      state.isAssign = true;
    }
    const closeExport = (val) => {
      if (val) {
        getRoomList()
      }
      state.isExport = false;
    };
    onMounted(() => {
      queryAreaCheckList();
    });
    return {
      building,
      room,
      defaultProps,
      allocStateList,
      treeRef,
      state,
      handleNodeClick,
      deptChange,
      search,
      handleExport,
      handleRoom,
      closeExport,
    };
  },
};
</script>
<style lang="scss" scoped>
.kade-table-wrap {
  padding-bottom: 0px;
  min-width: 1170px;
}

.main-box {
  min-width: 1170px;
  height: 75vh;
  display: flex;
  border-top: 1px solid #eeeeee;

  .area {
    width: 240px;
    min-width: 240px;
    height: 100%;
    border-right: 1px solid #eeeeee;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .room {
    width: 100%;

    .room-filter {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 15px;
    }

    .el-form {
      margin-top: 15px;
    }

    .allocate {
      display: flex;
      align-items: center;
      border-top: 1px solid #eeeeee;
      padding: 15px 20px;

      .allocate-item {
        display: flex;
        align-items: center;
        margin-right: 15px;

        .move-item {
          color: #1296db;
        }

        .nomove-item {
          color: #70b603;
        }

        .full-item {
          color: #aaaaaa;
        }

        .move {
          width: 15px;
          height: 15px;
          margin-right: 5px;
          background-color: #1296db;
        }

        .nomove {
          width: 15px;
          height: 15px;
          margin-right: 5px;
          background-color: #70b603;
        }

        .full {
          width: 15px;
          height: 15px;
          margin-right: 5px;
          background-color: #aaaaaa;
        }
      }
    }

    .detailed {
      background-color: black;
      border-radius: 5px;
      margin: 0 20px;
      line-height: 24px;
      font-size: 12px;
      color: #fff;
      text-align: center;
    }
  }
}

:deep(.el-checkbox__label) {
  color: #fff;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-dialog__body) {
  padding-bottom: 20px !important;
}
</style>