<template>
  <el-dialog :modelValue="modelValue" :title="title" :before-close="cancel" :append-to-body="true" width="1000px">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="考勤组名称" prop="atgName">
        <el-input placeholder="请输入（不超过30字）" v-model="state.model.atgName" :readonly="state.disType == 'info'" />
      </el-form-item>
      <el-form-item label="考勤组管理员" prop="atgManageUser" v-if="state.disType != 'info'">
        <el-row :gutter="20">
          <el-col :sm="20">
            <el-tag class="ml-2" type="success" v-if="state.model.manageUser['userNum']">{{
              state.model.manageUser["userNum"] ?
              state.model.manageUser.userNum + '-' + state.model.manageUser.userName : '' }}</el-tag>
          </el-col>
          <el-col :sm="4">
            <el-button type="primary" link @click="state.model.isShowSelectedManagerUser = true;">选择</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="考勤组管理员" prop="atgManageUser" v-if="state.disType == 'info'">
        <el-input :modelValue="state.model.manageUser.userNum + ' ' + state.model.manageUser.userName"
          :readonly="state.disType == 'info'" />
      </el-form-item>
      <el-form-item label="考勤策略" prop="atgManageUser" v-if="state.disType !== 'info'">
        <el-radio-group v-model="state.model.userDeptType">
          <el-radio :label="item.value" v-for="(item, index) in strategyList" :key="index">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="考勤策略" prop="atgManageUser" v-if="state.disType == 'info'">
        <el-input :modelValue="state.model.userDeptType === 1 ? '按组织机构分组' : '按人员分组'" readonly />
      </el-form-item>
      <el-form-item label=" 考勤组织机构" prop="atgUsers" v-if="state.disType != 'info' && state.model.userDeptType === 1">
        <el-row :gutter="20">
          <el-col :sm="20">
            <el-tag class="ml-2" type="success" v-for="(item, index) in state.model.attendDeptIdList" :key="index"
              closable @close="() => delDept(index)">{{
                item.deptName }}</el-tag>
          </el-col>
          <el-col :sm="4">
            <el-button type="primary" link @click="state.model.isSelectedDept = true;">选择</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="考勤组织机构" prop="atgUsers" v-if="state.disType == 'info' && state.model.userDeptType === 1">
        <el-tag class="ml-2" type="success" v-for="(item, index) in state.model.attendDepts" :key="index">{{ item.deptName
        }}</el-tag>
      </el-form-item>
      <el-form-item label="考勤人员" prop="atgUsers" v-if="state.disType == 'info' && state.model.userDeptType === 0">
        <el-table :data="state.model.attendUsers" style="width: 100%">
          <el-table-column label="用户编号" prop="userNum" align="center" width="120"></el-table-column>
          <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="考勤人员" prop="atgUsers" v-if="state.disType != 'info' && state.model.userDeptType === 0">
        <el-row :gutter="20">
          <el-col :sm="20">
            <el-tag class="ml-2" type="success" v-for="(item, index) in state.model.attendUserList"
              :key="index + '111'">{{ item.userNum + ' ' + item.userName }}</el-tag>
          </el-col>
          <el-col :sm="4">
            <el-button type="primary" link @click="state.model.isShowSelectedUsers = true;">选择</el-button>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="考勤类型" prop="atgType" v-if="state.disType != 'info'">
        <el-switch v-model="state.model.atgType" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor"
          active-value="固定考勤" inactive-value="自由考勤" inactive-text="自由考勤（不设置班次，随时打卡）" active-text="固定考勤（设置班次，根据班次设定打卡）"
          :disabled="state.disType != 'add'">
        </el-switch>
      </el-form-item>
      <el-form-item label="考勤类型" prop="atgType" v-if="state.disType == 'info'">
        <el-input :modelValue="state.model.atgType == '固定考勤' ? '固定考勤（设置班次，根据班次设定打卡）' : '自由考勤（不设置班次，随时打卡）'"
          :disabled="state.disType == 'info'" />
      </el-form-item>
      <el-form-item label="" v-if="state.model.atgType == '固定考勤' && state.disType != 'info'">
        <el-row :gutter="20">
          <el-col :sm="8">
            快捷设置班次：
          </el-col>
          <el-col :sm="8">

          </el-col>
          <el-col :sm="8">
            <el-button type="primary" link @click="showAllFixedClass">更改班次</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :sm="24">
            <el-table ref="fixed_tableRef" :data="state.model.fixedClassList" style="width: 100%"
              @selection-change="handleSelectionFixedChange">
              <el-table-column type="selection" width="55" />
              <el-table-column label="工作日" prop="atgDay" align="center" width="120">
                <template #default="scope">周{{ weekDays[scope.row.atgDay - 1] }}</template>
              </el-table-column>
              <el-table-column label="班次" prop="atcClassname" align="center">
                <template #default="scope">{{ scope.row.atcId ? scope.row.atcClassname + ':' + scope.row.atcTimes :
                  '' }}</template>
              </el-table-column>
              <el-table-column label="操作" prop="" align="center" width="120">
                <template #default="scope">
                  <el-button @click="editFreeClass(scope.row, 'info')" type="text" size="mini">更改班次</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="" v-if="state.model.atgType == '固定考勤' && state.disType == 'info'">
        <el-row :gutter="20">
          <el-col :sm="24">
            <el-table ref="fixed_tableRef1" :data="state.model.selectedFixedClassData" style="width: 100%">
              <el-table-column label="工作日" prop="atgDay" align="center" width="120">
                <template #default="scope">周{{ weekDays[scope.row.atgDay - 1] }}</template>
              </el-table-column>
              <el-table-column label="班次" prop="atcClassname" align="center">
                <template #default="scope">{{ scope.row.atcId ? scope.row.atcClassname + ':' + scope.row.atcTimes :
                  '' }}</template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="" v-if="state.model.atgType == '自由考勤' && state.disType != 'info'">
        <el-row :gutter="20">
          <el-col :sm="24">
            <el-table ref="free_tableRef" :data="state.model.freeClassList" style="width: 100%"
              @selection-change="handleSelectionFreeChange">
              <el-table-column type="selection" width="80" />
              <el-table-column label="工作日" prop="atgDay" align="center">
                <template #default="scope">周{{ weekDays[scope.row.atgDay - 1] }}</template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="" v-if="state.model.atgType == '自由考勤' && state.disType == 'info'">
        <el-row :gutter="20">
          <el-col :sm="24">
            <el-table ref="free_tableRef1" :data="state.model.selectedFreeClassData" style="width: 100%">
              <el-table-column label="工作日" prop="atgDay" align="center">
                <template #default="scope">周{{ weekDays[scope.row.atgDay - 1] }}</template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="是否启用节假日" prop="atgHoliday" v-if="state.disType != 'info'">
        <el-switch v-model="state.model.atgHoliday" :active-color="themes.primaryColor"
          :inactive-color="themes.dangerColor" active-value="1" inactive-value="0" inactive-text="不启用节假日"
          active-text="法定节假日自动排休">
        </el-switch>
      </el-form-item>
      <el-form-item label="是否启用节假日" prop="atgHoliday" v-if="state.disType == 'info'">
        <el-input :modelValue="state.model.atgHoliday == '1' ? '启用' : '不启用'" :readonly="state.disType == 'info'" />
      </el-form-item>
    </el-form>
    <template #footer>
      <p style="text-align: center" v-if="state.disType != 'info'">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small"
          type="primary">保存</el-button>
      </p>
      <p style="text-align: center" v-if="state.disType == 'info'">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">关闭</el-button>
      </p>
    </template>
    <selected-manageuser :modelValue="state.model.isShowSelectedManagerUser" :title="'选择管理员'"
      @update:modelValue="selectedManagerUserClose" />
    <selected-users :modelValue="state.model.isShowSelectedUsers" :title="'选择考勤人员'" :data="state.model.attendUserList"
      @update:modelValue="selectedUsersClose" />
    <selected-class-all :modelValue="state.model.isShowSelectedClassAll" :title="'选择考勤班次'"
      @update:modelValue="selectedClassAllClose" />
    <selected-dept :modelValue="state.model.isSelectedDept" @update:modelValue="selectedDeptClose" />
  </el-dialog>
</template>
<script>
import { reactive, watch, ref, nextTick, computed } from "vue";
import {
  // getAttentionGroupInfoPage , delAttentionGroupInfoPage , getAttentionGroupDettail ,
  updateAttentGroup, addAttentGroup, getAttentionGroupDetail
} from "@/applications/eccard-ams/api";
import selectedManageuser from "@/applications/eccard-ams/views/attendgroup/components/selectedmanageuser.vue"
import selectedUsers from "@/applications/eccard-ams/views/attendgroup/components/selecteduser.vue"
import selectedDept from "@/applications/eccard-ams/views/attendgroup/components/selectedDept.vue"
import selectedClassAll from "@/applications/eccard-ams/views/attendgroup/components/selectedgroupclass.vue"
import {
  ElRow,
  ElCol,
  ElTag,
  ElSwitch,
  // ElSelect,
  // ElOption,
  ElInput,
  ElForm,
  ElFormItem,
  ElTable,
  ElTableColumn,
  ElButton,
  // ElPagination,
  ElMessage,
  // ElMessageBox,
  ElDialog,
  ElRadioGroup,
  ElRadio
} from 'element-plus';
const strategyList = [
  { label: "按人员分组", value: 0 },
  { label: "按组织机构分组", value: 1 },
]
const getDefaultModel = () => ({
  manageUser: {},//userId:1 , userNum:'e23rwer' , userName:'rwerew'
  atgManageUser: '',
  attendUserList: [],//{userId:1 , userNum:'dfsd' , userName:'dfgfd'},{userId:2 , userNum:'dg' , userName:'sdfsd'},{userId:3 , userNum:'dfgdf' , userName:'asdasd'}
  fixedClassList: [
    { atgDay: 1, atcId: '', atcClassname: '', atcTimes: '' },
    { atgDay: 2, atcId: '', atcClassname: '', atcTimes: '' },
    { atgDay: 3, atcId: '', atcClassname: '', atcTimes: '' },
    { atgDay: 4, atcId: '', atcClassname: '', atcTimes: '' },
    { atgDay: 5, atcId: '', atcClassname: '', atcTimes: '' },
    { atgDay: 6, atcId: '', atcClassname: '', atcTimes: '' },
    { atgDay: 7, atcId: '', atcClassname: '', atcTimes: '' },
  ],
  selectedFreeClassData: [],
  selectedFixedClassData: [],
  attendDeptIdList: [],
  freeClassList: [
    { atgDay: 1 },
    { atgDay: 2 },
    { atgDay: 3 },
    { atgDay: 4 },
    { atgDay: 5 },
    { atgDay: 6 },
    { atgDay: 7 },
  ],
  userDeptType: 0,
  roleName: "",
  status: "ENABLE_TRUE",
  isShowSelectedManagerUser: false,
  isShowSelectedUsers: false,
  isShowSelectedClassAll: false,
  isSelectedDept: false,
  atgType: '自由考勤',
  selectedData: null,
  atgHoliday: '0',
});
export default {
  emits: ["close"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: "",
    },
  },
  components: {
    // "kade-modal": Modal,
    selectedManageuser,
    selectedUsers,
    // selectedClassAll,
    ElRow,
    ElCol,
    ElTag,
    ElSwitch,
    // ElSelect,
    // ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    selectedClassAll,
    ElDialog,
    ElRadioGroup,
    ElRadio,
    selectedDept,
  },
  setup(props, context) {
    const weekDays = ['一', '二', '三', '四', '五', '六', '日'];
    const free_tableRef = ref(null);
    const fixed_tableRef = ref(null);
    const formRef = ref(null);
    const btnLoading = ref(false);
    // const disType = ref(props.type);
    const state = reactive({
      model: getDefaultModel(),
      disType: ref(props.type),
    });
    const rules = {
      atgName: [
        { required: true, message: "请输入考勤组名称" },
        { max: 30, message: "考勤组名称不能超过30个字符" },
      ],
    };
    const selectedManagerUserClose = (val) => {
      if (val) {
        state.model.manageUser.userId = val.userId;
        state.model.manageUser.userNum = val.userAccount;
        state.model.manageUser.userName = val.userName;
      }
      state.model.isShowSelectedManagerUser = false;
    };
    const selectedUsersClose = (vals) => {
      if (vals) {
        state.model.attendUserList = [];
        vals.forEach((item) => {
          state.model.attendUserList.push({
            userId: item.userId,
            userNum: item.userNum,
            userName: item.userName
          });
        });
      }
      state.model.isShowSelectedUsers = false;
    };
    const selectedClassAllClose = (val) => {
      if (val) {
        if (state.model.selectedData) {
          state.model.selectedData["atcId"] = val["atcId"];
          state.model.selectedData["atcClassname"] = val["atcClassname"];
          state.model.selectedData["atcTimes"] = val["atcTimes"];
        } else {
          state.model.selectedFixedClassData.forEach((item) => {
            item["atcId"] = val["atcId"];
            item["atcClassname"] = val["atcClassname"];
            item["atcTimes"] = val["atcTimes"];
          });
        }
      }
      state.model.isShowSelectedClassAll = false;
    };

    const selectedDeptClose = val => {
      if (val) {
        let list = state.model.attendDeptIdList.concat(val)

        state.model.attendDeptIdList = list.reduce((prev, cur) => {
          let arr = prev.map(item => item.id)
          if (!arr.includes(cur.id)) {
            return [...prev, cur]
          } else {
            return prev
          }
        }, [])
      }
      state.model.isSelectedDept = false
    }
    const delDept = (index) => {
      state.model.attendDeptIdList.splice(index, 1)
    }

    const editFreeClass = (row) => {
      state.model.selectedData = row;
      state.model.isShowSelectedClassAll = true;
    };
    const handleSelectionFreeChange = (val) => {
      state.model.selectedFreeClassData = val;
    };
    const handleSelectionFixedChange = (val) => {
      state.model.selectedFixedClassData = val;
      // state.model.fixedClassList.forEach((item)=>{
      //   if(state.model.selectedFixedClassData.filter((sitem)=>{return sitem.atgDay == item.atgDay}).length == 0){
      //     item["atcId"] = "";
      //     item["atcClassname"] = "";
      //     item["atcTimes"] = "";
      //   }
      // })
    }
    const showAllFixedClass = () => {
      if (!state.model.selectedFixedClassData || state.model.selectedFixedClassData.length == 0) {
        ElMessage.error('请选择固定考勤时间！'); return;
      }
      state.model.selectedData = null;
      state.model.isShowSelectedClassAll = true;
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const update = (v) => {
      context.emit("update:modelValue", v);
    };



    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          if (!state.model.manageUser["userId"]) {
            ElMessage.error('考勤组管理员信息不能为空！'); return;
          }
          if (state.model.userDeptType === 0) {
            if (state.model.attendUserList.length == 0) {
              ElMessage.error('考勤人员信息不能为空！'); return;
            }
          } else {
            if (state.model.attendDeptIdList.length == 0) {
              ElMessage.error('考勤组织机构不能为空！'); return;
            }
          }

          if (state.model.atgType == '固定考勤' && state.model.selectedFixedClassData.length == 0) {
            ElMessage.error('工作日班次设置不能为空！'); return;
          }
          if (state.model.atgType == '自由考勤' && state.model.selectedFreeClassData.length == 0) {
            ElMessage.error('工作日班次设置不能为空！'); return;
          }
          let rt = true;
          state.model.selectedFixedClassData.forEach((item) => {
            if (!item["atcId"]) {
              rt = false;
            }
          })
          if (!rt) {
            ElMessage.error('工作日班次信息不能为空！'); return;
          }
          try {
            btnLoading.value = true;
            const fn = props.data?.atgId ? updateAttentGroup : addAttentGroup;
            /*
             atgName
             */
            state.model.atgManageUser = state.model.manageUser.userId;  //管理员
            state.model["attendUserIdList"] = state.model.attendUserList.map(item => { return item.userId }); //考勤人员
            state.model["attendDeptIdList"] = state.model.attendDeptIdList.map(item => { return Number(item.id) }); //考勤组织机构
            if (state.model.atgType == '固定考勤') {
              state.model["attendClassList"] = state.model.selectedFixedClassData.map(item => { return { atgDay: (item.atgDay == 7 ? 0 : item.atgDay), atcId: item.atcId, atgSet: 1 } });
            } else {
              state.model["attendClassList"] = state.model.selectedFreeClassData.map(item => { return { atgDay: (item.atgDay == 7 ? 0 : item.atgDay), atgSet: 1 } });
            }
            const { message, code } = await fn(state.model);
            if (code === 0) {
              ElMessage.success(message);
            } else {
              ElMessage.error(message);
            }
            context.emit("update:modelValue", true);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      attrs["width"] = "1200px";
      return attrs;
    });

    watch(
      () => props.modelValue,
      async (n) => {
        if (n) {
          if (props.data?.atgId) {
            const { code, message, data } = await getAttentionGroupDetail(props.data.atgId);
            if (code != 0) {
              ElMessage.error(message); return;
            }
            data["manageUser"] = { userId: data.atgManageUser, userNum: data.userNum, userName: data.userName };
            data["attendUserList"] = data.attendUsers ? data.attendUsers.map(item => { return { userId: item.userId, userNum: item.userNum, userName: item.userName } }) : [];
            data["attendDeptIdList"] = data.attendDepts ? data.attendDepts.map(item => { return { deptName: item.deptName, id: Number(item.deptId) } }) : [];
            let objbase = getDefaultModel();
            if (data.atgType == '固定考勤') {
              objbase.selectedFixedClassData = new Array();
              objbase.fixedClassList.forEach((item) => {
                let tempDay = item.atgDay == 7 ? 0 : item.atgDay;
                let tempObj = data.attendClassList.filter(sitem => {
                  return sitem.atgDay == tempDay;
                });
                if (tempObj.length > 0) {
                  objbase.selectedFixedClassData.push(item);
                  item["atcId"] = tempObj[0].atcId;
                  item["atcClassname"] = tempObj[0].atcClassname;
                  item["atcTimes"] = tempObj[0].atcTimes;
                }
              });
            }
            state.model = Object.assign(objbase, data);
            state.model.atgHoliday = state.model.atgHoliday.toString();

            if (data.atgType == '固定考勤') {
              state.model.fixedClassList.forEach((item) => {
                let tempDay = item.atgDay == 7 ? 0 : item.atgDay;
                let tempObj = data.attendClassList.filter(sitem => {
                  return sitem.atgDay == tempDay;
                });
                if (tempObj.length > 0) {
                  nextTick(() => {
                    fixed_tableRef.value.toggleRowSelection(item, true)
                  });
                }
              });
            } else {
              state.model.selectedFreeClassData = new Array();
              nextTick(() => {
                state.model.freeClassList.forEach((item) => {
                  let tempDay = item.atgDay == 7 ? 0 : item.atgDay;
                  let tempObj = data.attendClassList.filter(sitem => {
                    return sitem.atgDay == tempDay
                  });
                  if (tempObj.length > 0) {
                    state.model.selectedFreeClassData.push(item);
                    nextTick(() => {
                      free_tableRef.value.toggleRowSelection(item, true)
                    });
                  }
                });
              });
            }
          } else {
            state.model = getDefaultModel();
          }

          state.disType = props.type;
          // console.log("初始化数据：" , JSON.stringify(state.model))
        }
      }
    );
    return {
      strategyList,
      weekDays,
      attrs,
      update,
      formRef,
      cancel,
      submit,
      free_tableRef,
      fixed_tableRef,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      // disType,
      btnLoading,
      themes: THEMEVARS,
      delDept,
      showAllFixedClass,
      editFreeClass,
      selectedClassAllClose,
      selectedUsersClose,
      selectedDeptClose,
      selectedManagerUserClose,
      handleSelectionFreeChange,
      handleSelectionFixedChange,
    };
  },
};
</script>
