<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="选择人员" prop="atgUsers" v-if="state.model.disType!='info' && state.model.disType!='exam'">
        <div class="divSelectUser">
          <el-tag class="ml-2" type="success" v-if="state.model.manageUser">{{state.model.manageUser.userNum + ' ' + state.model.manageUser.userName}}</el-tag>
        </div>
        <el-button type="primary" link @click="state.model.isShowSelectedManagerUser = true;">选择</el-button>
      </el-form-item>

      <el-row :gutter="5" v-if="state.model.disType=='info' || state.model.disType=='exam'">
        <el-col :sm="12">
          <el-form-item label="用户编号">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.userNum : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="用户名称">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.userName : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="5" v-if="state.model.disType=='info'">
        <el-col :sm="12">
          <el-form-item label="组织机构">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.orgNameList : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="所属考勤组">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.atgName : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0">
        <el-col :sm="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker v-model="state.model.startDate" type="date" :readonly="state.model.disType=='info' || state.model.disType=='exam'" placeholder="选择开始日期"  format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="false">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker v-model="state.model.endDate" type="date" :readonly="state.model.disType=='info' || state.model.disType=='exam'" placeholder="选择结束日期"   format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0">
        <el-col :sm="12">
          <el-form-item label="开始时间" prop="leaStarttime">
            <el-time-picker v-model="state.model.leaStarttime" :readonly="state.model.disType=='info' || state.model.disType=='exam'" placeholder="请选择" format="HH:mm" value-format="HH:mm"></el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="结束时间" prop="leaEndtime">
            <el-time-picker v-model="state.model.leaEndtime" :readonly="state.model.disType=='info' || state.model.disType=='exam'" placeholder="请选择" format="HH:mm" value-format="HH:mm"></el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="加班原因" prop="ovaReson">
        <el-input v-model="state.model.ovaReson" :readonly="state.model.disType=='info' || state.model.disType=='exam'" :rows="5" type="textarea" placeholder="请输入补录原因" />
      </el-form-item>

      <el-form-item label="是否自动审核" prop="atoExam" v-if="state.model.disType!='info' && state.model.disType!='exam'">
        <el-switch v-model="state.model.atoExam" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                   inactive-value="0" inactive-text="关" active-text="开">
        </el-switch>
      </el-form-item>

      <el-row :gutter="0"  v-if="state.model.disType == 'info' || state.model.disType=='exam'">
        <el-col :sm="12">
          <el-form-item label="审核方式">
            <el-input :modelValue="state.model.atoExam == '1' ? '自动审核' : '领导审核'"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="提交时间">
            <el-input v-model="state.model.submitDate"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0"  v-if="state.model.disType=='info'">
        <el-col :sm="24">
          <el-row :gutter="0">
            <el-col :sm="24">
              审核信息
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :sm="12">
              <el-form-item label="审核结果">
                <el-input :modelValue="constExameList[state.model.exmResult]" />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item label="审核时间" prop="atoExam">
                <el-input v-model="state.model.exmDate"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :sm="24">
              <el-form-item label="未通过原因" prop="exmDesc" v-if="state.model.exmResult == 2">
                <el-input v-model="state.model.exmDesc" :readonly="state.model.disType=='info'" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <el-row :gutter="0"  v-if="state.model.disType=='exam'">
        <el-col :sm="24">
          <el-row :gutter="0">
            <el-col :sm="24">
              审核
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :sm="24">
              <el-form-item label="是否通过">
                <el-radio-group v-model="state.model.exmResult" class="ml-4">
                  <el-radio label="1" size="large">通过</el-radio>
                  <el-radio label="2" size="large">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="未通过原因" prop="exmDesc" v-if="state.model.exmResult == 2">
                <el-input v-model="state.model.exmDesc" :readonly="state.model.disType=='info'" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

    </el-form>
    <template #footer v-if="state.model.disType!='info'">
      <p style="text-align: center">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
    <selected-manageuser :modelValue="state.model.isShowSelectedManagerUser" :title="'选择人员'" @update:modelValue="selectedManagerUserClose" />
  </el-dialog>
</template>
<script>
  import { reactive, watch , ref , computed , onMounted} from "vue";
  import {
    // getAttentionOvertimeAddInfo ,
    examAttentionOvertimeAddInfo ,
    // delAttentionOvertimeAddInfo ,
    addAttentionOvertimeAddInfo ,
    updateAttentionOvertimeAddInfo ,
    getAttentionOvertimeAddInfoDetail
  } from "@/applications/eccard-ams/api";
  import { dateStr ,hourMinStr ,timeStr } from "@/utils/date.js"
  import selectedManageuser from "@/applications/eccard-ams/views/attendaddinfo/components/selectedmanageuser.vue"
  import {
    ElRow,
    ElCol,
    ElTag,
    ElSwitch,
    // ElLink,
    // ElSelect,
    // ElOption,
    ElTimePicker,
    ElInput,
    ElForm,
    ElFormItem,
    // ElTable,
    // ElTableColumn,
    ElButton,
    // ElPagination,
    ElMessage,
    ElDatePicker,
    // ElMessageBox,
    // ElCheckboxGroup,
    // ElCheckbox,
    ElRadioGroup,
    ElRadio,
    ElDialog,
  } from 'element-plus';

  const getDefaultModel = () => ({
    manageUser:null,
    isShowSelectedManagerUser:false,
    atgType:'',
    atoExam:0,
    disType:'info',
  });
  export default {
    emits: ["close"],
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      data: {
        type: Object,
        default: () => ({}),
      },
      type :{
        type: String,
        default: "",
      },
    },
    components: {
      selectedManageuser,
      ElTimePicker,
      ElRow,
      // ElLink,
      ElCol,
      ElTag,
      ElSwitch,
      ElDatePicker,
      // ElSelect,
      // ElOption,
      // ElCheckboxGroup,
      ElRadioGroup,
      ElRadio,
      // ElCheckbox,
      ElInput,
      ElForm,
      ElFormItem,
      // ElTable,
      // ElTableColumn,
      ElButton,
      // selectedClassAll,
      ElDialog,
    },
    setup(props, context) {
      const formRef = ref(null);
      const btnLoading = ref(false);
      const constExameList = ref(["待审核" , "已通过" , "未通过"]);
      const state = reactive({
        model: getDefaultModel(),
      });
      const rules = {
        startDate: [
          { required: true, message: "请输入开始日期" }
        ],
        // endDate: [
        //   { required: true, message: "请输入结束日期" }
        // ],
        leaStarttime: [
          { required: true, message: "请输入开始时间" }
        ],
        leaEndtime: [
          { required: true, message: "请输入开结束时间" }
        ],
        ovaReson: [
          { required: true, message: "请输入加班原因" },
          { max: 50, message: "考勤组名称不能超过50个字符" },
        ],
      };
      const selectedManagerUserClose = (val)=>{
        if(val){
          console.log(JSON.stringify(val))
          state.model.manageUser = {};
          state.model.manageUser["userId"] = val.userId;
          state.model.manageUser["userNum"] = val.userNum;
          state.model.manageUser["userName"] = val.userName;
          state.model.manageUser["atgId"] = val.atgId;
          state.model.manageUser["atgName"] = val.atgName;
          state.model.manageUser["orgNameList"] = val.orgNameList;
        }

        state.model.isShowSelectedManagerUser = false;
      };
      const cancel = () => {
        formRef.value?.resetFields();
        context.emit("update:modelValue", false);
      };
      const checkStr = (substring)=> {
        if(substring){
          var reg = new RegExp("[~#^$@%&!?%*]", 'g');
          if (substring.match(reg)) {
            return true;
          }
          for ( var i = 0; i < substring.length; i++) {
            var hs = substring.charCodeAt(i);
            if (0xd800 <= hs && hs <= 0xdbff) {
              if (substring.length > 1) {
                let ls = substring.charCodeAt(i + 1);
                let uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
                if (0x1d000 <= uc && uc <= 0x1f77f) {
                  return true;
                }
              }
            } else if (substring.length > 1) {
              let ls = substring.charCodeAt(i + 1);
              if (ls == 0x20e3) {
                return true;
              }
            } else {
              if (0x2100 <= hs && hs <= 0x27ff) {
                return true;
              } else if (0x2B05 <= hs && hs <= 0x2b07) {
                return true;
              } else if (0x2934 <= hs && hs <= 0x2935) {
                return true;
              } else if (0x3297 <= hs && hs <= 0x3299) {
                return true;
              } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030
                || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b
                || hs == 0x2b50) {
                return true;
              }
            }
          }
        }
      };
      const submit = async () => {
        if(checkStr(state.model.ovaReson)){
          ElMessage.error('加班原因不能包含特殊字符和表情图标！');
          return;
        }
        if(checkStr(state.model.exmDesc)){
          ElMessage.error('审核原因不能包含特殊字符和表情图标！');
          return;
        }
        state.model.endDate = state.model.startDate;
        if (state.model.disType == 'exam') {
          if (state.model.exmResult == '2' && (!state.model.exmDesc || state.model.exmDesc.toString().trim() == '')) {
            ElMessage.error('审核描述信息不能为空！');
            return;
          }
          try {
            btnLoading.value = true;
            const {message, code} = await examAttentionOvertimeAddInfo(state.model);
            if (code === 0) {
              ElMessage.success(message);
            } else {
              ElMessage.error(message);
            }
            context.emit("update:modelValue", true);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        } else {
          formRef.value?.validate(async (valid) => {
            if (valid) {
              if (!state.model.manageUser) {
                ElMessage.error('加班补录人员信息不能为空！');
                return;
              }
              try {
                btnLoading.value = true;
                const fn = props.data?.ovaId ? updateAttentionOvertimeAddInfo : addAttentionOvertimeAddInfo;
                state.model["userId"] = state.model.manageUser.userId;  //管理员

                let constObjs = Object.assign({} , state.model);

                constObjs["startDate"] = dateStr(constObjs["startDate"]);
                constObjs["endDate"] = dateStr(constObjs["endDate"]);
                let str1 = constObjs["leaStarttime"];
                let str2 = constObjs["leaEndtime"];

                constObjs["leaStarttime"] = hourMinStr(constObjs["leaStarttime"]);
                constObjs["leaEndtime"] = hourMinStr(constObjs["leaEndtime"]);

                if(constObjs["leaEndtime"] < constObjs["leaStarttime"]){
                  ElMessage.error('补录结束日期需大于或等于开始日期！');
                  return;
                }

                if(constObjs["atoExam"] == '1') {
                  constObjs["ovaExamType"] = "自动审核";
                }else{
                  constObjs["ovaExamType"] = "领导审核";
                }
                constObjs["submitDate"] = constObjs["submitDate"] ? new Date(constObjs["submitDate"]) : null;
                constObjs["exmDate"] = constObjs["exmDate"] ? new Date(constObjs["exmDate"]) : null;
                const {message, code} = await fn(constObjs);
                if (code === 0) {
                  ElMessage.success(message);
                } else {
                  constObjs["leaStarttime"] = str1;
                  constObjs["leaEndtime"] = str2;
                  ElMessage.error(message);
                }
                context.emit("update:modelValue", true);
              } catch (e) {
                throw new Error(e.message);
              } finally {
                btnLoading.value = false;
              }
            }
          });
        }
      };
      const attrs = computed(() => {
        // eslint-disable-next-line no-unused-vars
        const { modelValue, role, ...attrs } = props;
        return attrs;
      });

      onMounted(() => {
      });

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            if (props.data?.ovaId) {
              const { code, message , data } = await getAttentionOvertimeAddInfoDetail(props.data.ovaId);
              if(code != 0){
                ElMessage.error(message);return;
              }
              let objbase = getDefaultModel();
              data["manageUser"] = {userId:data.userId , userNum:data.userNum , userName:data.userName , atgId:data.atgId , atgName : data.atgName , orgNameList:data.orgNameList};

              data["startDate"] = new Date(data.startDate);
              data["endDate"] = new Date(data.endDate);
              data["leaStarttime"] = new Date(2022, 11, 4, parseInt(data.leaStarttime.toString().substr(0 , 2)), parseInt(data.leaStarttime.toString().substr(3 , 2)));
              data["leaEndtime"] = new Date(2022, 11, 4, parseInt(data.leaEndtime.toString().substr(0 , 2)), parseInt(data.leaEndtime.toString().substr(3 , 2)));

              if(data["exmDate"]) {
                data["exmDate"] = timeStr(data["exmDate"]);
              }
              if(data["submitDate"]) {
                data["submitDate"] = timeStr(data["submitDate"]);
              }
              state.model = Object.assign(objbase, data);
            } else {
              state.model = getDefaultModel();
            }

            state.model.disType = props.type;
          }
        }
      );
      return {
        attrs,
        rules,
        formRef,
        cancel,
        submit,
        labelWidth: THEMEVARS.formLabelWidth,
        state,
        btnLoading,
        themes: THEMEVARS,
        // editFreeClass,
        // selectedClassAllClose,
        // selectedUsersClose,
        constExameList,
        selectedManagerUserClose,
        checkStr,
      };
    },
  };
</script>
