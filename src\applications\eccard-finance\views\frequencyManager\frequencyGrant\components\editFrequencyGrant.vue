<template>
  <div>
    <el-dialog :model-value="isEdit" :title="'编辑次数发放'" width="90%" :before-close="beforeClose"
      :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" ref="ruleForm" :model="state.form" :rules="state.rules" label-width="120px">
            <el-form-item label="次数类型:" prop="frequencyType">
              <el-select clearable v-model="state.form.frequencyType" placeholder="请选择">
                <el-option v-for="(item, index) in frequencyTypeList" :key="index" :label="item.ftName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="交易类型:" prop="costType">
              <el-select clearable disabled v-model="state.form.costType" placeholder="请选择">
                <el-option v-for="(item, index) in state.tradeTypeList" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="120px">
            <el-form-item label="发放说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.grantRemark"></el-input>
            </el-form-item>
            <el-form-item label="发放方式:" prop="grantType">
              <el-radio-group v-model="state.grantType">
                <el-radio label="SYSTEM">发放清单</el-radio>
                <el-radio label="IMPORT">导入清单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <template v-if="state.grantType == 'SYSTEM'">
            <el-form inline ref="generateFrequency" :model="state.generateFrequencyForm" :rules="state.generateFrequencyRule"
              size="small" label-width="120px">
              <el-form-item label=" 身份类别:" prop="userRole">
                <el-select v-model="state.generateFrequencyForm.userRole" placeholder="请选择">
                  <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="生效时间:" prop="takeEffectTime">
                <el-date-picker v-model="state.generateFrequencyForm.takeEffectTime" type="date" placeholder="请选择日期"
                  @change="changeDate">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="每人次数金额:" prop="frequencyAmount">
                <el-input placeholder="请输入" v-model="state.generateFrequencyForm.frequencyAmount"></el-input>
              </el-form-item>
              <el-form-item label="有效期至:" prop="invalidTime">
                <el-date-picker v-model="state.generateFrequencyForm.invalidTime" type="date" placeholder="请选择日期"
                  @change="changeDate">
                </el-date-picker>
              </el-form-item>
            </el-form>
          </template>
          <div class="select-input-lang" v-if="state.grantType == 'SYSTEM'">
            <el-form inline size="small" label-width="120px">
              <el-form-item label=" 组织机构:">
                <kade-dept-select-tree style="width: 100%" :value="state.generateFrequencyForm.deptPaths"
                  valueKey="deptPath" dataKey="deptPath" :multiple="true" @valueChange="
                    (val) => (state.generateFrequencyForm.deptPaths = val)
                  " />
              </el-form-item>

              <el-button size="small" type="primary" @click="defineGenerateListClick()">确认生成清单</el-button>
              <el-button size="small" @click="state.generateFrequencyForm = {}">重置</el-button>
            </el-form>
          </div>
          <el-form v-show="state.grantType == 'IMPORT'" inline size="small" label-width="100px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-success="handleSuccess"
                :on-remove="handleRemove" :limit="3" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="blue" @click="uploadSample()">下载样例</div>
            </el-form-item>
          </el-form>
          <div v-if="state.abnormalData > 0">
            <span class="red">•</span> 异常数据
            {{ state.abnormalData }} 条，请修改确认
          </div>
<!--           <el-table v-if="state.grantType == 'SYSTEM'" style="width: 100%" :data="state.WaitFrequencyList"
            v-loading="state.WaitFrequencyLoading" border stripe>
            <el-table-column label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="
                  scope.row.verifyResult != '成功' && { 'color': '#f00' }
                ">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <el-button type="text" @click="delClick(scope.row, scope.$index)" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table> -->
          <el-table style="width: 100%" :data="state.WaitFrequencyList"
            v-loading="state.WaitFrequencyLoading" border stripe>
            <el-table-column  show-overflow-tooltip label="校检结果" prop="verifyResult" align="center">
              <template #default="scope">
                <div :style="
                  scope.row.verifyResult != '成功' && { 'color': '#f00' }
                ">
                  {{ scope.row.verifyResult }}
                </div>
              </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="发放金额（元）" prop="frequencyAmount" align="center"></el-table-column>
            <el-table-column  show-overflow-tooltip label="有效期至" prop="invalidTime" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <el-button type="text" @click="delClick(scope.row, scope.$index)" size="mini">编辑</el-button>
                <el-button type="text" @click="delClick(scope.row, scope.$index)" size="mini">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box" v-if="state.grantType">
            <div>
              <span>合计：<span>{{ state.totalCount }}</span>人，<span>{{ state.totalAmount }}</span>元</span>
            </div>
            <div class="pagination">
              <el-pagination background :current-page="state.WaitFrequencyForm.currentPage"
                :page-size="state.WaitFrequencyForm.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :page-sizes="[6, 10, 20, 50, 100]" :total="state.WaitFrequencyListTotal"
                @current-change="handlePageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitForm()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { getToken } from "@/utils";
import { timeStr } from "@/utils/date.js";
import {
  editFrequencyProject,
  delUpdateWaitFrequency,
  getFrequencyListByPage,
  generateFrequencyList,
  getWaitFrequencyListByPage,
  deleteWaitFrequency,
  exportFrequencyList,
} from "@/applications/eccard-finance/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElRadio,
  ElRadioGroup,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElDatePicker,
  ElMessage,
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";

export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElRadio,
    ElRadioGroup,
    ElUpload,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,

  },
  props: {},
  setup(props, context) {
    const store = useStore();
    const uploadDom = ref(null);
    let generateFrequency = ref(null);
    let ruleForm = ref(null);
    const state = reactive({
      isEdit: false,
      WaitFrequencyLoading: false,
      isNewUpload: 1, //1 未新发放清单  2生成清单  3上传清单
      grantType: "",
      form: {},
      generateFrequencyForm: {},
      rules: {
        frequencyType: [
          {
            required: true,
            message: "请选择次数类型",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        costType: [
          {
            required: true,
            message: "请选择交易类型",
            trigger: "change",
          },
        ],
        grantType: [
          {
            required: true,
            message: "请选择发放方式",
            trigger: "change",
          },
        ],
      },
      generateFrequencyRule: {
        userRole: [
          {
            required: true,
            message: "请选择身份类别",
            trigger: "change",
          },
        ],
        takeEffectTime: [
          {
            required: true,
            message: "请选择生效时间",
            trigger: "change",
          },
        ],
        frequencyAmount: [
          {
            required: true,
            message: "请输入次数金额",
            trigger: "blur",
          },
        ],
        invalidTime: [
          {
            required: true,
            message: "请选择到期时间",
            trigger: "change",
          },
        ],
      },
      WaitFrequencyForm: {
        currentPage: 1,
        pageSize: 6,
      },
      totalAmount: 0,
      totalCount: 0,
      abnormalData: 0,
      WaitFrequencyList: [],
      WaitFrequencyListTotal: 0,
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/frequencyRecord/importFrequencyList`,
      roleList: [],
      departCheckList: [],
      frequencyTypeList: [],
      tradeTypeList: [
        { label: "充值", value: 101 },
      ],
    });

    const isEdit = computed(() => {
      return store.state.frequencyData.isEdit;
    });

    const frequencyTypeList = computed(() => {
      return store.state.frequencyData.frequencyTypeList;
    });
    const roleList = computed(() => {
      return store.state.frequencyData.roleList;
    });
    const departCheckList = computed(() => {
      return store.state.frequencyData.departCheckList;
    });

    watch(
      () => state.grantType,
      () => {
        if (state.isNewUpload !== 1) {
          state.WaitFrequencyList = [];
          state.generateFrequencyForm = {};
          state.WaitFrequencyForm = {
            currentPage: 1,
            pageSize: 6,
          };
          uploadDom.value.clearFiles();
          state.WaitFrequencyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.abnormalData = 0;
        }
      }
    );

    watch(
      () => store.state.frequencyData.isEdit,
      (val) => {
        if (val) {
          let data = store.state.frequencyData.selectRow;
          let {
            frequencyType,
            projectName,
            costType,
            grantRemark,
            grantMode,
            takeEffectTime,
            totalAmount,
            invalidTime,
            userRole,
            totalPerson,
            id,
          } = { ...data };

          state.grantType = grantMode;
          state.form = {
            frequencyType,
            projectName,
            costType,
            grantRemark,
            id
          };
          if (state.grantType == "IMPORT") {
            state.generateFrequencyForm = {};
          } else {
            state.generateFrequencyForm = {
              takeEffectTime: timeStr(takeEffectTime),
              frequencyAmount: totalAmount / totalPerson,
              invalidTime: timeStr(invalidTime),
              userRole,
              id,
            };
          }
          state.WaitFrequencyForm.projectId = id;

          state.totalCount = totalPerson;
          state.totalAmount = totalAmount;
          queryFrequencyListByPage();
        }
      }
    );

    //上传成功
    const handleSuccess = (res) => {
      let { totalCount, totalAmount, errorCount } = res.data;
      state.isNewUpload = 3;
      state.WaitFrequencyForm.projectNo = res.data.projectNo;
      state.form.projectNo = res.data.projectNo;
      state.totalCount = totalCount;
      state.totalAmount = totalAmount;
      state.abnormalData = errorCount;
      queryWaitFrequencyListByPage();
    };

    //清除上传
    const handleRemove = () => {
      state.WaitFrequencyList = [];
      state.WaitFrequencyListTotal = 0;
    };
    const cascaderChange = (val) => {
      console.log(val);
      console.log(state.generateFrequencyForm.deptPaths);
      if (val.length) {
        let arr = val.map(item => {
          return item.length && item[item.length - 1]
        })
        state.generateFrequencyForm.deptPaths = arr
      } else {
        state.generateFrequencyForm.deptPaths = []
      }
    };
    //生成名单编号
    const defineGenerateListClick = () => {
      generateFrequency.value.validate((valid) => {
        if (valid) {
          let params = { ...state.generateFrequencyForm }
          params.takeEffectTime = timeStr(params.takeEffectTime);
          params.invalidTime = timeStr(params.invalidTime);
          params.frequencyAmount = Number(params.frequencyAmount);

          generateFrequencyList(state.generateFrequencyForm).then((res) => {
            if (res.code === 0) {
              state.isNewUpload = 2;
              state.WaitFrequencyForm.projectNo = res.data.projectNo;
              state.form.projectNo = res.data.projectNo;
              state.totalAmount = res.data.totalAmount;
              state.totalCount = res.data.totalCount;
              state.abnormalData = res.data.errorCount;
              queryWaitFrequencyListByPage();
            } else {
              ElMessage.error(res.message);
            }
          });
        } else {
          return false;
        }
      });
    };

    //获取未入库名单
    const queryWaitFrequencyListByPage = () => {
      state.WaitFrequencyLoading = true;
      getWaitFrequencyListByPage(state.WaitFrequencyForm)
        .then(({ data }) => {
          let { generateFrequency, pageInfo } = data;
          state.totalCount = generateFrequency.totalCount;
          state.totalAmount = generateFrequency.totalAmount;
          state.abnormalData = generateFrequency.errorCount;
          state.WaitFrequencyList = pageInfo;
          state.WaitFrequencyListTotal = generateFrequency.totalCount;
          state.WaitFrequencyLoading = false;
        })
        .catch(() => { });
      state.WaitFrequencyLoading = false;
    };

    //获取已入库名单
    const queryFrequencyListByPage = () => {
      state.WaitFrequencyLoading = true;
      getFrequencyListByPage(state.WaitFrequencyForm)
        .then((res) => {
          state.WaitFrequencyList = res.data.pageInfo.list;
          state.WaitFrequencyListTotal = res.data.pageInfo.total;
          state.generateFrequencyForm.userRole = res.data.role ? Number(res.data.role) : "";
          state.generateFrequencyForm.deptPaths = res.data.dept;
          state.totalCount = res.data.generateFrequency.totalCount;
          state.totalAmount = res.data.generateFrequency.totalAmount;
          state.abnormalData = res.data.generateFrequency.errorCount;
          state.WaitFrequencyLoading = false;
        })
        .catch(() => { });
      state.WaitFrequencyLoading = false;
    };

    const delClick = (val) => {
      if (state.isNewUpload === 1) {
        delUpdateWaitFrequency(val.subSidyRecordId).then(({ code, message }) => {
          if (code === 0) {
            queryFrequencyListByPage();
          } else {
            ElMessage.error(message);
          }
        });
      } else if (state.isNewUpload === 2 || state.isNewUpload === 3) {
        let data = {
          projectNo: state.form.projectNo,
          id: parseInt(val.id),
        };
        deleteWaitFrequency(data).then(({ code, message }) => {
          if (code === 0) {
            queryWaitFrequencyListByPage();
          } else {
            ElMessage.error(message);
          }
        });
      }
    };
    const uploadSample = async () => {
      let res = await exportFrequencyList();
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "导入次数清单样例.xlsx");
      document.body.appendChild(link);
      link.click();
    };
    const submitForm = async () => {
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          /*           data.takeEffectTime = timeStr(data.takeEffectTime);
                    data.invalidTime = timeStr(data.invalidTime); */
          state.form.grantMode = state.grantType;
          let data = {}
          if (state.form.grantMode == "SYSTEM") {
            let params = { ...state.generateFrequencyForm }
            params.takeEffectTime = timeStr(params.takeEffectTime);
            params.invalidTime = timeStr(params.invalidTime);
            params.frequencyAmount = Number(params.frequencyAmount);
            data = { ...state.form, ...params };
          } else {
            data = { ...state.form };
          }
          if (state.isNewUpload === 1) {
            console.log(data);
          } else {
            if (!state.form.projectNo) {
              return ElMessage.error("请先生成或导入次数清单！");
            }
          }
          let { code, message } = await editFrequencyProject(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("offEdit", true);
            state.isNewUpload = 1
          } else {
            ElMessage.error(message);
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      store.commit("frequencyData/updateState", {
        key: "isEdit",
        payload: false,
      });
      context.emit("offEdit", false);
    };

    const handlePageChange = (val) => {
      state.WaitFrequencyForm.currentPage = val;
      queryFrequencyListByPage();
    };
    const handleSizeChange = (val) => {
      state.WaitFrequencyForm.currentPage = 1;
      state.WaitFrequencyForm.pageSize = val;
      queryFrequencyListByPage();
    };

    onMounted(() => { });
    return {
      state,
      uploadDom,
      isEdit,
      frequencyTypeList,
      roleList,
      departCheckList,
      generateFrequency,
      defineGenerateListClick,
      ruleForm,
      submitForm,
      beforeClose,
      uploadSample,
      handleRemove,
      handleSuccess,
      delClick,
      cascaderChange,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.select-input-lang {
  .el-select {
    width: 500px;
  }
}

.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
