<template>
  <div style="height: auto" class="padding-box">
    <el-row :gutter="5">
      <el-col :sm="12">
        <el-card class="box-card">
          <template #header>
            每日汇总列表
          </template>
          <el-row :gutter="5">
            <el-col :sm="24">
              <p style="margin:10px auto 0px 10px;">
                <el-form inline size="mini">
                  <el-form-item label="组织机构">
                    <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath"
                      :multiple="false" @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
                  </el-form-item>
                  <el-form-item label="关键字">
                    <el-input v-model="state.form.keyWord" placeholder="请输入姓名或编号搜索" :clearable="true"></el-input>
                  </el-form-item>
                  <el-form-item label="所属考勤组">
                    <el-select v-model="state.form.atgId" :clearable="true">
                      <el-option v-for="(item, index) in state.groupList" :key="index" :label="item.atgName"
                        :value="item.atgId"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="reset">重置</el-button>
                  </el-form-item>
                </el-form>
              </p>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :sm="24">
              <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading"
                highlight-current-row height="55vh" @current-change="handleSelectionChange" border stripe>
                <el-table-column label="所属考勤组" prop="atgName" align="center" show-overflow-tooltip></el-table-column>
                <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
                <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
                <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
              </el-table>
              <div class="pagination">
                <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
                  layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
                  @current-change="handlePageChange" @size-change="handleSizeChange">
                </el-pagination>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :sm="12">
        <el-card class="box-card">
          <el-row :gutter="5">
            <el-col :sm="24">
              <el-calendar v-model="state.curDate">
                <template #dateCell="{ data }">
                  <div :class="data.isSelected ? 'is-selected' : ''" class="calendar-item">
                    <div>
                      {{ data.day.split('-').slice(1).join('-') }}
                      {{ data.isSelected ? '✔️' : '' }}
                    </div>
                    <div class="status" :style="{ 'background': statusSelect(data.day) }"></div>
                  </div>
                </template>
              </el-calendar>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :sm="24">
              <el-row :gutter="5" style="margin:5px">
                <el-col :sm="24">
                  {{ getDateStr(state.curDate) }}
                </el-col>
              </el-row>
              <div v-if="state.detailList.length > 0">
                <el-row :gutter="5">
                  <el-col style="margin-top: 20px;" :span="12" v-for="(item, index) in state.detailList"
                    :key="index + 'eee'">
                    <div class="msg">
                      <span :style="'background:' + colorFnc(item.atiAnalyResult).color" class="d"></span>
                      <span class="text" :style="'color:' + colorFnc(item.atiAnalyResult).color">{{
                        colorFnc(item.atiAnalyResult).text }}</span>
                      <span>{{ hourMinStr(item.atiCheckDate) }}打卡</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div v-else class="none">暂无数据</div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { reactive, watch, onMounted } from "vue";
import { dateStr, hourMinStr, getMonthFirst, getMonthLast } from "@/utils/date.js"
import {
  ElRow,
  ElCol,
  // ElTag,
  ElSelect,
  ElOption,
  ElCalendar,
  ElCard,
  ElPagination,
  ElInput,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessage,
} from 'element-plus';
import { getAttentionUserAttendPage, getAttentionGroupInfoPageAll, getAttentionInfoPage } from "@/applications/eccard-ams/api";
import deptSelectTree from '@/components/tree/deptSelectTree'
const colorFnc = (val) => {
  if (val == 0) {
    return {
      text: "正常",
      color: "#53c122"
    }
  } else if (val == 1) {
    return {
      text: "迟到",
      color: "#e6a23c"
    }
  } else if (val == 2) {
    return {
      text: "旷工",
      color: "#e91010"
    }
  } else if (val == 3) {
    return {
      text: "早退",
      color: "#e9e710"
    }
  } else {
    return {
      text: "异常",
      color: "#f00"
    }
  }
}
export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElRow,
    ElCol,
    // ElTag,
    ElSelect,
    ElOption,
    ElCalendar,
    ElCard,
    ElPagination,
    ElInput,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      groupList: [],
      form: {
        orgNameList: '',
        keyWord: '',
        atgId: '',
        userDisType: 2,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: "",
      type: "",
      columnList: [],
      selectData: '',
      curDate: new Date(),
      detailList: [],
      detailType: '',
      AttentionInfoList: [],
    });
    watch(
      () => state.curDate,
      (val, oldVal) => {
        let month1 = new Date(val).getMonth()
        let month2 = new Date(oldVal).getMonth()
        if (month1 !== month2) {
          refreshData();
        }
        getDetailList()
      }
    );
    //分页
    const getList = async () => {
      state.loading = true
      let { data } = await getAttentionUserAttendPage(state.form)
      state.dataList = data.list
      state.total = parseInt(data.count)
      state.loading = false;
    }
    const edit = (row, type) => {
      state.type = type
      state.rowData = row
      state.isEdit = true
    }
    const reset = () => {
      state.form = {
        userDisType: 2,
        currentPage: 1,
        pageSize: 10000
      }
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageSize = val
      getList()
    }
    const getcurdatetime = (val) => {
      if (!val) {
        return "";
      }
      return hourMinStr(val);
    };
    const statusSelect = (date) => {
      console.log(date, "date");
      let list = state.AttentionInfoList.filter(item => item.checkDate === dateStr(date)).map(item => item.atiAnalyResult)
      if (list.includes('1') || list.includes('2') || list.includes('3')||list.includes(null)) {
        return "#f00"
      } else if (!list.length) {
        return "#ccc"
      } else {
        return "#53c122"
      }

    }
    const getDetailList = () => {
      state.detailList = state.AttentionInfoList.filter(item => item.checkDate === dateStr(state.curDate))
    }
    const refreshData = async () => {
      if (!state.selectData.userId) return
      let params = {
        userId: state.selectData.userId,
        atiStarttime: getMonthFirst(state.curDate),
        atiEndtime: getMonthLast(state.curDate),
        currentPage: 1,
        pageSize: 1000
      }
      const { code, message, data } = await getAttentionInfoPage(params);
      if (code != 0) {
        ElMessage.error(message); return;
      }
      state.AttentionInfoList = data.list.map(item => {
        return {
          ...item,
          checkDate: dateStr(item.atiCheckDate)
        }
      })
      getDetailList()
    }
    const getDateStr = (date) => {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? ('0' + m) : m;
      var d = date.getDate();
      d = d < 10 ? ('0' + d) : d;
      return y + '-' + m + '-' + d;
    }
    const search = () => {
      getList()
    }
    const handleSelectionChange = (val) => {
      state.selectData = val;
      refreshData();
    };
    onMounted(async () => {
      getList();
      let { data } = await getAttentionGroupInfoPageAll();
      state.groupList = data;
    })

    return {
      hourMinStr,
      colorFnc,
      state,
      edit,
      reset,
      search,
      handleSelectionChange,
      getDateStr,
      getcurdatetime,
      statusSelect,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.calendar-item {
  position:relative;
}

.status {
  position: absolute;
  top:3px;
  right:3px;
  width: 12px;
  height: 12px;
  border-radius: 12px;
}

.msg {
  margin-left: 20px;
  display: flex;
  align-items: center;

  .d {
    width: 12px;
    height: 12px;
    border-radius: 6px;
  }

  .text {
    margin: 0 20px 0 5px;
  }
}

.none {
  margin-top: 20px;
  color: #8a8a8a;
  text-align: center;
}
</style>
