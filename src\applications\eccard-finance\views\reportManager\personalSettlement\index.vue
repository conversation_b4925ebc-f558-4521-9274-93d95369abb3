<template>
  <!-- 个人账户结算 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="结算日期:">
          <el-date-picker v-model="state.requestDate" type="daterange" unlink-panels range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用户编号:">
          <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名:">
          <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
        </el-form-item>
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="账户钱包:">
          <el-select clearable multiple v-model="state.form.walletCode" placeholder="请选择">
            <el-option v-for="(item, index) in state.allWalletList" :key="index" :label="item.walletName" :value="item.walletCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="个人账户结算报表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" highlight-current-row height="55vh" border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElInput,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElMessage
} from "element-plus";
import { downloadXlsx } from "@/utils"
import { dateStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import { requestDate } from "@/utils/reqDefaultDate";
import {
  getPersonalSettlementInfoPage,
  personalSettlementInfoExport,
  getWalletActiveList,
  getSystemUser,
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "", },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "时间", prop: "createTime", width: "170", render: (val) => val && dateStr(val) },
  { label: "账户钱包", prop: "walletName", width: "" },
  { label: "期初结转", prop: "openingBalance", width: "" },
  { label: "本期收入", prop: "currentIncome", width: "" },
  { label: "本期支出", prop: "currentExpend", width: "" },
  { label: "本期结余", prop: "currentBalance", width: "" },
  { label: "期末结余", prop: "endtermBalance", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElInput,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      requestDate: requestDate(),
      systemUserList: [],
    });
    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data;
      });
    };
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.startTime = dateStr(state.requestDate[0]) + " 00:00:00";
        params.endTime = dateStr(state.requestDate[1]) + " 23:59:59";
      } else {
        ElMessage.error("请选择结算日期")
        return false
      }
      return params
    }

    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data } = await getPersonalSettlementInfoPage(params);
        if (code === 0) {
          let {
            page: { total, list },
            currentBalanceCount,
            currentExpendCount,
            currentIncomeCount,
            endtermBalanceCount,
            openingBalanceCount,
          } = data;
          state.detailList = list;

          if (state.detailList.length) {
            state.detailList.push({
              openingBalance: openingBalanceCount,
              endtermBalance: endtermBalanceCount,
              currentIncome: currentIncomeCount,
              currentExpend: currentExpendCount,
              currentBalance: currentBalanceCount,
              userCode: "合计",
            });
          }

          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await personalSettlementInfoExport(params);
        downloadXlsx(res, '个人账户结算报表.xlsx')
        state.loading = false;
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
      };
      state.requestDate = requestDate()
    };
    onMounted(() => {
      // getList();
      queryWalletActiveList();
      querySystemUser();
    });
    return {
      column,
      state,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>