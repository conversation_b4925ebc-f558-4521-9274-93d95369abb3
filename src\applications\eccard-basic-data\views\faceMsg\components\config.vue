<template>
  <el-dialog :model-value="modelValue" title="算法配置" width="800px" :before-close="beforeClose">
    <el-form ref="formRef" size="mini" label-width="120px" :model="state.form" :rules="rules" style="margin-top:20px">
      <el-form-item label="算法类型:">
        <el-checkbox-group v-model="state.form.type">
          <el-checkbox label="旷视">旷视</el-checkbox>
          <el-checkbox label="虹软">虹软</el-checkbox>
          <el-checkbox label="商汤">商汤</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item :label="item+`模板列表:`" v-for="(item,index) in state.form.type" :key="index">
        <el-table :data="modelList.filter(v=>v.idText==item)" border highlight-current-row>
          <el-table-column label="名称" prop="name" align="center">
            <template #default="scope">
              {{  item+'模板'+scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column label="阈值" prop="value" align="center"></el-table-column>
          <el-table-column label="是否活体检测" prop="is" align="center"></el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElCheckboxGroup,
  ElCheckbox,
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { watch } from "@vue/runtime-core";

export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElCheckboxGroup,
    ElCheckbox,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const modelList = [
      { name: "一", value: "90", is: '是', idText: '旷视' },
      { name: "二", value: "80", is: '否', idText: '旷视' },
      { name: "三", value: "60", is: '是', idText: '旷视' },
      { name: "一", value: "90", is: '否', idText: '虹软' },
      { name: "二", value: "80", is: '是', idText: '虹软' },
      { name: "三", value: "60", is: '否', idText: '虹软' },
      { name: "一", value: "90", is: '是', idText: '商汤' },
      { name: "二", value: "80", is: '否', idText: '商汤' },
      { name: "三", value: "60", is: '是', idText: '商汤' },
    ]
    const formRef = ref(null);
    const state = reactive({
      form: {
        type: []
      },
    });
    watch(
      () => props.isShow,
      () => {

      }
    );
    const submit = () => {
      ElMessage.success("保存成功！")
      context.emit("update:modelValue", false);
    };
    const beforeClose = () => {
      context.emit("update:modelValue", false);
    };
    return {
      modelList,
      formRef,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}
.kade-table-wrap {
  padding-bottom: 0;
}
</style>