<template>
  <el-dialog append-to-body="true" :model-value="modelValue" title="数据分析" :before-close="update" :close-on-click-modal="false" :modal="true">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-row :gutter="5">
        <el-col :sm="24">
          <el-form-item label="选择月份" prop="analyMonth">
            <el-date-picker v-model="state.model.analyMonth" type="month" placeholder="请选择月份"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="开始时间" prop="analyStart">
        <el-date-picker v-model="state.model.analyStart" type="datetime" placeholder="开始时间搜索"  format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
      </el-form-item>
      <el-form-item label="结束时间" prop="analyEnd">
        <el-date-picker v-model="state.model.analyEnd" type="datetime" placeholder="开始时间搜索"  format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
      </el-form-item>
      <el-form-item label="方向键" prop="analyDirection">
        <el-switch v-model="state.model.analyDirection" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="开"
                   inactive-value="关" inactive-text="关" active-text="开">
        </el-switch>
        <span>启用方向键,分析结果会更准确，但要保证考勤时，没按错方向键，锁定的记录,将不再进行数据分析。</span>
      </el-form-item>
    </el-form>
    <template #footer v-if="disType!='info'">
      <p style="text-align: center">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>
</template>
<script>
  import { reactive , ref , watch} from "vue";
// import { getAttentionGroupInfoPage , delAttentionGroupInfoPage , updateAttentGroup , addAttentGroup , getAttentionGroupDettail } from "@/applications/eccard-ams/api";
  import {
    // ElTabPane,
    // ElTabs,
    ElButton,
    ElForm,
    ElFormItem,
    // ElInput,
    // ElMessage,
    ElDatePicker,
    ElSwitch,
    // ElImage,
    ElDialog
  } from "element-plus";

const getDefaultModel = () => ({
  analyMonth:'',
  analyStart:'',
  analyEnd:'',
  analyDirection:'开'
});
export default {
  emits: ["close"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({}),
    },
    type :{
      type: String,
      default: "",
    },
  },
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    // ElInput,
    ElDatePicker,
    ElSwitch,
    ElDialog
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: getDefaultModel(),
    });
    const rules = {
      analyMonth: [
        { required: true, message: "请输入考勤月份" },
      ],
      analyStart: [
        { required: true, message: "请输入分析开始时间" },
      ],
      analyEnd: [
        { required: true, message: "请输入分析结束时间" },
      ],
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", true);
    };
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            // const fn = props.role?.atgId ? updateAttentGroup : addAttentGroup;
            // state.model.atgManageUser = state.model.manageUser.userId;  //管理员
            // state.model["attendUserIdList"] = state.model.attendUserList.map(item=>{return item.userId}); //考勤人员
            // if(state.model.atgType == '固定考勤'){
            //   state.model["attendClassList"] = state.model.selectedFreeClassData.map(item=>{return {atgDay:item.atgDay , atcId:item.atcId , atgSet:1}});
            // }else{
            //   state.model["attendClassList"] = state.model.selectedFixedClassData.map(item=>{return {atgDay:item.atgDay , atgSet:1}});
            // }
            // const { message, code } = await fn(state.model);
            // if (code === 0) {
            //   ElMessage.success(message);
            // } else {
            //   ElMessage.error(message);
            // }
            context.emit("update:modelValue", true);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };

    watch(
      () => props.modelValue,
      (n) => {
        if (n) {
        //   if (props.role?.atgId) {
        //     const { code, message , data } = await getAttentionGroupDetail(props.role.atgId);
        //     if(code != 0){
        //       ElMessage.error(message);return;
        //     }
        //     data["manageUser"] = {userId:data.atgManageUser , userNum:data.userNum , userName:data.userName};
        //     data["attendUserList"] = data.attendUsers.map(item=>{return {userId:item.userId , userNum:item.userNum , userName:item.userName}});
        //     let objbase = getDefaultModel();
        //     if(data.atgType == '固定考勤') {
        //       objbase.freeClassList.forEach((item, index) => {
        //         let tempObj = data.attendClassList.filter(sitem => {
        //           return sitem.atgDay == item.atgDay
        //         });
        //         if (tempObj.length > 0) {
        //           item["atcId"] = tempObj[0].atcId;
        //           item["atcClassname"] = tempObj[0].atcClassname;
        //           item["atcTimes"] = tempObj[0].atcTimes;
        //         }
        //       });
        //     }
        //     state.model = Object.assign(objbase, data);
        //   } else {
          formRef.value?.resetFields();
            state.model = getDefaultModel();
        //   }
        }
      }
    );
    return {
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>
