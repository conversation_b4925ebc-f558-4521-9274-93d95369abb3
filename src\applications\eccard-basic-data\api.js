import request from "@/service";

/**
 * 上传头像
 */
export function uploadHeadImage64(data) {
  return request.post(
    "/eccard-basic-data/UserInfo/uploadHeadImageBase64",
    data
  );
}

/**
 * 查询用户状态列表
 */
export function queryUserState(params) {
  return request.post(`/eccard-basic-data//UserInfo/queryUserState`, params);
}

/**
 * 一卡通用户列表
 */
export function getUserInfoListByPage(params) {
  return request.post(
    "/eccard-basic-data/UserInfo/getUserInfoListByPage",
    params
  );
}

/**
 * 通过id重置人员密码
 */
export function resetPwdById(params) {
  return request.post(`/eccard-basic-data/UserInfo/ResetPwdById`, params);
}

/**
 * 添加一卡通用户
 */
export function addUser(params) {
  return request.post("/eccard-basic-data/UserInfo/addUserInfo", params);
}

/**
 * 删除一卡通用户
 */
export function deleteById(params) {
  return request.post(`/eccard-basic-data/UserInfo/delUserInfo`, params);
}

/**
 * 查询一卡通用户详情
 */
export function getUserInfoById(params) {
  return request.post("/eccard-basic-data/UserInfo/getUserInfoById", params);
}

/**
 * 上传头像
 */
export function upUserHeadImg(params) {
  return request.post("/eccard-basic-data/UserInfo/upLoadHeadImg", params);
}

/**
 * 修改人员信息
 */
export function updateUserInfo(params) {
  return request.post("/eccard-basic-data/UserInfo/updateUserInfo", params);
}

/**
 * 添加区域
 */
export function addArea(params) {
  return request.post("/eccard-basic-data/area/addArea", params);
}

/**
 * 删除区域
 */
export function delArea(params) {
  return request.post("/eccard-basic-data/area/delArea", params);
}

/**
 * 编辑区域
 */
export function editArea(params) {
  return request.post("/eccard-basic-data/area/editArea", params);
}

/**
 * 获取区域选择列表
 */
export function getAreaCheckList(params) {
  return request.get("/eccard-basic-data/area/getAreaCheckList", { params });
}

/**
 * 获取区域详情
 */
export function getAreaDetailByID(params) {
  return request.post("/eccard-basic-data/area/getAreaDetailByID", params);
}

/**
 * 获取区域列表
 */
export function getAreaForPage(params) {
  return request.get("/eccard-basic-data/area/getAreaForPage", params);
}

/**
 * 获取区域树
 */
export function getAreaMenuList(params) {
  return request.get("/eccard-basic-data/area/getAreaMenuList", { params });
}

/**
 * 添加单位
 */
export function addCompany(params) {
  return request.post("/eccard-basic-data/Company/add", params);
}

/**
 * 获取单位信息
 */
export function getCompanyInfo(params) {
  return request.get("/eccard-basic-data/Company/getinfo", { params });
}

/**
 * 卡分类列表
 */
export function getCardTypeByPage(params) {
  return request.get("/eccard-basic-data/cardType/getCardTypeByPage", {
    params
  });
}

/**
 * 卡类详情
 */
export function getCardTypeDetailsByCardTypeID(params) {
  return request.get(
    "/eccard-basic-data/cardType/getCardTypeDetailsByCardTypeID",
    { params }
  );
}

/**
 * 修改卡类信息
 */
export function updateCardTypeDetailsByCardTypeID(params) {
  return request.post(
    "/eccard-basic-data/cardType/updateCardTypeDetailsByCardTypeID",
    params
  );
}

/**
 * 家庭成员列表
 */
export function getFamilyList(params) {
  return request.get("/eccard-basic-data/FamilyInfo/familylist", { params });
}

/**
 * 添加家庭
 */
export function addFamilyInfo(params) {
  return request.post("/eccard-basic-data/FamilyInfo/addFamilyinfo", params);
}

/**
 * 删除家庭
 */
export function delFamilyInfo(params) {
  return request.get(`/eccard-basic-data/FamilyInfo/deleteFamilyinfoById`, {
    params
  });
}

/**
 * 更新家庭
 */
export function updateFamilyInfo(params) {
  return request.post("/eccard-basic-data/FamilyInfo/editFamilyinfo", params);
}

/**
 * 获取家庭成员信息
 */
export function getFamilyDetailsById(params) {
  return request.get("/eccard-basic-data/FamilyInfo/getFamilyDetailsById", {
    params
  });
}

/**
 * 身份类别列表分页
 */
export function getRolelistByPage(params) {
  return request.get("/eccard-basic-data/Role/getRolelistByPage", { params });
}
//获取身份类别
export function getRolelist(params) {
  return request.get("/eccard-basic-data/Role/getRolelist", { params });
}
/**
 * 添加身份类别
 */
export function addRole(params) {
  return request.post("/eccard-basic-data/Role/add", params);
}

/**
 * 删除身份类别
 */
export function delRole(params) {
  return request.post("/eccard-basic-data/Role/deleteByid", params);
}

/**
 * 修改身份类别
 */
export function updateRole(params) {
  return request.post("/eccard-basic-data/Role/update", params);
}

/**
 * 获取部门详情
 */
export function getDepartInfo(params) {
  return request.get("/eccard-basic-data/depart/getDeptDetailByID", { params });
}

/**
 * 获取部门树
 */
export function getDepartTree(params) {
  return request.get("/eccard-basic-data/depart/getDeptTreeList", { params });
}

/**
 * 获取所有部门树
 */

export function getDepartCheckList(params) {
  return request.get("/eccard-basic-data/depart/getDepartCheckList", {
    params
  });
}

/**
 * 删除部门
 */
export function delPart(params) {
  return request.post("/eccard-basic-data/depart/delDept", params);
}

/**
 * 新增部门
 */
export function addPart(params) {
  return request.post("/eccard-basic-data/depart/addDept", params);
}

/**
 * 编辑部门
 */
export function editPart(params) {
  return request.post("/eccard-basic-data/depart/editDept", params);
}

/* 日志 */
export function getLoginLogList(params) {
  return request.get("/eccard-basic-data/log/loginLogList", { params });
}

/* 日志 */
export function getServerList(params) {
  return request.get("/eccard-basic-data/server/page", { params });
}

/* 操作日志 */
export function getOperateLogList(params) {
  return request.get("/eccard-basic-data/log/operateLogList", { params });
}

/* 异常日志 */
export function getexceptionLogList(params) {
  return request.get("/eccard-basic-data/log/exceptionLogList", { params });
}

/**
 * 权限树列表
 */
/* 组织机构列表 */
export function getUserOrgPurview(params) {
  return request.get("/eccard-sys/sysPermission/userDeptList", params);
}
/* 区域列表 */
export function getUserAreaPurview(params) {
  return request.get("/eccard-sys/sysPermission/userAreaList", params);
}

/* 新闻公告 */
//获取新闻列表
export function getNewsList(params) {
  return request.get("/eccard-basic-data/News/page", { params });
}
/* 添加新闻 */
export function addNews(params) {
  return request.post("/eccard-basic-data/News/add", params);
}
/* 修改新闻 */
export function editNews(params) {
  return request.post("/eccard-basic-data/News/update", params);
}
/* 删除新闻 */
export function delNews(params) {
  return request.delete(`/eccard-basic-data/News/delete/${params}`);
}
/* 获取新闻详情 */
export function detailNews(params) {
  return request.post(`/eccard-basic-data/News/details/${params}`);
}

/**
 * 待办事项
 */
/* 待办事项分页列表 */
export function getTodoInfoList(params) {
  return request.get("/eccard-basic-data/base/todoInfo", { params });
}

/* 待办事项确认 */
export function todoInfoAffirm(params) {
  return request.post("/eccard-basic-data/base/todoInfo/affirm", params);
}

/* 待办事项处理 */
export function todoInfoDeal(params) {
  return request.post("/eccard-basic-data/base/todoInfo/deal", params);
}

/* 待办类型分页列表 */
export function getTodoTypeListPage(params) {
  return request.get("/eccard-basic-data/base/todoType", { params });
}
/* 待办类型不分页列表 */
export function getTodoTypeList(params) {
  return request.get("/eccard-basic-data/base/todoType/checklist", { params });
}
/* 待办类型-新增 */
export function addTodoType(params) {
  return request.post("/eccard-basic-data/base/todoType", params);
}
/* 待办类型-编辑 */
export function editTodoType(params) {
  return request.put("/eccard-basic-data/base/todoType", params);
}

/* 待办项目列表 */
export function getTodoProjectList(params) {
  return request.get("/eccard-basic-data/base/todoProject/checklist", {
    params
  });
}

/* 待办项目-新增 */
export function addTodoProject(params) {
  return request.post("/eccard-basic-data/base/todoProject", params);
}
/* 待办项目-编辑 */
export function editTodoProject(params) {
  return request.put("/eccard-basic-data/base/todoProject", params);
}

/**
 * 系统通知
 */
/* 系统通知发布列表 */
export function getSysMessageSendListPage(params) {
  return request.get("/eccard-basic-data/main/message/page", { params });
}
/* 系统通知详情 */
export function getSysMessageDetails(params) {
  return request.get(`/eccard-basic-data/main/message/${params}`);
}
/* 系统通知-新增 */
export function addSysMessage(params) {
  return request.post("/eccard-basic-data/main/message/add", params);
}
/* 系统通知-修改 */
export function editSysMessage(params) {
  return request.put("/eccard-basic-data/main/message/update", params);
}
/* 系统通知-删除 */
export function delSysMessage(params) {
  return request.delete(`/eccard-basic-data/main/message/${params}`);
}

/* 系统通知列表 */
export function getSysMessageListPage(params) {
  return request.get("/eccard-basic-data/main/message/log/page", { params });
}
/* 系统通知详情 */
export function getMessageDetails(params) {
  return request.get(`/eccard-basic-data/main/message/log/${params}`, {
    params
  });
}

/**
 * v1.1版本新接口
 */
/* 查询用户详情 */
export function getBaseUserInfo(params) {
  return request.post(`/eccard-basic-data/UserInfo/getBaseUserInfo`, params);
}
/* 查询住址详情 */
export function getUserAddressInfo(params) {
  return request.get(`/eccard-basic-data/address/${params}/detail`);
}

/* 人员信息导入模板下载 */
export function downImportTemplate(params) {
  return request.get("/eccard-basic-data/UserInfo/downImportTemplate", {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 人员信息导出 */
export function userInfoExport(params) {
  return request.get("/eccard-basic-data/UserInfo/UserInfoExport", {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 获取省份列表 */
export function getProvince(params) {
  return request.post(`/eccard-basic-data/city/province`, params);
}
/* 获取城市列表 */
export function getCity(params) {
  return request.post(`/eccard-basic-data/city/${params}/city`);
}
/* 获取区列表 */
export function getArea(params) {
  return request.post(`/eccard-basic-data/city/${params}/area`);
}

/* 系统地区信息表(省市区)不分页列表 */
export function getAddressList(params) {
  return request.get(`/eccard-basic-data/city/list`, { params });
}

/* 系统民族信息表不分页列表 */
export function nationList(params) {
  return request.get(`/eccard-basic-data/nation/list`, { params });
}

/* 新增用户住址信息表 */
export function addAddress(params) {
  return request.post(`/eccard-basic-data/address`, params);
}
/* 修改用户住址信息表 */
export function editAddress(params) {
  return request.put(`/eccard-basic-data/address`, params);
}

/* 用户银行卡信息信息 */
export function banksDetail(params) {
  return request.get(`/eccard-basic-data/banks/${params}/detail`);
}
/* 新增用户银行卡信息 */
export function addBanks(params) {
  return request.post(`/eccard-basic-data/banks`, params);
}
/* 修改用户银行卡信息 */
export function editBanks(params) {
  return request.put(`/eccard-basic-data/banks`, params);
}

/* 用户资格证书分页列表 */
export function certificate(params) {
  return request.get(`/eccard-basic-data/certificate`, { params });
}
/* 新增用户资格证书 */
export function addCertificate(params) {
  return request.post(`/eccard-basic-data/certificate`, params);
}
/* 修改用户资格证书 */
export function editCertificate(params) {
  return request.put(`/eccard-basic-data/certificate`, params);
}
/* 删除用户资格证书 */
export function delCertificate(params) {
  return request.delete(`/eccard-basic-data/certificate/${params}`);
}

/* 用户教育经历分页列表 */
export function educate(params) {
  return request.get(`/eccard-basic-data/educate`, { params });
}
/* 新增用户教育经历 */
export function addTeach(params) {
  return request.post(`/eccard-basic-data/educate`, params);
}
/* 修改用户教育经历 */
export function editTeach(params) {
  return request.put(`/eccard-basic-data/educate`, params);
}
/* 删除用户教育经历 */
export function delTeach(params) {
  return request.delete(`/eccard-basic-data/educate/${params}`);
}

/* 一卡通用户人脸数据表不分页列表 */
export function faceList(params) {
  return request.get(`/eccard-basic-data/face/list`, { params });
}
/* 新增一卡通用户人脸数据表 */
export function addFace(params) {
  return request.post(`/eccard-basic-data/face`, params);
}
/* 修改一卡通用户人脸数据表 */
export function editFace(params) {
  return request.put(`/eccard-basic-data/face`, params);
}
/* 删除一卡通用户人脸数据表 */
export function delFace(params) {
  return request.delete(`/eccard-basic-data/face/${params}`);
}

/* 用户附件信息分页列表 */
export function annex(params) {
  return request.get(`/eccard-basic-data/annex`, { params });
}
/* 新增用户附件信息 */
export function addAnnex(params) {
  return request.post(`/eccard-basic-data/annex`, params);
}
/* 删除用户教育经历 */
export function delAnnex(params) {
  return request.delete(`/eccard-basic-data/annex/${params}`);
}

/* 批量修改部门 */
export function batchEditDept(params) {
  return request.post(`/eccard-basic-data/UserInfo/batchEditDept`, params);
}
/* 批量修改状态 */
export function batchEditStatus(params) {
  return request.post(`/eccard-basic-data/UserInfo/batchEditStatus`, params);
}

/* 用户岗位信息表分页列表 */
export function getStationList(params) {
  return request.get(`/eccard-basic-data/post`, { params });
}
/* 用户岗位信息表不分页列表 */
export function stationList(params) {
  return request.get(`/eccard-basic-data/post/list`, { params });
}
/* 新增用户岗位信息表 */
export function addStation(params) {
  return request.post(`/eccard-basic-data/post`, params);
}
/* 修改用户岗位信息表 */
export function editStation(params) {
  return request.put(`/eccard-basic-data/post`, params);
}
/* 删除用户岗位信息表 */
export function delStation(params) {
  return request.delete(`/eccard-basic-data/post/${params}`);
}

/* 组织机构信息导出 */
export function DeptInfoExport(params) {
  return request.get(`/eccard-basic-data/depart/DeptInfoExport`, {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}
/* 组织机构导入模板导出 */
export function exportDeptExample(params) {
  return request.get(`/eccard-basic-data/depart/exportDeptExample`, {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 区域标签分页列表 */
export function getAreaLabelListByPage(params) {
  return request.get(`/eccard-basic-data/area/getAreaLabelListByPage`, {
    params
  });
}
/* 区域标签不分页列表 */
export function getAreaLabelCheckList(params) {
  return request.get(`/eccard-basic-data/area/getAreaLabelCheckList`, {
    params
  });
}
/* 新增区域标签 */
export function addAreaLabel(params) {
  return request.post(`/eccard-basic-data/area/addAreaLabel`, params);
}
/* 修改区域标签 */
export function editAreaLabel(params) {
  return request.post(`/eccard-basic-data/area/editAreaLabel`, params);
}
/* 删除区域标签 */
export function deleteAreaLabel(params) {
  return request.post(`/eccard-basic-data/area/deleteAreaLabel`, params);
}

/* 人脸采集数据 */
export function userFaceListByPage(params) {
  return request.get(`/eccard-basic-data/face/userFaceListByPage`, { params });
}

/* OSS上传用户人脸照片信息 */
export function uploadUserFaceV2(params) {
  return request.post(
    `/eccard-basic-data/UserInfo/notoken/uploadUserFaceV2`,
    params
  );
}

/* 部门负责人分页列表 */
export function getDirectorPageList(params) {
  return request.post(`/eccard-basic-data/depart/getDirectorPageList`, params);
}

/* 电话号码库管理分页列表 */
export function baseTelLibPage(params) {
  return request.post(`/eccard-basic-data/baseTelLib/page`, params);
}
/* 新增电话号码 */
export function baseTelLibAdd(params) {
  return request.post(`/eccard-basic-data/baseTelLib`, params);
}
/* 修改电话号码*/
export function baseTelLibEdit(params) {
  return request.put(`/eccard-basic-data/baseTelLib`, params);
}
/* 删除电话号码 */
export function baseTelLibDel(params) {
  return request.delete(`/eccard-basic-data/baseTelLib/deleteBatch/${params}`);
}
/* 电话号码导入模板 */
export function baseTelLibImportTemplate(params) {
  return request.get(`/eccard-basic-data/baseTelLib/downImportTemplate`, {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}
/* 家长信息导入模板 */
export function parentTemplate(params) {
  return request.get(`/eccard-basic-data/UserInfo/down/parentTemplate`, {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}

/* 批量修改人员部门导入模版 */
export function updateBatchUserDeptTemplate(params) {
  return request.get(`/eccard-basic-data/UserInfo/updateBatchUserDeptTemplate`, {
    params,
    headers: {
      "Content-Type": "application/octet-stream",
      Accept: "*/*"
    },
    responseType: "blob"
  });
}