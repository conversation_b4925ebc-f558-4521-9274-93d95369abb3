
<template>
  <div class="income-detail border-box" v-loading="state.listLoading">
    <div class="padding-box transaction-info-report">
      <div class="transaction-item">
        <div class="label">今日现金总充值金额</div>
        <div class="value">
          <span class="num">{{
            state.todayData.totalAmount ? state.todayData.totalAmount : 0
          }}</span>
          <span class="unit">元</span>
        </div>
      </div>
      <div class="transaction-item">
        <div class="label">今日现金充值笔数</div>
        <div class="value">
          <span class="num">{{
            state.todayData.totalCount ? state.todayData.totalCount : 0
          }}</span>
          <span class="unit">笔</span>
        </div>
      </div>
    </div>
    <el-divider></el-divider>

    <div class="padding-form-box report-search-box">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="精准查询:">
          <el-input placeholder="输入交易单号或账户名称" v-model="state.form.keyWord"></el-input>
        </el-form-item>
        <el-form-item label="&nbsp;">
          <el-button @click="search()" size="small" type="primary" icon="el-icon-search">查询</el-button>
          <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
        </el-form-item>
        <el-form-item label="&nbsp;">
          <el-button icon="el-icon-daoru" size="small" class="btn-purple" @click="exportClick()">下载查询明细</el-button>
          <el-button @click="printClick()" icon="el-icon-daochu" size="small" class="btn-blue">打印</el-button>
        </el-form-item>

        <el-form-item label="选择日期:">
          <el-col :span="24">
            <el-date-picker v-model="state.requestDate" type="datetimerange" :default-time="state.defaultTime"
              range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
            </el-date-picker>
          </el-col>
          <el-col :span="3" class="date" v-for="(item, index) in defaultDateList" :key="index"
            @click="changeDefaultDate(item.value)">
            {{ item.label }}
          </el-col>
        </el-form-item>

        <el-form-item label="金额范围:" class="money-range">
          <el-col :span="1"> ￥ </el-col>
          <el-col :span="10">
            <el-input placeholder="输入金额" v-model="state.form.minAmount"></el-input>
          </el-col>
          <el-col :span="3">&nbsp;&nbsp;&nbsp;- &nbsp;&nbsp;&nbsp;</el-col>
          <el-col :span="10">
            <el-input placeholder="输入金额" v-model="state.form.maxAmount"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item label="交易方式:">
          <el-select clearable v-model="state.form.tradeMode" placeholder="请选择">
            <el-option v-for="(item, index) in costTypelist" :key="index" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <!--       <div class="export-and-print">
            <el-button
              icon="el-icon-daoru"
              size="small"
              class="btn-purple"
              @click="exportClick()"
              >下载查询明细</el-button
            >
            <el-button
              @click="print()"
              icon="el-icon-daochu"
              size="small"
              class="btn-blue"
              >打印</el-button
            >
          </div> -->
    </div>

    <el-table style="width: 100%" :data="state.dataList" v-loading="false" highlight-current-row border stripe>
      <el-table-column v-for="(item, index) in state.columns" :key="index" :label="item.label" :prop="item.prop"
        :width="item.width" align="center"></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange"
        @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <el-divider></el-divider>
    <div class="count">
      <span class="count-text">合计：</span>
      <span class="count-money">{{ state.moneyTotal.toFixed(2) }}</span>
      <span class="count-unit">元</span>
    </div>
  </div>
</template>
<script>
import {
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDivider,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { onMounted, reactive } from "vue";
import { timeStr } from "@/utils/date.js";
import { print } from "@/utils";
import { defaultDateList, requestDate, requestDefaultTime } from "@/utils/reqDefaultDate.js";
import {
  getRechargeList,
  getWebRechargeTotalByDay,
  getRechargeListByExport,
  getRechargeListByPrint
} from "@/applications/eccard-finance/api";
import { formatColumns } from "@/utils";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";

export default {
  components: {
    "el-divider": ElDivider,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-date-picker": ElDatePicker,
    "el-col": ElCol,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-dept-select-tree": deptSelectTree,

  },
  props: {
    costTypelist: {
      types: Array,
      default: [],
    },
    departCheckList: {
      types: Array,
      default: [],
    },
    systemUserList: {
      types: Array,
      default: [],
    },
  },
  setup() {
    const state = reactive({
      listLoading: false,
      isPrint: false,
      total: 0,
      todayData: {},
      moneyTotal: 0,
      dataList: [],
      printList: [],
      form: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 10,
        tradeSource: 2,
      },
      requestDate: requestDate(),
      defaultTime: requestDefaultTime(),
      columns: formatColumns([
        {
          label: "交易单号",
          prop: "tradeNo",
          width: 250,
        },
        {
          label: "交易时间",
          prop: "tradeDate",
          width: 160,
        },
        {
          label: "充值帐户",
          prop: "userName",
        },
        {
          label: "充值钱包",
          prop: "walletName",
        },
        {
          label: "收支类型",
          prop: "inoutTypeName",
        },
        {
          label: "交易类型",
          prop: "costTypeName",
        },
        {
          label: "交易来源",
          prop: "tradeSourceName",
        },
        {
          label: "交易方式",
          prop: "tradeModeName",
        },
        {
          label: "充值金额(元)",
          prop: "tradeAmount",
        },
        {
          label: "充值前余额(元)",
          prop: "tradeBeforeBalance",
        },
        {
          label: "充值后余额(元)",
          prop: "tradeAfterBalance",
        },
        {
          label: "操作员",
          prop: "operatorName",
        },
      ]),
    });
    const getDataList = () => {
      state.listLoading = true;
      getRechargeList(state.form)
        .then((res) => {
          state.total = res.data.total;
          state.dataList = res.data.list;
          let moneyTotal = 0;
          if (state.dataList.length) {
            state.dataList.forEach((item) => {
              item.tradeDate = timeStr(item.tradeDate);
              moneyTotal += item.tradeAmount;
            });
          }
          state.moneyTotal = moneyTotal;
          state.listLoading = false;
        })
        .catch(() => {
          state.listLoading = false;
        });
    };
    const getDayData = async () => {
      let { code, data } = await getWebRechargeTotalByDay();
      if (code === 0) {
        state.todayData = data;
      }
    };
    const changeDefaultDate = (val) => {
      state.requestDate = val();
      state.form.beginDate = state.requestDate[0];
      state.form.endDate = state.requestDate[1];
    };
    const search = () => {
      getDataList();
    };
    const reset = () => {
      state.requestDate = requestDate();
      state.form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 10,
        tradeSource: 2,
      };
    };

    const printClose = () => {
      state.isPrint = false;
    };

    const printClick = async () => {
      let { data, code } = await getRechargeListByPrint(state.form)
      if (code === 0) {
        print(data, "web端充值明细")
      }
    };

    const exportClick = () => {
      getRechargeListByExport(state.form).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "现金充值记录列表.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    };

    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getDataList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getDataList();
    };
    onMounted(() => {
      getDayData();
      getDataList();
    });
    return {
      state,
      defaultDateList,
      exportClick,
      printClose,
      printClick,
      changeDefaultDate,
      handlePageChange,
      handleSizeChange,
      changeDate,
      search,
      reset,
    };
  },
};
</script>
<style lang="scss">
.income-detail {
  .transaction-info-report {
    display: flex;

    // justify-content: space-between;
    .transaction-item {
      display: flex;
      flex-direction: column;
      padding: 20px 40px;

      .label {
        color: $font-sub-color;
      }

      .value {
        margin-top: 20px;

        .num {
          color: $font-dark;
          font-size: 40px;
        }

        .unit {
          color: $font-sub-color;
          margin-left: 10px;
        }
      }
    }
  }

  .el-divider--horizontal {
    margin: 0 !important;
  }

  .kade-table-wrap {
    border: none;
  }
}

.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.847058823529412);
}

.date:hover {
  color: #06f;
}

.el-botton {
  color: #fff;
}

.el-table {
  border-left: none;

  tr>th:last-child,
  tr>td:last-child {
    border-right: none !important;
  }

  .el-table--border,
  .el-table--group {
    border: none;
  }

  &::after {
    background-color: transparent;
  }
}

.count {
  padding: 20px;
  background: #fff;

  .count-text {
    margin-right: 20px;
  }

  .count-money {
    font: bold 20px arial;
    color: #09b706;
    margin-right: 10px;
  }

  .count-unit {
    color: #666666;
  }
}

.report-search-box {
  position: relative;

  .export-and-print {
    position: absolute;
    top: 20px;
    right: 30px;
  }
}
</style>
