<template>
  <div class="cashierdetails">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="search()" @reset="reset()">
        <el-form inline label-width="100px" size="mini">
          <!--           <el-form-item label="组织机构:">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
              @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
          </el-form-item>
          <el-form-item label="区域">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath"
              :multiple="false" @valueChange="(val)=>state.form.areaPath=val.areaPath" />
          </el-form-item> -->
          <el-form-item label="消费时间">
            <el-date-picker unlink-panels v-model="state.requestDate" type="datetimerange" :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" />
          </el-form-item>
          <el-form-item label="查询类型:">
            <el-select v-model="state.form.type" placeholder="请选择">
              <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="消费统计" v-loading="state.loading">
        <template #extra>
          <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
          <el-button class="btn-purple" icon="el-icon-printer" size="mini" @click="printClick()">打印</el-button>
        </template>
        <el-table :data="state.dataList.length && Object.keys(state.dataList[0].areaMap).length != 0 ? state.dataList : []" border>
          <el-table-column show-overflow-tooltip prop="deptName" label="类别" align="center"></el-table-column>
          <el-table-column v-for="(item, index) in state.dataList.length ? state.dataList[0].areaMap : []" :key="index" show-overflow-tooltip prop="areaMap" :label="index" align="center">
            <el-table-column show-overflow-tooltip label="现金消费" prop="cashConsume" align="center">
              <template #default="scope">
                {{ scope.row.areaMap[index].cashConsume.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="补助消费" prop="subsidyConsume" align="center">
              <template #default="scope">
                {{ scope.row.areaMap[index].subsidyConsume.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="小计" prop="totalConsume" align="center">
              <template #default="scope">
                {{ scope.row.areaMap[index].totalConsume.toFixed(2) }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessage,
} from "element-plus";
/* import deptSelectTree from "@/components/tree/deptSelectTree";
import areaSelectTree from "@/components/tree/areaSelectTree"; */
import {
  consumeReportList,
  consumeReportListExport,
  consumeReportPrint
} from "@/applications/eccard-finance/api";
import { downloadXlsx, print } from "@/utils";
import { onMounted } from "@vue/runtime-core";
import { timeStr } from "@/utils/date.js";
import { requestDate,defaultTime } from "@/utils/reqDefaultDate";
export default {
  components: {
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElButton,
    ElTable,
    ElTableColumn,
    /*     "kade-dept-select-tree": deptSelectTree,
        "kade-area-select-tree": areaSelectTree, */
  },
  setup() {
    const typeList = [
      { label: "按部门", value: 1 },
      { label: "全部", value: 2 },
    ]
    const state = reactive({
      loading: false,
      requestDate: requestDate(),
      walletActiveList: [],
      form: {
        type: 2
      },
      dataList: [],
      total: 0,
      costTypeList: [],
      systemUserList: [],
    });
    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.startDate = timeStr(state.requestDate[0]);
        params.endDate = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择消费时间")
        return false
      }
      return params
    }
    const getList = () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      consumeReportList(params).then(({ data }) => {
        state.dataList = data
        state.loading = false
        // state.form = res.data;
      }).catch(() => {
        state.loading = false
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        type: 2
      };
      state.requestDate = requestDate();
    };

    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await consumeReportListExport(params);
        downloadXlsx(res, "消费统计报表.xlsx");
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const printClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      let { data, code } = await consumeReportPrint(params)
      if (code === 0) {
        print(data)
      } else {
        ElMessage.error("未查询到数据！")
      }
      state.loading = false
    }
    onMounted(() => {
      // getList();
    });
    return {
      defaultTime,
      typeList,
      state,
      search,
      exportClick,
      printClick,
      reset,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>