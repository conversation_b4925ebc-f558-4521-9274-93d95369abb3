import request from '@/service';
import Qs from 'qs';

// 新增商户信息
export function addMerchant(params) {
    return request.post('/eccard-merchant/Basic/addMerchant', params);
}
//商户信息列表--导出
export function getMerchantListToExport(params) {
    return request.get('/eccard-merchant/Basic/getMerchantListToExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}
// 商户收入明细--分页查询
export function getMerchantIncomeListByPage(params) {
    return request.get('/eccard-merchant/Basic/getMerchantIncomeListByPage', { params });
}
//商户收入明细--导出
export function getMerchantIncomeListToExport(params) {
    return request.get('/eccard-merchant/Basic/getMerchantIncomeListToExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}
// 商户收入明细--打印
export function getMerchantIncomeListToPrint(params) {
    return request.get('/eccard-merchant/Basic/getMerchantIncomeListToPrint', { params });
}

// 结算费率--分页查询
export function getMerchantRateLogListByPage(params) {
    return request.get('/eccard-merchant/Basic/getMerchantRateLogListByPage', { params });
}
// 商户绑定操作员信息
export function addMerchantRateLog(params) {
    return request.post('/eccard-merchant/Basic/addMerchantRateLog', params);
}



// 商户绑定操作员信息
export function addMerchantOperator(params) {
    return request.post('/eccard-merchant/Basic/addMerchantOperator', params);
}

// 商户绑定操作员信息
export function deleteMerchantOperator(params) {
    return request.post('/eccard-merchant/Basic/deleteMerchantOperator', params);
}

// 根据商户自编号ID查询商户详情信息
export function getMerchantInfoById(params) {
    return request.get('/eccard-merchant/Basic/getMerchantInfoById', { params });
}

// 根据指定条件查询商户信息列表
export function getMerchantListByPage(params) {
    return request.get('/eccard-merchant/Basic/getMerchantListByPage', { params });
}

// 商户操作员详情信息
export function getMerchantOperatorById(params) {
    return request.get('/eccard-merchant/Basic/getMerchantOperatorById', { params });
}

// 查询商户下的操作员列表
export function getMerchantOperatorList(params) {
    return request.get('/eccard-merchant/Basic/getMerchantOperatorList', { params });
}

// 更新保存商户信息
export function updateMerchant(params) {
    return request.post('/eccard-merchant/Basic/updateMerchant', params);
}

// 更新商户操作员信息
export function updateMerchantOperator(params) {
    return request.post('/eccard-merchant/Basic/updateMerchantOperator', params);
}

// 更新商户操作员使用状态
export function updateMerchantOperatorState(params) {
    return request.post('/eccard-merchant/Basic/updateMerchantOperatorState', params);
}

// 添加结算记录
export function addMerchantSettlementInfo(params) {
    return request.post('/eccard-merchant/settlement/addMerchantSettlementInfo', params);
}

// 下载商户结算明细
export function downMerchantSettlementList(params) {
    return request.post('/eccard-merchant/settlement/downMerchantSettlementList', params, {
        headers: {
            'Content-Type': 'application/octet-stream',
        },
        responseType: 'blob',
    });
}
// 商户结算明细--打印
export function getMerchantSettlementListByPrint(params) {
    return request.post('/eccard-merchant/settlement/getMerchantSettlementListByPrint', params);
}
// 查询结算信息详情
export function getMerchantSettlementDetailById(params) {
    return request.get('/eccard-merchant/settlement/getMerchantSettlementDetailById', { params });
}

// 查询结算信息列表
export function getMerchantSettlementListByPage(params) {
    return request.get('/eccard-merchant/settlement/getMerchantSettlementListByPage', { params });
}

// 打印结算凭证
export function printMerchantSettlementProof(params) {
    return request.get('/eccard-merchant/settlement/printMerchantSettlementProof', { params });
}

// 绑定商户设备
export function addMerchantDevice(params) {
    return request.post('/eccard-merchant/device/addMerchantDevice', params);
}

// 解除绑定商户设备
export function deleteMerchantDevice(params) {
    return request.post('/eccard-merchant/device/deleteMerchantDevice', params);
}
//商户设备列表--导出
export function getDeviceListToExport(params) {
    return request.get('/eccard-merchant/device/getDeviceListToExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}


// 分页查询商户设备列表
export function getDeviceListToPage(params) {
    return request.get('/eccard-merchant/device/getDeviceListToPage', { params });
}

// 查询商户设备详情
export function getMerchantDeviceInfoById(params) {
    return request.get('/eccard-merchant/device/getMerchantDeviceInfoById', { params });
}

// 待绑定设备
export function getNotBindDeviceList(params) {
    return request.get('/eccard-merchant/device/getNotBindDeviceList', { params });
}

/**
 * 银行卡列表
 */
export function getBankList(params) {
    return request.get('/eccard-merchant/Basic/getBanksList', { params });
}

/**
 * 获取交易设备下拉列表
 */
export function getDeviceList(params) {
    return request.post('/eccard-iot/consumeDevice/getDeviceList', params);
}

/**
 * 获取操作员
 */
export function getSystemUser(params) {
    return request.post('/eccard-purview/systemUserManage/getSystemUserByKey', { params });
}

/**
 * 获取组织机构下拉列表
 */
export function getDepartCheckList(params) {
    return request.get('/eccard-basic-data/depart/getDepartCheckList', { params });
}
/**
 * 获取交易方式下拉列表
 */
export function getCostTypelist(params) {
    return request.get('/eccard-finance/tradeMode/list', { params });
}

//分页查询充值明细
export function getRechargeList(params) {
    return request.get('/eccard-finance/CashReport/getRechargeListByPage', { params });
}

//卡务中心每日充值汇总
export function getCardCenterRechargeTotalByDay(params) {
    return request.get('/eccard-finance/CashReport/getCardCenterRechargeTotalByDay', { params });
}

//自助终端每日充值汇总
export function getConsumeRechargeTotalByDay(params) {
    return request.get('/eccard-finance/CashReport/getConsumeRechargeTotalByDay', { params });
}

//Web端每日充值汇总
export function getWebRechargeTotalByDay(params) {
    return request.get('/eccard-finance/CashReport/getWebRechargeTotalByDay', { params });
}

//导出充值明细
export function getRechargeListByExport(params) {
    return request.get('/eccard-finance/CashReport/getRechargeListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}


/* 个人账户模块 */

//获取人员信息列表
export function getPersonList(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonListByPage', { params });
}

//获取人员信息详情
export function getPersonAccountInfo(params) {
    return request.post('/eccard-finance/AccountBasic/getPersonAccountInfo', params);
}

//获取身份类别
export function getRolelist(params) {
    return request.get('/eccard-basic-data/Role/getRolelist', { params });
}

//获取开户策略列表select
export function getAccountStrategyList(params) {
    return request.get('/eccard-finance/AccountBasic/getAccountStrategyList', { params });
}
//获取开户策略详情select
export function getAccountStrategyInfo(params) {
    return request.get('/eccard-finance/sys/accountStrategy/getAccountStrategyInfoById', { params });
}


//获取账户状态select
export function getDictionaryList(params) {
    return request.get('/eccard-partal-manage/dict/getDictionaryList', { params });
}
//获取卡片列表select
export function getCardTypeList(params) {
    return request.get('/eccard-card/common/card/type/list', { params });
}

//单人员或者批量人员开户
export function openPersonAccount(params) {
    return request.post('/eccard-finance/AccountBasic/openPersonAccount', params);
}

//获取租户下的已经激活的钱包列表
export function getWalletActiveList(params) {
    return request.get('/eccard-finance/AccountBasic/getWalletActiveList', { params });
}

//个人账户钱包激活
export function personWalletActive(params) {
    return request.post('/eccard-finance/AccountBasic/personWalletActive', params);
}

//个人账户钱包激活
export function batchPersonWalletActive(params) {
    return request.post('/eccard-finance/AccountBasic/batchPersonWalletActive', params);
}
//修改用户卡类
export function batchPersonCardType(params) {
    return request.put('/eccard-finance/AccountBasic/changePersonCardType', params);
}
//获取人员的电子钱包信息
export function getPersonWalletInfo(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonWalletInfo', { params });
}

//导出人员列表
export function exportPersonList(params) {
    return request.post('/eccard-finance/AccountBasic/exportPersonList',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}

//获取充值面额列表
export function getRechargeDenomination(params) {
    return request.get('/eccard-finance/AccountBasic/getRechargeDenomination', { params });
}

//获取支付方式列表
export function getWalletRechargeModeList(params) {
    return request.get('/eccard-finance/AccountBasic/getWalletRechargeModeList', { params });
}

//个人账户钱包充值
export function personAccountRecharge(params) {
    return request.post('/eccard-finance/AccountBasic/personAccountRecharge', { ...params, isRSA: true, timeStamp: new Date().getTime() });
}

//人员钱包清零（补助钱包和次数钱包）
export function personResetWallet(params) {
    return request.post('/eccard-finance/AccountBasic/personResetWallet', { ...params, isRSA: true, timeStamp: new Date().getTime() });
}


//获取人员交易明细
export function getPersonTradeList(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonTradeListByPage', { params });
}

//导出人员交易明细
export function getPersonTradeListByExport(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonTradeListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}

//获取交易方式select
export function tradeMode(params) {
    return request.get('/eccard-finance/tradeMode/list', {
        params,
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//获取交易类型select
export function costType(params) {
    return request.get('/eccard-finance/cost/type/list', { params });
}

//获取交易来源select
export function tradeSource(params) {
    return request.get('/eccard-finance/trade/source/list', { params });
}

//获取人员卡片信息
export function getPersonCardInfo(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonCardInfo', { params });
}

//挂失和解挂人员卡片信息
export function lossPersonCard(params) {
    return request.post('/eccard-finance/AccountBasic/lossPersonCard', params);
}


//获取人员卡片操作列表
export function getPersonCardLog(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonCardLogByPage', { params });
}

//获取个人的钱包交易汇总(已激活钱包)
export function getPersonWalletTradeSum(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonWalletTradeSum', { params });
}

//获取个人的家属信息集合(已发卡)
export function getPersonFamilyList(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonFamilyList', { params });
}

//激活人员家属账户
export function personFamilyAccountActive(params) {
    let data = new URLSearchParams()
    data.append("acctType", params.acctType)
    data.append("familyId", params.familyId)
    data.append("userId", params.userId)
    return request.post('/eccard-finance/AccountBasic/personFamilyAccountActive', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}
//更新人员家属账户状态
export function updatePersonFamilyAcctStatus(params) {
    let data = new URLSearchParams()
    data.append("acctStatus", params.acctStatus)
    data.append("familyId", params.familyId)
    data.append("userId", params.userId)
    return request.post('/eccard-finance/AccountBasic/updatePersonFamilyAcctStatus', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}

//分页查询人员充值记录
export function getRechargeRecord(params) {
    return request.get('/eccard-finance/AccountBasic/getRechargeRecordByPage', { params });
}
//充值记录冲正
export function personAccountRighting(params) {
    return request.post('/eccard-finance/AccountBasic/personAccountRighting', { ...params, isRSA: true, timeStamp: new Date().getTime() });
}

//分页查询人员冲正记录
export function getRightingList(params) {
    return request.get('/eccard-finance/AccountBasic/getRightingListByPage', { params });
}

//导出人员冲正记录
export function getRightingListByExport(params) {
    return request.get('/eccard-finance/AccountBasic/getRightingListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    })
}



//分页查询人员充值记录
export function getConsumeRecordList(params) {
    return request.get('/eccard-finance/AccountBasic/getConsumeRecordListByPage', { params });
}

//消费记录纠错
export function personAccountCorrection(params) {
    return request.post('/eccard-finance/AccountBasic/personAccountCorrection', params);
}

//分页查询人员纠错记录
export function getCorrectionList(params) {
    return request.get('/eccard-finance/AccountBasic/getCorrectionListByPage', { params });
}
//导出人员纠错记录
export function getCorrectionListByExport(params) {
    return request.get('/eccard-finance/AccountBasic/getCorrectionListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    })
}

//查询人员退款清单
export function getPersonRefundList(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonRefundList', { params });
}


//人员账户退款
export function personRefund(params) {
    return request.post('/eccard-finance/AccountBasic/personRefund', { ...params, isRSA: true, timeStamp: new Date().getTime() });
}

//查询人员账户的退款记录
export function getRefundRecordList(params) {
    return request.get('/eccard-finance/AccountBasic/getRefundRecordList', { params });
}

//查询人员退款详情
export function getPersonRefundDetails(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonRefundDetails', { params });
}
//导出人员退款记录
export function getRefundRecordListExport(params) {
    return request.get('/eccard-finance/AccountBasic/getRefundRecordListExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    })
}




/* 现金充值模块 */

//分页查询现金发放列表
export function getCashGrantListByPage(params) {
    return request.get('/eccard-finance/CashGrant/getCashGrantListByPage', { params });
}

//导出现金发放列表
export function getCashGrantListByExport(params) {
    return request.get('/eccard-finance/CashGrant/getCashGrantListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}


//分页查询待充值清单
export function getWaitRechargeListByPage(params) {
    return request.get('/eccard-finance/CashGrant/getWaitRechargeListByPage', { params });
}

//生成充值清单
export function generateRechargeList(params) {
    return request.post('/eccard-finance/CashGrant/generateRechargeList', params);
}

//删除待充值清单
export function deleteWaitRecharge(params) {
    return request.post('/eccard-finance/CashGrant/deleteWaitRechargeById', params);
}


//确认生成充值清单（保存）
export function saveImportRechargeList(params) {
    return request.post('/eccard-finance/CashGrant/saveImportRechargeList', params);
}


//分页查询已入库充值清单
export function getCashGrantList(params) {
    return request.post('/eccard-finance/CashGrant/getCashGrantList', params);
}

//删除已入库充值清单
export function deleteCashRecord(params) {
    return request.post('/eccard-finance/CashGrant/deleteCashRecord', params);
}

//编辑充值清单
export function editRechargeModel(params) {
    return request.post('/eccard-finance/CashGrant/editRechargeModel', params);
}

//保存导入的补助冲正数据
export function saveImportRightingList(params) {
    return request.post('/eccard-finance/CashGrant/saveImportRightingList', params);
}


//分页查询导入冲正清单
export function getWaitRechargeRightingList(params) {
    return request.get('/eccard-finance/CashGrant/getWaitRechargeRightingListByPage', { params });
}

//删除导入冲正人员
export function deleteWaitRighting(params) {
    return request.post('/eccard-finance/CashGrant/deleteWaitRightingById', params);
}

//下载导入充值清单样例
export function exportCashExample(params) {
    return request.get('/eccard-finance/CashGrant/exportCashExample', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}
//下载导入冲正清单样例
export function exportRightingExample(params) {
    return request.get('/eccard-finance/CashGrant/exportRightingExample', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}
//审核充值或冲正的数据
export function auditRechargeImport(params) {
    return request.post('/eccard-finance/CashGrant/auditRechargeImport', params);
}



/* 补助发放 */

//补助类型列表
export function getSubsidyTypeList(params) {
    return request.get('/eccard-finance/sys/subsidyType/getSubsidyTypeList', { params });
}

//分页查询补助发放列表
export function getSubsidyGrantListByPage(params) {
    return request.get('/eccard-finance/subsidyProject/getSubsidyGrantListByPage', { params });
}

//获取补助发放列表-导出打印
export function getSubsidyGrantListByExport(params) {
    return request.get('/eccard-finance/subsidyProject/getSubsidyGrantListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    })
}

//生成补助清单
export function generateSubsidyList(params) {
    return request.post('/eccard-finance/subsidyRecord/generateSubsidyList', params);
}

//获取待补助清单-分页
export function getWaitSubsidyListByPage(params) {
    return request.get('/eccard-finance/subsidyRecord/getWaitSubsidyListByPage', { params });
}
//获取已入库补助清单-分页
export function getSubsidyListByPage(params) {
    return request.get('/eccard-finance/subsidyRecord/getSubsidyListByPage', { params });
}


//删除补助清单表
export function deleteWaitSubsidy(params) {
    let data = new URLSearchParams()
    data.append("projectNo", params.projectNo)
    data.append("id", params.id)
    return request.post('/eccard-finance/subsidyRecord/deleteWaitSubsidyById', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}

//保存导入-生成补助数据
export function saveImportSubsidyList(params) {
    return request.post('/eccard-finance/subsidyProject/saveImportSubsidyList', params);
}

//修改待补助清单人员
export function updateWaitSubsidy(params) {
    return request.post('/eccard-finance/subsidyRecord/updateWaitSubsidyById', params);
}

//删除入库清单人员
export function delUpdateWaitSubsidy(params) {
    return request.delete('/eccard-finance/subsidyRecord/' + params);
}


//编辑补助发放项目
export function editSubsidyProject(params) {
    return request.post('/eccard-finance/subsidyProject/editSubsidyProject', params);
}




//待补助冲正清单分页列表
export function getWaitSubsidyRightingList(params) {
    return request.get('/eccard-finance/subsidyRecord/getWaitSubsidyRightingListByPage', { params });
}
//删除待冲正清单人员
export function deleteWaitSubsidyRighting(params) {
    let data = new URLSearchParams()
    data.append("projectNo", params.projectNo)
    data.append("id", params.id)
    return request.post('/eccard-finance/subsidyRecord/deleteWaitSubsidyRightingById', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}



//保存导入的补助冲正数据
export function saveImportSubsidyRightingList(params) {
    return request.post('/eccard-finance/subsidyProject/saveImportSubsidyRightingList', params);
}

//审核补助或补助冲正的数据
export function auditSubsidyImport(params) {
    let data = new URLSearchParams()
    for (let key in params) {
        data.append(key, params[key])
    }
    return request.post('/eccard-finance/subsidyProject/auditSubsidyImport', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}



//补助类型分页列表
export function getSubsidyTypeListByPage(params) {
    return request.get('/eccard-finance/sys/subsidyType/getSubsidyTypeListByPage', { params });
}
//新增删除补助类型
export function addSubsidyType(params) {
    return request.post('/eccard-finance/sys/subsidyType/addSubsidyType', params);
}
//编辑补助类型
export function updateSubsidyTypeById(params) {
    return request.post('/eccard-finance/sys/subsidyType/updateSubsidyTypeById', params);
}
//删除补助类型
export function deleteSubsidyTypeById(params) {
    return request.delete('/eccard-finance/sys/subsidyType/deleteSubsidyTypeById', { params });
}

//根据ID查询补助类型详情
export function getSubsidyTypeById(params) {
    return request.get('/eccard-finance/sys/subsidyType/detailById', { params });
}

//导出补助冲正清单样例
export function exportSubsidyRightingList(params) {
    return request.get('/eccard-finance/subsidyRecord/exportSubsidyRightingList', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}

//导出清单样例
export function exportSubsidyList(params) {
    return request.get('/eccard-finance/subsidyRecord/exportSubsidyList', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}


/* 次数充值明细 */

//次数充值记录分页列表
export function getFrequencyRechargeList(params) {
    return request.get('/eccard-finance/frequency/tradeRecord/getFrequencyTradeRecordListByPage', { params });
}

//获取次数充值汇总信息(当日)
export function getFrequencyRechargeTotal(params) {
    return request.get(`/eccard-finance/frequency/tradeRecord/getFrequencyTradeRecordTotalByDay/${params}`);
}
//导出次数充值记录
export function getFrequencyRechargeListByExport(params) {
    return request.get('/eccard-finance/frequency/tradeRecord/getFrequencyTradeRecordListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}

/**
 * 次数充值明细--打印
 */
export function getFrequencyRechargeListByPrint(params) {
    return request.get('/eccard-finance/frequency/tradeRecord/getFrequencyTradeRecordListByPrint', { params });
}


/*
次数发放
 */
//补助类型列表
export function getFrequencyTypeList(params) {
    return request.get('/eccard-finance/sys/frequencyType/getFrequencyTypeList', { params });
}

//分页查询补助发放列表
export function getFrequencyGrantListByPage(params) {
    return request.get('/eccard-finance/frequencyProject/getFrequencyGrantListByPage', { params });
}

//获取补助发放列表-导出打印
export function getFrequencyGrantListByExport(params) {
    return request.get('/eccard-finance/frequencyProject/getFrequencyGrantListByExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    })
}

//生成补助清单
export function generateFrequencyList(params) {
    return request.post('/eccard-finance/frequencyRecord/generateFrequencyList', params);
}

//获取待补助清单-分页
export function getWaitFrequencyListByPage(params) {
    return request.get('/eccard-finance/frequencyRecord/getWaitFrequencyListByPage', { params });
}
//获取已入库补助清单-分页
export function getFrequencyListByPage(params) {
    return request.get('/eccard-finance/frequencyRecord/getFrequencyListByPage', { params });
}

//删除补助清单表
export function deleteWaitFrequency(params) {
    let data = new URLSearchParams()
    data.append("projectNo", params.projectNo)
    data.append("id", params.id)
    return request.post('/eccard-finance/frequencyRecord/deleteWaitFrequencyById', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}

//保存导入-生成补助数据
export function saveImportFrequencyList(params) {
    return request.post('/eccard-finance/frequencyProject/saveImportFrequencyList', params);
}

//修改待补助清单人员
export function updateWaitFrequency(params) {
    return request.post('/eccard-finance/frequencyRecord/updateWaitFrequencyById', params);
}

//删除入库清单人员
export function delUpdateWaitFrequency(params) {
    return request.delete('/eccard-finance/frequencyRecord/' + params);
}

//编辑补助发放项目
export function editFrequencyProject(params) {
    return request.post('/eccard-finance/frequencyProject/editFrequencyProject', params);
}

//待补助冲正清单分页列表
export function getWaitFrequencyRightingList(params) {
    return request.get('/eccard-finance/frequencyRecord/getWaitFrequencyRightingListByPage', { params });
}
//删除待冲正清单人员
export function deleteWaitFrequencyRighting(params) {
    let data = new URLSearchParams()
    data.append("projectNo", params.projectNo)
    data.append("id", params.id)
    return request.post('/eccard-finance/frequencyRecord/deleteWaitFrequencyRightingById', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}

//保存导入的补助冲正数据
export function saveImportFrequencyRightingList(params) {
    return request.post('/eccard-finance/frequencyProject/saveImportFrequencyRightingList', params);
}

//审核补助或补助冲正的数据
export function auditFrequencyImport(params) {
    let data = new URLSearchParams()
    for (let key in params) {
        data.append(key, params[key])
    }
    return request.post('/eccard-finance/frequencyProject/auditFrequencyImport', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}

//导出次数冲正清单样例
export function exportFrequencyRightingList(params) {
    return request.get('/eccard-finance/frequencyRecord/exportFrequencyRightingList', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}

//导出次数清单样例
export function exportFrequencyList(params) {
    return request.get('/eccard-finance/frequencyRecord/exportFrequencyList', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}


/*
次数类型
 */
//次数类型分页列表
export function getFrequencyTypeListByPage(params) {
    return request.get('/eccard-finance/sys/frequencyType/getFrequencyTypeListByPage', { params });
}
//新增删除次数类型
export function addFrequencyType(params) {
    return request.post('/eccard-finance/sys/frequencyType/addFrequencyType', params);
}
//编辑次数类型
export function updateFrequencyTypeById(params) {
    return request.post('/eccard-finance/sys/frequencyType/updateFrequencyTypeById', params);
}
//删除次数类型
export function deleteFrequencyTypeById(params) {
    return request.delete('/eccard-finance/sys/frequencyType/deleteFrequencyTypeById', { params });
}

//根据ID查询次数类型详情
export function getFrequencyTypeById(params) {
    return request.get('/eccard-finance/sys/frequencyType/detailById', { params });
}


//获取开户策略列表-分页
export function getAccountStrategyListByPage(params) {
    return request.get('/eccard-finance/sys/accountStrategy/getAccountStrategyListByPage', { params });
}
//获取开户策略列表-分页
export function getAccountStrategyInfoById(params) {
    return request.get('/eccard-finance/sys/accountStrategy/getAccountStrategyInfoById', { params });
}
//删除开户策略
export function deleteAccountStrategyById(params) {
    return request.delete('/eccard-finance/sys/accountStrategy/deleteAccountStrategyById', { params });
}
//新增开户策略
export function addAccountStrategy(params) {
    return request.post('/eccard-finance/sys/accountStrategy/addAccountStrategy', params);
}
//修改开户策略
export function updateAccountStrategyById(params) {
    return request.post('/eccard-finance/sys/accountStrategy/updateAccountStrategyById', params);
}
//交易类型表租户的交易类型表
export function getFinanceCostTypePage(params) {
    return request.get('/eccard-finance/cost/type/page', { params });
}

//分页查询交易来源
export function getTradeSourceList(params) {
    return request.get('/eccard-finance/trade/source/page', { params });
}
//新增交易来源
export function saveTradeSource(params) {
    return request.post('/eccard-finance/trade/source/save', params);
}
//修改交易来源
export function updateTradeSource(params) {
    return request.put('/eccard-finance/trade/source/update', params);
}
//删除交易来源
export function deleteTradeSource(params) {
    return request.delete('/eccard-finance/trade/source/' + params);
}


/* 统计报表 */
//获取终端类型-下拉选择列表
export function getTerminalTypeList(params) {
    return request.get('/eccard-finance/report/form/getTerminalType', { params });
}
//根据终端类型获取设备-下拉选择列表
export function getDeviceByTerminalType(params) {
    return request.get('/eccard-finance/report/form/getDeviceByTerminalType', { params });
}
//v1.2出纳交款明细交易类型
export function cashierDetailCostTypeList(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierDetail/costTypeList', { params });
}
//v1.2出纳交款明细交易钱包
export function cashierDetailWalletList(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierDetail/walletList', { params });
}

//出纳明细报表列表
export function cashierDetail(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierDetail', { params });
}

//出纳明细导出
export function cashierDetailExport(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierDetail/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}

//分页查询现金充值明细
export function getRechargeCashList(params) {
    return request.get('/eccard-finance/report/form/recharge/cash/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//导出现金充值明细
export function rechargeCashExport(params) {
    return request.get('/eccard-finance/report/form/recharge/cash/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}

//分页查询出纳交款
export function getCashierPaymentList(params) {
    return request.get('/eccard-finance/report/form/cashier/payment/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//出纳交款-导出
export function cashierPaymentExport(params) {
    return request.get('/eccard-finance/report/form/cashier/payment/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}



//分页查询交款明细
export function getCashierPaymentDetailList(params) {
    return request.get('/eccard-finance/report/form/cashier/payment/detail', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//交款明细-导出
export function cashierPaymentDetailExport(params) {
    return request.get('/eccard-finance/report/form/cashier/payment/detail/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}


//分页查询补助明细
export function getSubsidyList(params) {
    return request.get('/eccard-finance/report/form/subsidy/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}

//补助明细-导出
export function subsidyExport(params) {
    return request.get('/eccard-finance/report/form/subsidy/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}

//分页线上支付明细
export function getOnlinePayList(params) {
    return request.get('/eccard-finance/report/form/online/pay/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}

//补助明细-导出
export function onlinePayExport(params) {
    return request.get('/eccard-finance/report/form/online/pay/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}

//统计线上充值对账记录
export function getPaymentReconcileList(params) {
    return request.get('/eccard-finance/report/form/paymentReconcileList', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}

//统计线上充值对账记录-导出
export function paymentReconcileExport(params) {
    return request.get('/eccard-finance/report/form/paymentReconcileExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}

//统计次数充值明细-分页查询
export function getFrequencyList(params) {
    return request.get('/eccard-finance/report/form/frequencyList', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}

//统计次数充值明细-导出
export function frequencyExport(params) {
    return request.get('/eccard-finance/report/form/frequencyExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}

//查询统计押金明细
export function getDepositList(params) {
    return request.post('/eccard-finance/report/form/depositList', params);
}

//统计押金明细-导出
export function depositExport(params) {
    return request.get('/eccard-finance/report/form/depositStatistics', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}

//查询统计工本费明细-分页
export function getFlatCostList(params) {
    return request.post('/eccard-finance/report/form/flatCostList', params);
}

//统计工本费明细-导出
export function flatCostExport(params) {
    return request.get('/eccard-finance/report/form/flatCostStatistics', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',

    });
}

//个人账户交易明细分页查询
export function getPersonalAccountTradeList(params) {
    return request.get('/eccard-finance/report/form/personalAccountTradeDetail/page', {
        params,
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}

//个人账户交易明细-导出
export function personalAccountTradeExport(params) {
    return request.get('/eccard-finance/report/form/personalAccountTradeDetail/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}




//查询个人账户余额明细
export function getPersonalAccountBalancePage(params) {
    return request.get('/eccard-finance/report/form/personalAccountBalance/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//导出个人余额明细
export function personalAccountBalanceExport(params) {
    return request.get('/eccard-finance/report/form/personalAccountBalance/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}

//获取商户结算信息
export function getMerchantSettlementInfoPage(params) {
    return request.get('/eccard-finance/SettlementReport/merchantSettlementInfo/Page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//商户结算信息-导出
export function merchantSettlementInfoExport(params) {
    return request.get('/eccard-finance/SettlementReport/merchantSettlementInfo/Export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}

//获取个人账户结算信息-分页
export function getPersonalSettlementInfoPage(params) {
    return request.get('/eccard-finance/SettlementReport/personalSettlementInfo/Page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//个人账户结算信息-导出
export function personalSettlementInfoExport(params) {
    return request.get('/eccard-finance/SettlementReport/personalSettlementInfo/Export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}
//v1.2出纳汇总列表
export function cashierSummary(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierSummary/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
////v1.2出纳汇总列表-导出
export function cashierSummaryExport(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierSummary/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}
//v1.2出纳汇总列表-打印
export function cashierSummaryPrint(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierSummary/print', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}


//v1.2出纳汇总明细
export function cashierSummaryDetail(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierSummaryDetail', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
////v1.2出纳汇总明细-导出
export function cashierSummaryDetailExport(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierSummaryDetail/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}
//v1.2出纳汇总明细-打印
export function cashierSummaryDetailPrint(params) {
    return request.get('/eccard-finance/report/form/cashier/cashierSummaryDetail/print', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//v1.2消费汇总
export function consumerSummary(params) {
    return request.get('/eccard-finance/report/form/consumer/consumerSummary/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
////v1.2消费汇总-导出
export function consumerSummaryExport(params) {
    return request.get('/eccard-finance/report/form/consumer/consumerSummary/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}
//v1.2消费汇总-打印
export function consumerSummaryPrint(params) {
    return request.get('/eccard-finance/report/form/consumer/consumerSummary/print', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//v1.2系统结算汇总
export function systemSettlement(params) {
    return request.get('/eccard-finance/report/form/systemSettlement/page', {
        params, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
    });
}
//v1.2系统结算-导出
export function systemSettlementExport(params) {
    return request.get('/eccard-finance/report/form/systemSettlement/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        }, paramsSerializer: function (params) {
            return Qs.stringify(params, { arrayFormat: 'repeat' })
        },
        responseType: 'blob',
    });
}
//v1.2系统结算----打印
export function systemSettlementPrint(params) {
    return request.get('/eccard-finance/report/form/systemSettlement/print', { params });
}
//v1.2系统结算----明细
export function systemSettlementDetail(params) {
    return request.get('/eccard-finance/report/form/systemSettlementDetail/detail', { params });
}
//v1.2系统结算明细----打印
export function systemSettlementDetailPrint(params) {
    return request.get('/eccard-finance/report/form/systemSettlementDetail/print', { params });
}

//资阳口腔定制报表
//分页查询充值汇总报表
export function rechargeSum(params) {
    return request.get('/eccard-finance/oral/pageRechargeSum', { params });
}
//消费分类汇总报表
export function oralConsumeSum(params) {
    return request.get('/eccard-finance/oral/pageOralConsumeSum', { params });
}
//卡片分类汇总报表
export function oralCardSum(params) {
    return request.get('/eccard-finance/oral/pageOralCardSum', { params });
}
//导出充值汇总报表
export function exportRechargeSumList(params) {
    return request.post('/eccard-finance/oral/exportRechargeSumList',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}

//导出消费分类汇总报表
export function exportConsumeSumList(params) {
    return request.post('/eccard-finance/oral/exportConsumeSumList',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}
//导出卡片分类汇总报表
export function exportCardSumList(params) {
    return request.post('/eccard-finance/oral/exportCardSumList',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}

//数据统计汇总报表
export function consumeReportList(params) {
    return request.get('/eccard-finance/oral/consumeReportList', { params });
}
//导出数据统计汇总报表
export function consumeReportListExport(params) {
    return request.post('/eccard-finance/oral/consumeReportListExport',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}
//数据统计汇总报表----打印
export function consumeReportPrint(params) {
    return request.get('/eccard-finance/oral/consumeReportPrint', { params });
}

//电费报表查询
export function eleReportList(params) {
    return request.get('/eccard-finance/oral/eleReportList', { params });
}
//导出电费报表
export function eleReportListExport(params) {
    return request.post('/eccard-finance/oral/eleReportListExport',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}
//电费报表打印
export function eleReportPrint(params) {
    return request.get('/eccard-finance/oral/eleReportPrint', { params });
}

//资阳口腔补助余额，现金余额数据查询
export function balanceReportList(params) {
    return request.get('/eccard-finance/oral/balanceReportList', { params });
}
//资阳口腔补助余额，现金余额数据导出
export function balanceReportExport(params) {
    return request.post('/eccard-finance/oral/balanceReportExport',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}
//资阳口腔补助余额，现金余额数据打印
export function balanceReportPrint(params) {
    return request.get('/eccard-finance/oral/balanceReportPrint', { params });
}




/* *
 *  交易管理
 */
//查询钱包列表
export function getWalletList(params) {
    return request.get('/eccard-finance/WalletInfo/getWalletList', { params });
}

//更新钱包使用状态
export function upateWalletStatusEntity(params) {
    return request.post('/eccard-finance/WalletInfo/upateWalletStatusEntity', params);
}

//查询钱包参数信息
export function getWalletParamConfig(params) {
    return request.get('/eccard-finance/WalletInfo/getWalletParamConfig', { params });
}


//修改钱包参数信息
export function updateWalletParamConfig(params) {
    return request.post('/eccard-finance/WalletInfo/updateWalletParamConfig', params);
}


//获取钱包的交易方式
export function getWalletTradeMode(params) {
    return request.get('/eccard-finance/WalletInfo/getWalletTradeMode', { params });
}

//更新钱包支付方式使用状态
export function updateWalletTradeModeStatus(params) {
    return request.post('/eccard-finance/WalletInfo/updateWalletTradeModeStatus', params);
}




//查询统一收费类型
export function getUnifiedChargeTypeList(params) {
    return request.get('/eccard-finance/unifiedchargetype/getUnifiedChargeTypeListByPage', { params });
}

//新增统一收费类型
export function addUnifiedChargeType(params) {
    return request.post('/eccard-finance/unifiedchargetype/addUnifiedChargeType', params);
}

//修改统一收费类型
export function updateUnifiedChargeType(params) {
    return request.post('/eccard-finance/unifiedchargetype/updateUnifiedChargeTypeById', params);
}

//删除统一收费类型
export function deleteUnifiedChargeType(params) {
    return request.delete('/eccard-finance/unifiedchargetype/deleteUnifiedChargeTypeById', { params });
}

/* 统一收费 */
//查询统一收费类型列表（select）
export function getUnifiedChargeType(params) {
    return request.get('/eccard-finance/unifiedchargetype/getUnifiedChargeTypeList', { params });
}

//查询统一收费列表
export function getUnifiedChargeList(params) {
    return request.get('/eccard-finance/uniPro', { params });
}

//生成统一收费记录清单
export function generateUni(params) {
    return request.post('/eccard-finance/uniRecords/generateUniList', params);
}
//查询待保存清单列表
export function getWaitUniListByPage(params) {
    return request.get('/eccard-finance/uniRecords/getWaitUniListByPage', { params });
}
//查询已保存清单列表
export function getUniListByPage(params) {
    return request.get('/eccard-finance/uniRecords/getUniListByPage', { params });
}
//删除待保存清单表
export function deleteWaitUni(params) {
    let data = new URLSearchParams()
    data.append("projectNo", params.projectNo)
    data.append("id", params.id)
    return request.post('/eccard-finance/uniRecords/deleteWaitUniById', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}
//查询已保存清单列表
export function exportUniList(params) {
    return request.get('/eccard-finance/uniRecords/exportUniList', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}
//新增统一收费列表
export function addUnifiedCharge(params) {
    return request.post('/eccard-finance/uniPro/saveUniProject', params);
}
//编辑统一收费列表
export function editUnifiedCharge(params) {
    return request.post('/eccard-finance/uniPro/editUniProject', params);
}
//删除待保存清单表
export function auditUniProject(params) {
    let data = new URLSearchParams()
    data.append("auditRemark", params.auditRemark)
    data.append("auditStatus", params.auditStatus)
    data.append("projectId", params.projectId)
    return request.post('/eccard-finance/uniPro/auditUniProject', data, {
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
            Accept: "*/*"
        }
    });
}




/**
 * 导出个人交易明细
 */
export function getPersonTradeListByPrint(params) {
    return request.get('/eccard-finance/AccountBasic/getPersonTradeListByPrint', { params });
}

/**
 * 充值明细--打印
 */
export function getRechargeListByPrint(params) {
    return request.get('/eccard-finance/CashReport/getRechargeListByPrint', { params });
}

/**
 * 现金发放--打印
 */
export function getCashGrantListByPrint(params) {
    return request.get('/eccard-finance/CashGrant/getCashGrantListByPrint', { params });
}

/**
 * 补助发放列表--打印
 */
export function getSubsidyGrantListByPrint(params) {
    return request.get('/eccard-finance/subsidyProject/getSubsidyGrantListByPrint', { params });
}


/**
 * 次数发放列表--打印
 */
export function getFrequencyGrantListByPrint(params) {
    return request.get('/eccard-finance/frequencyProject/getFrequencyGrantListByPrint', { params });
}


/**
 * 水控消费汇总
 */
export function waterConsumerSummaryList(params) {
    return request.get('/eccard-finance/report/form/consumer/waterConsumerSummary/page', { params });
}


//水控消费汇总-导出
export function waterConsumerSummaryExport(params) {
    return request.get('/eccard-finance/report/form/consumer/waterConsumerSummary/export', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',

    });
}

//水控消费汇总-打印
export function waterConsumerSummaryPrint(params) {
    return request.get('/eccard-finance/report/form/consumer/waterConsumerSummary/print', { params });
}
//水控交易明细-分页
export function waterTradeDetail(params) {
    return request.post('/eccard-finance/report/form/consumer/waterTradeDetail/page', params);
}
//水控交易明细-退款
export function waterTradeRefund(params) {
    return request.post(`/eccard-finance/report/form/consumer/waterTradeRefund/${params}`);
}

//水控交易汇总-分页
export function waterTradeSummary(params) {
    return request.post('/eccard-finance/report/form/consumer/waterTradeSummary/page', params);
}


//钱包账户异常列表
export function errorAccountPage(params) {
    return request.post('/eccard-finance/WalletInfo/queryErrorWalletBalanceList', params);
}


//钱包账户异常列表-导出
export function errorAccountExport(params) {
    return request.post('/eccard-finance/WalletInfo/export',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}


/**
 * 水控充值赠送明细
 */
export function waterRechargeGiftPage(params) {
    return request.get('/eccard-finance/report/form/recharge/waterRechargeGiftPage', { params });
}


//水控充值赠送明细-导出
export function waterRechargeGiftExport(params) {
    return request.get('/eccard-finance/report/form/recharge/waterRechargeGiftExport', {
        params,
        headers: {
            'Content-Type': 'application/octet-stream',
            Accept: "*/*"
        },
        responseType: 'blob',
    });
}


/**
 * 消费分类汇总分页查询
 */
export function consumptionCategorySummaryPage(params) {
    return request.post('/eccard-finance/report/form/consumer/consumptionCategorySummaryPage',params);
}


//消费分类汇总-导出
export function consumptionCategorySummaryExport(params) {
    return request.post('/eccard-finance/report/form/consumer/consumptionCategorySummary/export',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}

//消费分类汇总-打印
export function consumptionCategorySummaryPrint(params) {
    return request.post('/eccard-finance/report/form/consumer/consumptionCategorySummary/print', params);
}


/**
 * 商户交易结算分页查询
 */
export function merchantTradeSettlementPage(params) {
    return request.post('/eccard-finance/report/form/consumer/merchantTradeSettlementPage',params);
}


//商户交易结算-导出
export function merchantTradeSettlementExport(params) {
    return request.post('/eccard-finance/report/form/consumer/merchantTradeSettlement/export',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}

//商户交易结算-打印
export function merchantTradeSettlementPrint(params) {
    return request.post('/eccard-finance/report/form/consumer/merchantTradeSettlement/print', params);
}


/**
 * 电控缴费明细分页查询
 */
export function queryElectricControlPaymentDetailsPage(params) {
    return request.post('/eccard-finance/report/form/queryElectricControlPaymentDetailsPage',params);
}


//电控缴费明细-导出
export function queryElectricControlPaymentDetailsExport(params) {
    return request.post('/eccard-finance/report/form/queryElectricControlPaymentDetails/export',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}

/**
 * 转账分类汇总分页查询
 */
export function queryTransferSummaryPage(params) {
    return request.post('/eccard-finance/report/form/queryTransferSummaryPage',params);
}


//转账分类汇总导出
export function queryTransferSummaryExport(params) {
    return request.post('/eccard-finance/report/form/queryTransferSummary/export',
        params,
        {
            headers: {
                'Content-Type': 'application/octet-stream',
                Accept: "*/*"
            },
            responseType: 'blob',
        }
    );
}