<template>
  <el-dialog :model-value="isShow" title="上传照片" width="500px" :before-close="beforeClose" :append-to-body="true">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="state.rules" :model="state.form">
        <el-form-item label="照片:" prop="faceImg">
          <kade-face-image-upload :action="uploadFaceImage" icon="iconuser" v-model="state.form.faceImg" />
        </el-form-item>
        <el-form-item label="卡片类型:" prop="cardCategory">
          <el-select v-model="state.form.cardCategory">
            <el-option v-for="(item,index) in cardCategoryList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-radio-group v-model="state.form.cardCategory">
            <el-radio v-for="(item,index) in cardCategoryList" :key="index" :label="item.value">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElRadioGroup,
  ElRadio,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict.js";
import { addFace,uploadUserFaceV2 } from "@/applications/eccard-basic-data/api";
import { uploadFaceImage } from "@/applications/unified_portal/api";
import faceImageUpload from "@/components/faceImageUpload";
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElRadioGroup,
    ElRadio,
    ElButton,
    "kade-face-image-upload": faceImageUpload,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const cardCategoryList = useDict("PERSON_CARD_CATEGORY");
    const store = useStore();
    const formDom = ref(null);
    const state = reactive({
      form: {},
      rules: {
        faceImg: [
          {
            required: true,
            message: "请上传照片",
            trigger: "change",
          },
        ],
        cardCategory: [
          {
            required: true,
            message: "请选择卡片类型",
            trigger: "change",
          },
        ],
      },
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          state.form = {};
        }
      }
    );
    const handleExceed = () => {
      ElMessage.error("最多上传一个文件！");
    };

    const handleSuccess = (res) => {
      console.log(res);
      state.form.annexUrl = res.data;
    };

    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let fn =JSON.parse(sessionStorage.getItem('kade-common-oss-params')).ossUse === 1?uploadUserFaceV2:addFace
          let params = {
            ...state.form,
            userId: store.state.userInfo.rowData.id,
          };
          let { code, message } = await fn(params);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
        } else {
          return false;
        }
      });
    };
    const beforeClose = () => {
      context.emit("close", false);
      nextTick(() => {
        formDom.value.clearValidate();
      });
    };
    return {
      uploadFaceImage,
      cardCategoryList,
      formDom,
      state,
      handleExceed,
      handleSuccess,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 100% !important;
  }
  .el-input__inner {
    width: 100% !important;
  }
  .el-date-editor.el-input {
    width: 100%;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
