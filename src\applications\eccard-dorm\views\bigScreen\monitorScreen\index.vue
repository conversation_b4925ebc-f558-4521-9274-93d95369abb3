<template>
  <div class="box">
    <kade-header-box :title="route.params.buildName + route.params.unitNum+'单元监控大屏'" />

    <div class="main">
      <el-row :gutter="15" style="height: 100%;">
        <el-col :span="12">
          <kade-border-box style="height: 100%;">
            <kade-person-msg />
          </kade-border-box>
        </el-col>
        <el-col :span="12" class="right">
          <kade-border-box class="notice">
            <kade-notice-msg />
          </kade-border-box>
          <div class="room-msg">
            <div class="room-msg-left">
              <kade-border-box style="margin-bottom: 15px;flex: 1;">
                <kade-repair-msg />
              </kade-border-box>
              <kade-border-box style="flex:1">
                <kade-violation-msg />
              </kade-border-box>
            </div>
            <kade-border-box class="room-msg-right">
              <kade-warning-msg />
            </kade-border-box>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { reactive, onMounted, onBeforeUnmount } from "vue"
import { useRouter, useRoute } from "vue-router"
import { hourStr, time_to_sec } from "@/utils/date.js"
import { setScreenCompatibility } from "@/utils"
import { ElCol, ElRow, } from "element-plus"
import borderBox from "../components/borderBox.vue"
import headerBox from "../components/headerBox.vue"

// import labelBox from "../components/labelBox.vue"
import personMsg from "./components/personMsg.vue"
import noticeMsg from "./components/noticeMsg.vue"
import repairMsg from "./components/repairMsg.vue"
import violationMsg from "./components/violationMsg.vue"
import warningMsg from "./components/warningMsg.vue"
export default {
  name: "monitorScreen",
  components: {
    ElCol, ElRow,
    "kade-header-box": headerBox,
    "kade-border-box": borderBox,
    "kade-person-msg": personMsg,
    "kade-notice-msg": noticeMsg,
    "kade-repair-msg": repairMsg,
    "kade-violation-msg": violationMsg,
    "kade-warning-msg": warningMsg,
    // "kade-label-box": labelBox
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const state = reactive({
      timer: null,
      timerOut: null,
    })
    onMounted(() => {
      state.timerOut = setTimeout(() => {
        state.timer = setInterval(() => {
          let nowTimeSec = time_to_sec(hourStr(new Date))
          let startTimeSec = time_to_sec(CONFIG.SCREEN_CKECK_WORK_TIME[0])
          let endTimeSec = time_to_sec(CONFIG.SCREEN_CKECK_WORK_TIME[1])
          if (nowTimeSec > startTimeSec && nowTimeSec < endTimeSec) {
            router.push({
              name: 'CheckWorkScreen',
              params: {
                ...route.params
              }
            })
          }
        }, 1000);
      }, CONFIG.SCREEN_TIMEOUT);
      setScreenCompatibility();
      window.addEventListener('resize', () => {
        setScreenCompatibility();
      });
    })
    onBeforeUnmount(() => {
      if (state.timerOut) {
        clearInterval(state.timerOut)
      }
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      route,
      state,
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  background: #001034;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .main {
    flex: 1;
    padding: 0 20px 20px;

    .right {
      display: flex;
      flex-direction: column;

      .notice {
        flex: 1;
        margin-bottom: 15px;
      }

      .room-msg {
        // height: 70%;
        display: flex;

        .room-msg-left {
          width: 50%;
          margin-right: 15px;
          display: flex;
          flex-direction: column;
        }

        .room-msg-right {
          width: 50%;
        }
      }
    }
  }
}
</style>