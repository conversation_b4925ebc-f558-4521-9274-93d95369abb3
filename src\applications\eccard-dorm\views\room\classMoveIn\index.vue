<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="60px" inline size="mini">
        <el-form-item label="年级">
          <el-select v-model="state.form.deptCollegeId" @change="gradeChange">
            <el-option :label="item.deptName" :value="item.id" v-for="(item, index) in state.gradeList" :key="index">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级">
          <el-select v-model="state.form.deptClassId">
            <el-option :label="item.deptName" :value="item.id" v-for="(item, index) in state.gradeClassList" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="宿舍房间入住列表">
      <template #extra>
        <el-button icon="el-icon-daochu" size="small" class="btn-purple" @click="handleExport">导出</el-button>
      </template>
      <el-table style="width: 100%" :data="state.data" height="55vh" border stripe v-loading="state.loading">
        <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :prop="item.prop"
          :label="item.label" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total"
          :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import { ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElButton, ElPagination } from "element-plus"
import { reactive, onMounted } from 'vue'
import { deptStayInfoList, exportDeptStayInfo } from '@/applications/eccard-dorm/api.js'
import { userDeptGradeList, userDeptGradeClassList } from '@/applications/eccard-sys/api.js'
const column = [
  { label: "年级", prop: "collegeName", width: "150px" },
  { label: "班级", prop: "className" },
  { label: "总人数", prop: "totalPersonCount" },
  { label: "男生人数", prop: "malePersonCount" },
  { label: "女生人数", prop: "femalePersonCount" },
  { label: "住校人数", prop: "totalStayPersonCount" },
  { label: "住校男生", prop: "maleStayPersonCount" },
  { label: "住校女生", prop: "femaleStayPersonCount" },
  { label: "房间数", prop: "totalRoomCount" },
  { label: "男生房间数", prop: "maleRoomCount" },
  { label: "女生房间数", prop: "femaleRoomCount" },
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination
  },
  setup() {
    const state = reactive({
      gradeList: [],
      gradeClassList: [],
      form: {
        currentPage: 1,
        pageSize: 10
      },
      total: 0,
      data: [],
      loading: false
    })
    const handlePageChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()

    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      getList()
    }
    const getUserDeptGradeList = async () => {
      let { data } = await userDeptGradeList()
      state.gradeList = data
    }
    const getUserDeptGradeClassList = async (id) => {
      let { data } = await userDeptGradeClassList(id)
      state.gradeClassList = data
    }
    const gradeChange = val => {
      if (val) {
        getUserDeptGradeClassList(val)
      }else{
        state.gradeClassList=[]
      }
    }
    const getList = async () => {
      state.loading = true
      try {
        let { data: { dataList, totalCount } } = await deptStayInfoList(state.form)
        state.data = dataList
        state.total = totalCount
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleExport = async () => {
      let res = await exportDeptStayInfo(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url
      link.setAttribute("download", '班级入住列表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
      getUserDeptGradeList()

    })
    return {
      column,
      state,
      handlePageChange,
      handleSizeChange,
      gradeChange,
      handleSearch,
      handleReset,
      handleExport
    }
  }
}
</script>