﻿import {
  createRouter,
  createWebHistory,
  createWebHashHistory
} from "vue-router";
import View from "@/components/view";

const routes = [
  {
    path: "/",
    redirect: "/main",
    meta: {
      noauth: true
    }
  },
  {
    path: "/404",
    name: "NotFound",
    component: () => import("@/components/404"),
    meta: {
      noauth: true
    }
  },

  {
    path: "/test",
    name: "test",
    component: () => import("../views/test"),
    meta: {
      noauth: true
    }
  },

  {
    path: "/monitor-screen/:tenantId/:buildName/:buildId/:unitNum",
    name: "MonitorScreen",
    component: () => import("../views/bigScreen/monitorScreen"),
    meta: {
      noauth: true
    }
  },
  {
    path: "/check-work-screen/:tenantId/:buildName/:buildId/:unitNum",
    name: "CheckWorkScreen",
    component: () => import("../views/bigScreen/checkWorkScreen"),
    meta: {
      noauth: true
    }
  },
  {
    path: "/AI-screen/:buildName/:buildId/:unitNum",
    name: "AIScreen",
    component: () => import("../views/bigScreen/AIScreen"),
    meta: {
      noauth: true
    }
  },
  {
    path: "/main",
    component: View,
    name: "Main",
    redirect: { name: "Panel" },
    children: [
      {
        path: "panel",
        name: "Panel",
        component: () =>
          import(/* webpackChunkName: "main" */ "@/components/panel")
      }
    ]
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404"
  }
];
const fn =
  CONFIG.ROUTE_TYPE === "hash" ? createWebHashHistory : createWebHistory;
const router = createRouter({
  history: fn(
    CONFIG.IS_ROUTE_BASE_REWRITE ? CONFIG.ROUTE_BASE : "/eccard-dorm"
  ),
  scrollBehavior: () => ({ y: 0 }),
  routes
});

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router;
