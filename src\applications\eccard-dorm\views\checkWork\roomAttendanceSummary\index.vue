<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择" :size="size" />
        </el-form-item>
        <el-form-item label="考勤时段">
          <el-select clearable v-model="state.form.attendancePeriodId" placeholder="全部">
            <el-option v-for="(item, index) in state.periodIdList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <!--         <el-form-item label="是否晚归">
          <el-select clearable v-model="state.form.isLateReturn" placeholder="全部">
            <el-option v-for="(item, index) in isLateReturnList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否未归">
          <el-select clearable v-model="state.form.isNotReturn" placeholder="全部">
            <el-option v-for="(item, index) in isNotReturnList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="宿舍考勤汇总列表">
      <template #extra>
        <el-button class="btn-purple" icon="el-icon-bottom" type="" size="mini" @click="exportClick">导出</el-button>
      </template>

      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column show-overflow-tooltip prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="buildType" label="楼栋类型" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.buildType) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="roomString" label="房间" width="300px"
          align="center"></el-table-column>
        <!-- <el-table-column show-overflow-tooltip prop="deptName" label="组织机构" align="center"></el-table-column> -->
        <el-table-column show-overflow-tooltip prop="attendanceDate" label="考勤日期" align="center">
          <template #default="scope">
            {{ timeFilter(scope.row.attendanceDate) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="attendancePeriodName" label="考勤时段" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="dueNumber" label="入住人数" align="center"></el-table-column>
        <el-table-column prop="actuaNumber" label="正常考勤人数" align="center">
          <template #default="scope">
            <div :style="{ 'backgroundColor': scope.row.actuaNumber == scope.row.dueNumber ? '#03c316' : '' }">
              {{ scope.row.actuaNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="leaveNumber" label="请假人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': scope.row.leaveNumber > 0 ? '#aaaaaa' : '', color: scope.row.leaveNumber > 0 ? '#fff' : '' }">
              {{ scope.row.leaveNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lateNumber" label="晚归人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).lateReturnNumber > 0 ? '#f59a23' : '', color: attendanceNum(scope.row).lateReturnNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).lateReturnNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lateNumber" label="晚离人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).lateLeaveNumber > 0 ? '#f59a23' : '', color: attendanceNum(scope.row).lateLeaveNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).lateLeaveNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="未归人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).notReturnNumber > 0 ? '#d9001b' : '', color: attendanceNum(scope.row).notReturnNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).notReturnNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="未离人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).notLeaveNumber > 0 ? '#d9001b' : '', color: attendanceNum(scope.row).notLeaveNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).notLeaveNumber }}</div>
          </template>
        </el-table-column>
        <!--         <el-table-column prop="notReturnNumber" label="异常人数" align="center">
          <template #default="scope">
            <div :style="{'backgroundColor':scope.row.errorNumber>0?'#d9001b':'',color:scope.row.errorNumber>0?'#fff':''}">{{scope.row.errorNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="晚离寝人数" align="center">
          <template #default="scope">
            <div :style="{'backgroundColor':scope.row.lateOutNumber>0?'#955fff':'',color:scope.row.lateOutNumber>0?'#fff':''}">{{scope.row.lateOutNumber}}</div>
          </template>
        </el-table-column> -->
        <!--         <el-table-column prop="lateFlag" label="是否晚归" align="center">
          <template #default="scope">
            {{dictionaryFilter(scope.row.lateFlag)}}
          </template>
        </el-table-column>
        <el-table-column prop="notReturnFlag" label="是否未归" align="center">
          <template #default="scope">
            {{dictionaryFilter(scope.row.notReturnFlag)}}
          </template>
        </el-table-column> -->
        <el-table-column label="详情" align="center" width="200px">
          <template #default="scope">
            <el-button v-if="scope.row.attendancePeriodName.includes('归')" type="text" @click="details('未归', scope.row)"
              size="mini" class="green">未归</el-button>
            <el-button v-if="scope.row.attendancePeriodName.includes('归')" type="text" @click="details('晚归', scope.row)"
              size="mini" class="green">晚归</el-button>

            <el-button v-if="scope.row.attendancePeriodName.includes('离')" type="text" @click="details('未离', scope.row)"
              size="mini" class="green">未离</el-button>
            <el-button v-if="scope.row.attendancePeriodName.includes('离')" type="text" @click="details('晚离', scope.row)"
              size="mini" class="green">晚离</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-summary-details :type="state.type" :tableRow="state.tableRow" :dialogVisible="state.dialogVisible"
      @close="() => state.dialogVisible = false"></kade-summary-details>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { dateStr } from "@/utils/date.js"
import details from "./components/details.vue"
import { onMounted } from '@vue/runtime-core';
import { useDict } from "../../../../../hooks/useDict";
import linkageSelect from "../../../components/linkageSelect.vue";
import { attendanceRoomSummaryList, exportAttendanceRoomSummary, attendancePeriodList } from '@/applications/eccard-dorm/api.js'
const linkageData = {
  area: { label: '区域', valueKey: 'areaPath', key: 'areaPath' },
  buildingType: { label: '楼栋类型', valueKey: 'buildType' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  floor: { label: '楼层', valueKey: 'floorNum' },
  room: { label: '房间', valueKey: 'roomNo' }
}
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    "kade-summary-details": details,
    'kade-linkage-select': linkageSelect
  },
  setup() {
    const isNotReturnList = useDict('SYS_BOOL_STRING')
    const isLateReturnList = useDict('SYS_BOOL_STRING')
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      total: 0,
      data: [],
      requestDate: [],
      dialogVisible: false,
      type: '',
      loading: false,
      periodIdList: '',
      tableRow: ''
    });
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true
      try {
        let { data: { list, total } } = await attendanceRoomSummaryList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const getPeriodIdList = async () => {
      let { data } = await attendancePeriodList()
      state.periodIdList = data
    }
    const details = (type, row) => {
      console.log(row)
      state.type = type
      state.dialogVisible = true
      state.tableRow = row
    };

    const attendanceNum = (row) => {
      if (row.attendancePeriodName.includes('归')) {
        return {
          lateReturnNumber: row.lateNumber,
          lateLeaveNumber: 0,
          notReturnNumber: row.notReturnNumber,
          notLeaveNumber: 0,
        }
      } else {
        return {
          lateReturnNumber: 0,
          lateLeaveNumber: row.lateNumber,
          notReturnNumber: 0,
          notLeaveNumber: row.notReturnNumber,
        }
      }
    }

    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = []
      getList()
    };
    const exportClick = async () => {
      let res = await exportAttendanceRoomSummary(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '宿舍考勤汇总表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
      getPeriodIdList()
    })
    return {
      state,
      details,
      isNotReturnList,
      isLateReturnList,
      linkageData,
      attendanceNum,
      linkageChange,
      close,
      handleSearch,
      handleReset,
      exportClick,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-date-editor.el-input) {
  width: 210px;
}

:deep(.el-icon-date:before) {
  display: none;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog) {
  border-radius: 8px;
  padding-bottom: 20px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 210px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-table td) {
  margin: 0;
  padding: 0;
  height: 50px;
}

:deep(.el-table .cell) {
  margin: 0;
  padding: 0;
}

:deep(.el-table td div) {
  box-sizing: border-box;
  height: 50px;
  line-height: 50px;
}
</style>