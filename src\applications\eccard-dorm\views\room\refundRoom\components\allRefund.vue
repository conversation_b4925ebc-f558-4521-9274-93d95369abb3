<template>
  <el-dialog :model-value="isShow" title="批量退宿" width="70%" :before-close="beforeClose" :close-on-click-modal="false" v-loading="state.loading">
    <el-form label-width="100px" size="mini" style="margin-top:20px" inline>
      <kade-linkage-select :value="state.form" :data="linkageData" @change="linkageChange" />
      <el-form-item label="人员编号">
        <el-input v-model="state.form.userCode" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="人员姓名">
        <el-input v-model="state.form.userName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="人员组织">
        <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
      </el-form-item>
      <el-form-item label="辅导员编号/姓名">
        <el-input v-model="state.form.counsellorKey" placeholder="请输入" />
      </el-form-item>
      <el-button size="mini" @click="handleSearch" class="btn-blue" icon="el-icon-search">搜索</el-button>
      <el-button size="mini" @click="handleExport" class="btn-deep-blue" icon="el-icon-daochu">导出</el-button>
    </el-form>
    <el-table :data="state.dataList" height="30vh" border>
      <el-table-column :label="item.label" :prop="item.prop" :width="item.width" v-for="(item, index) in column" :key="index" align="center" show-overflow-tooltip></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.submitForm.total" :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <el-form label-width="120px" size="mini" ref="formRef" style="margin-top:20px" :model="state.submitForm" :rules="rules" inline>
      <el-form-item label="退宿总人数：">
        <el-input v-model="state.submitForm.total" disabled></el-input>
      </el-form-item>
      <el-form-item label="退宿时间：" prop="checkOutTime">
        <el-date-picker v-model="state.submitForm.checkOutTime" type="date" placeholder="请选择"></el-date-picker>
      </el-form-item>
      <el-form-item label="退宿备注：">
        <el-input v-model="state.submitForm.checkOutRemark" type="textarea" placeholder="请输入" show-word-limit maxlength="200"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">取&nbsp;&nbsp;消</el-button>
        <el-button @click="submit()" type="primary" size="mini">确&nbsp;&nbsp;认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElDatePicker, ElTable, ElTableColumn, ElPagination, ElMessage } from "element-plus";
import { reactive, watch, ref, nextTick } from 'vue';
import { downloadXlsx } from "@/utils"
import { getCheckOutList, checkOutExport, batchCheckOut } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import { timeStr } from "../../../../../../utils/date";
const column = [
  { prop: "areaName", label: "区域", width: "" },
  { prop: "roomString", label: "房间", width: "260px" },
  { prop: "bedNum", label: "床位", width: "" },
  { prop: "userCode", label: "人员编号", width: "" },
  { prop: "userName", label: "人员姓名", width: "" },
  { prop: "deptName", label: "人员组织", width: "" },
]
const linkageData = {
  area: { label: '区域', valueKey: "areaPath", key: "areaPath" },
  building: { label: '楼栋', valueKey: "buildId" },
  unit: { label: '单元', valueKey: "unitNum" },
  floor: { label: '楼层', valueKey: "floorNum" },
  room: { label: '房间', valueKey: "roomId" },
}
const rules = {
  checkOutTime: [
    {
      required: true,
      message: "请选择退宿时间",
      trigger: "change",
    },
  ],
}
export default {
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-dept-select-tree": deptSelectTree,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const formRef = ref(null)

    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],

      submitForm: {
        total: 0,
      },
    })

    watch(() => props.isShow, val => {
      if (val) {
        state.form = {
          currentPage: 1,
          pageSize: 10
        }
        state.submitForm = {
          total: 0,
        }
        state.dataList = []
        state.submitForm.total = 0
        getList()
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })

    const getList = async () => {
      let { data: { list, total } } = await getCheckOutList(state.form)
      state.dataList = list
      state.submitForm.total = total
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleSearch = () => {
      getList()
    }
    const handleExport = async () => {
      let res = await checkOutExport(state.form)
      downloadXlsx(res, "可退宿列表.xlsx")
    }
    const handlePageChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          state.loading = true
          try {
            let params = {
              ...state.form,
              ...state.submitForm,
              checkOutTime: timeStr(state.submitForm.checkOutTime)
            }
            let { code, message } = await batchCheckOut(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("close", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        }
      })
    }
    const beforeClose = () => {
      context.emit("close", false)
    }
    return {
      linkageData,
      column,
      formRef,
      rules,
      state,
      linkageChange,
      handleSearch,
      handleExport,
      handlePageChange,
      handleSizeChange,
      submit,
      beforeClose
    }
  }
};
</script>
<style lang="scss" scoped>
.export-header {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-upload {
  text-decoration: underline;
}

.pagination {
  border: 1px solid $border-color;
  border-top: 0;
  padding: 10px;
  border-radius: 0 0 10px 10px;
}
</style>