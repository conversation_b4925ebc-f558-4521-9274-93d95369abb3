<template>
  <div>
    <p style="margin:10px auto 0px 10px;">
      <el-form inline size="mini">
        <el-form-item label="审核状态" v-if="!state.form.pageType && state.form.pageType != '0'">
          <el-select v-model="state.form.exmResult" :clearable="true">
            <el-option v-for="(item,index) in examList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核类型">
          <el-select v-model="state.form.ataType" :clearable="true">
            <el-option v-for="(item,index) in examType" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" :clearable="true">
          <el-date-picker v-model="state.form.createTimeStart" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="结束时间" :clearable="true">
          <el-date-picker v-model="state.form.createTimeEnd" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>&nbsp;&nbsp;
          <el-button size="mini" type="primary" :icon="Download" @click="handleClick()">导出</el-button>
        </el-form-item>
      </el-form>
    </p>
    <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
      <el-table-column label="审核状态" prop="exmResult" align="center">
        <template #default="scope">
          <span style="color:#02D200" v-if="scope.row.exmResult == '0'">待审核</span>
          <span style="color:#3399FF" v-if="scope.row.exmResult == '1'">已通过</span>
          <span style="color:#FF3F3F" v-if="scope.row.exmResult == '2'">未通过</span>
        </template>
      </el-table-column>
      <el-table-column label="审核类型" prop="ataType" align="center">
        <template #default="scope">
          {{ scope.row.ataType == 'overtime' ? '加班补录' : '出勤补录' }}
        </template>
      </el-table-column>
      <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
      <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
      <el-table-column label="组织机构" prop="orgNameList" align="center"></el-table-column>
      <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
      <el-table-column label="提交时间" prop="submitDate" align="center">
        <template #default="scope">
          {{ getdatetime(scope.row.submitDate) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="" align="center">
        <template #default="scope">
          <el-button @click="edit(scope.row , 'exam')" type="text" size="mini" v-if="scope.row.exmResult == '0'">审核</el-button>
          <el-button @click="edit(scope.row , 'info')" type="text" size="mini" v-if="scope.row.exmResult != '0'">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <attend-add-info-edit :modelValue="state.isEdit" :title="state.type == 'add' ? '新增出勤补录' : (state.type == 'edit' ? '编辑出勤补录' : '出勤补录详情')"
                          :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'"/>

    <attend-over-time-add-info-edit :modelValue="state.isOverTimeEdit" :title="state.type == 'add' ? '新增加班补录' : (state.type == 'edit' ? '编辑加班补录' : '加班补录详情')"
                                    :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
  </div>
</template>
<script>
  import { reactive , onMounted } from "vue";
  import { getAttentionAllInfoPage , delAttentionAddInfo ,downLoadFile } from "@/applications/eccard-ams/api";
  import { downloadXlsx } from "@/utils"
  import { timeStr } from "@/utils/date.js"
  import attendAddInfoEdit from "@/applications/eccard-ams/views/attendaddinfo/components/edit.vue"
  import attendOverTimeAddInfoEdit from "@/applications/eccard-ams/views/attendovertimeaddinfo/components/edit.vue"
  import {
    ElSelect,
    ElOption,
    // ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElDatePicker,
    ElPagination,
    ElMessage,
    ElMessageBox,
    // ElDialog
  } from 'element-plus';

  export default {
    props: {
      type :{
        type: String,
        default: "",
      },
    },
    components: {
      attendAddInfoEdit,
      attendOverTimeAddInfoEdit,
      ElSelect,
      ElOption,
      ElButton,
      ElDatePicker,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      // ElButton,
      ElPagination,
      // ElMessage,
      // ElMessageBox,
      // ElDialog
    },
    setup(props ) {
      const state = reactive({
        loading: false,
        isEdit:false,
        isOverTimeEdit:false,
        form:{
          exmResult : props.type,
          ataType: '',
          createTimeStart: null,
          createTimeEnd : null,
          currentPage:1,
          pageSize:10,
          pageType:props.type,
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      const examList = [
        {label:'待审核',value:'0'},
        {label:'已通过',value:'1'},
        {label:'未通过',value:'2'},
      ];
      const examType = [
        {label:'出勤补录',value:'attention'},
        {label:'加班补录',value:'overtime'},
      ];

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionAllInfoPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }

      const edit=(row,type)=>{
        state.type=type;
        state.rowData=row;

        if(row.ataType == 'attention') {
          state.isEdit = true;
        }else{
          state.isOverTimeEdit = true;
          state.rowData["ovaId"] = state.rowData["ataId"];
        }
      }

      const handleClick=async ()=>{
        let res = await downLoadFile("/eccard-ams/api/ams/attention-addinfo/exportStr" , state.form);
        downloadXlsx(res, "补录审核列表.xlsx");
      }

      const getdatetime = (val) =>{
        return timeStr(val);
      }
      const reset=()=>{
        state.form= {
          exmResult : props.type,
          ataType: '',
          createTimeStart: '',
          createTimeEnd : '',
          currentPage:1,
          pageSize:10,
          pageType:props.type,
        };
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const del=async (row)=>{
        ElMessageBox.confirm(`确定删除当前考勤补录信息？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code,message}=await delAttentionAddInfo(row.id)
          if(code===0){
            ElMessage.success(message)
            getList()
          }
        });
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
        state.isOverTimeEdit = false;
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        del,
        close,
        examList,
        examType,
        getdatetime,
        handleClick,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
