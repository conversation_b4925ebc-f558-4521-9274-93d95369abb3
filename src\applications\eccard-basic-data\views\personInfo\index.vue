<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="关键字">
            <el-input size="small" v-model="querys.userName" placeholder="姓名或编号关键字搜索" />
          </el-form-item>
          <el-form-item label="组织机构">
            <kade-dept-select-tree style="width: 100%" :value="querys.userDept" valueKey="deptPath" :multiple="false" @valueChange="(val) => (querys.userDept = val.deptPath)" />
          </el-form-item>
          <el-form-item label="身份类别">
            <el-select clearable v-model="querys.userRole">
              <el-option v-for="(item,index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="人员状态">
            <el-select clearable v-model="querys.userState">
              <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账户状态">
            <el-select clearable v-model="querys.userAccountState">
              <el-option v-for="(item,index) in accountStatusList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="性别">
            <el-select clearable v-model="querys.userSex">
              <el-option v-for="(item,index) in sexList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input clearable v-model="querys.userTel" placeholder="输入手机号码搜索" />
          </el-form-item>
          <el-form-item label="政治面貌">
            <el-select clearable v-model="querys.userPoliticalFace">
              <el-option v-for="(item,index) in faceList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="证件类型">
            <el-select clearable v-model="querys.userIdType">
              <el-option v-for="(item,index) in cardTypes" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="证件号码">
            <el-input clearable v-model="querys.userIdNo" placeholder="输入证件号码搜索" />
          </el-form-item>
          <el-form-item label="是否住校">
            <el-select clearable v-model="querys.userIsBoarders">
              <el-option v-for="(item,index) in isSchoolList" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap v-loading="state.loading">
        <template #extra>

          <el-button icon="el-icon-plus" @click="handleEdit('')" size="small" class="btn-deep-blue">新增</el-button>
          <el-button class="btn-import" icon="el-icon-daoru" size="small" @click="handleImportPerson">导入人员信息</el-button>
          <el-button @click="exportPerson" class="btn-blue" icon="el-icon-daochu" size="small">导出</el-button>
          <!-- <el-button class="btn-green" icon="el-icon-picture" size="small">导入照片</el-button> -->
          <el-button type="warning" icon="el-icon-upload" size="small" @click="handleImportParent">导入家长信息</el-button>
          <!-- <el-button class="btn-purple" icon="el-icon-edit" size="small" @click="handleEditDept">批量修改部门</el-button> -->

          <el-dropdown trigger="click" @command="command" style="margin: 0 10px;">
            <el-button class="btn-purple" icon="el-icon-edit" size="small">批量修改部门</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="select">选择人员</el-dropdown-item>
                <el-dropdown-item command="upload">导入人员</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button class="btn-pink" icon="el-icon-edit" size="small" @click="handleEditStatus">批量修改状态</el-button>
        </template>
        <el-table style="width: 100%" :data="options.dataList" height="55vh" v-loading="options.loading" border stripe>
          <el-table-column label="用户编号" prop="userCode" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="姓名" prop="userName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="性别" align="center" prop="userSex" show-overflow-tooltip>
            <template #default="scope"> {{ dictionaryFilter(scope.row.userSex) }}</template>
          </el-table-column>
          <el-table-column label="民族" align="center" prop="userNationStr" show-overflow-tooltip>
            <template #default="scope">
              {{state.nationList.length&&scope.row.userNation?state.nationList.filter(item=>item.code==scope.row.userNation)[0].name:''
              }}</template>
          </el-table-column>
          <el-table-column label="籍贯" align="center" prop="userNativePlace" show-overflow-tooltip></el-table-column>
          <el-table-column label="政治面貌" align="center" prop="userPoliticalFace" show-overflow-tooltip>
            <template #default="scope"> {{ dictionaryFilter(scope.row.userPoliticalFace) }}</template>
          </el-table-column>
          <el-table-column label="证件类型" align="center" prop="userIdType" show-overflow-tooltip>
            <template #default="scope"> {{ dictionaryFilter(scope.row.userIdType) }}</template>
          </el-table-column>
          <el-table-column label="证件号码" align="center" prop="userIdNo" show-overflow-tooltip></el-table-column>
          <el-table-column label="手机号码" align="center" prop="userTel" show-overflow-tooltip></el-table-column>
          <el-table-column label="组织机构" align="center" prop="deptName" show-overflow-tooltip></el-table-column>
          <el-table-column label="身份类别" align="center" prop="roleName" show-overflow-tooltip></el-table-column>
          <el-table-column label="账户状态" align="center" prop="userAccountState" show-overflow-tooltip>
            <template #default="scope">{{ dictionaryFilter(scope.row.userAccountState) }}</template>
          </el-table-column>
          <el-table-column label="人员状态" align="center" show-overflow-tooltip>
            <template #default="scope">
              <el-tag size="small" :type=" scope.row.userState === 'STATE_IN' ? 'primary' : 'warning' ">{{scope.row.userStateName}}</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="用户来源" align="center" show-overflow-tooltip>
            <template #default="scope"> {{ dictionaryFilter(scope.row.userSource) }} </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button @click="handleInfo(scope.row)" type="text" size="mini">详情</el-button>
              <el-button @click="handleEdit(scope.row)" type="text" size="mini">编辑</el-button>
              <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" :page-size="options.pageSize" @current-change="(val) => pageChange(val)" @size-change="(val) => sizeChange(val)">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
    <kade-user-info-edit :roleList="state.roleList" :nationList="state.nationList" :userStateList="state.userStateList" />
    <kade-user-info-details :nationList="state.nationList" />
    <kade-batch-edit :roleList="state.roleList" :userStateList="state.userStateList" />
    <kade-import-parent v-model="state.isImportParent" />
    <kade-import-person v-model="state.isImportPerson" @success="handleSearch" />
    <kade-import-edit-dept v-model="state.isImportEditDept" @success="handleSearch" />
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElPagination,
  ElMessageBox,
  ElMessage,
  ElTag,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem
} from "element-plus";
import {
  getUserInfoListByPage,
  deleteById,
  getRolelist,
  queryUserState,
  userInfoExport,
  nationList,
} from "@/applications/eccard-basic-data/api";
import { usePagination } from "@/hooks/usePagination";
import { downloadXlsx } from "@/utils";
import { useEvent } from "@/hooks";
import { useDict } from "@/hooks/useDict.js";
import { onMounted, reactive, watch } from "vue";
import { useStore } from "vuex";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import edit from "./components/edit.vue";
import details from "./components/details";
import batchEdit from "./components/batchEdit";
import importParent from "./components/importParent";
import importPerson from "./components/importPerson";
import importEditDept from "./components/importEditDept";

const getQuerys = () => ({
  userTel: "",
  userIdNo: "",
  userName: "",
  userDept: "",
  userRole: "",
  userState: "",
  userAccountState: "",
  userSex: "",
  userPoliticalFace: "",
  userIdType: "",
  userIsBoarders: "",
});
export default {
  components: {
    "el-table": ElTable,
    "el-button": ElButton,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-tag": ElTag,
    ElSelect,
    ElOption,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    "kade-dept-select-tree": deptSelectTree,
    "kade-user-info-edit": edit,
    "kade-user-info-details": details,
    "kade-batch-edit": batchEdit,
    "kade-import-parent": importParent,
    "kade-import-person": importPerson,
    "kade-import-edit-dept": importEditDept,
  },
  setup() {
    const statusList = useDict("BASE_USER_STATE");
    const accountStatusList = useDict("SYS_ENABLE");
    const sexList = useDict("SYS_SEX");
    const faceList = useDict("USER_POLITICAL_OUTLOOK");
    const isSchoolList = useDict("SYS_BOOL_STRING");
    const cardTypes = useDict("BASE_ID_TYPE");
    const store = useStore();
    const state = reactive({
      loading: false,
      isEdit: false,
      isImportParent: false,
      isImportPerson: false,
      isImportEditDept: false,
      rowData: {},
      roleList: [],
      userStateList: [],
      nationList: [],
    });
    watch(
      () => store.state.userInfo.isUpdateList,
      (val) => {
        val && loadData();
        store.commit("userInfo/updateState", {
          key: "isUpdateList",
          payload: false,
        });
      }
    );

    const { $on } = useEvent();
    const { options, search, querys, loadData, pageChange, sizeChange } =
      usePagination(getUserInfoListByPage, getQuerys());
    $on("personal/refresh", () => {
      loadData();
    });

    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.roleList = data;
      store.commit("userInfo/updateState", {
        key: "roleList",
        payload: data,
      });
    };

    const queryUserStatusList = async () => {
      let { data } = await queryUserState();
      state.userStateList = data;
      store.commit("userInfo/updateState", {
        key: "userStateList",
        payload: data,
      });
    };

    const getNationList = async () => {
      let { data } = await nationList();
      state.nationList = data;
      store.commit("userInfo/updateState", {
        key: "nationList",
        payload: data,
      });
    };

    const handleInfo = (row) => {
      store.commit("userInfo/updateState", {
        key: "rowData",
        payload: row,
      });
      store.commit("userInfo/updateState", {
        key: "isDetails",
        payload: true,
      });
    };
    const handleEdit = (row) => {
      store.commit("userInfo/updateState", {
        key: "rowData",
        payload: row,
      });
      store.commit("userInfo/updateState", {
        key: "isEdit",
        payload: true,
      });
    };
    const handleDel = ({ userName, id }) => {
      ElMessageBox.confirm(`确认删除用户"${userName}"?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          const { message, code } = await deleteById({ id });
          if (code === 0) {
            ElMessage.success(message);
          } else {
            ElMessage.error(message);
          }
          loadData();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    const exportPerson = () => {
      userInfoExport(querys).then((res) => {
        downloadXlsx(res, "人员信息表.xlsx");
      });
    };
    const handleEditDept = () => {
      store.commit("userInfo/updateState", {
        key: "isBatchEdit",
        payload: {
          type: "dept",
          isShow: true,
        },
      });
    };

    const command = (c) => {
      switch (c) {
        case "select":
          handleEditDept();
          break;
        case "upload":
          handleUploadEditBacthDept();
          break;
      }
    };
    const handleUploadEditBacthDept = () => {
      state.isImportEditDept = true
    }

    const handleEditStatus = () => {
      store.commit("userInfo/updateState", {
        key: "isBatchEdit",
        payload: {
          type: "status",
          isShow: true,
        },
      });
    };

    const handleImportParent = () => {
      state.isImportParent = true
    }
    const handleImportPerson = () => {
      state.isImportPerson = true
    }
    const handleReset = () => {
      Object.assign(querys, getQuerys());
    };



    onMounted(() => {
      getNationList();
      queryRolelist();
      queryUserStatusList()
    });
    return {
      statusList,
      accountStatusList,
      sexList,
      faceList,
      isSchoolList,
      cardTypes,
      state,
      options,
      querys,
      pageChange,
      sizeChange,
      handleInfo,
      handleEdit,
      loadData,
      handleSearch: search,
      handleReset,
      handleDel,
      exportPerson,
      handleEditDept,
      handleEditStatus,
      handleImportParent,
      handleImportPerson,
      command
    };
  },
};
</script>
<style lang="scss" scoped>
.personnelInformation {
  width: 100%;
  height: 100%;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-dialog) {
  margin-top: 5vh !important;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }

  .el-input__inner {
    width: 200px;
    box-sizing: border-box;
  }
}
</style>