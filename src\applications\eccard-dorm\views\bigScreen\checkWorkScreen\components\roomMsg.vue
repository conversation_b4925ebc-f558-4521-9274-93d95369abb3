<template>
  <div class="room-msg">
    <div class="room-msg-title">房间考勤结果</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="5000" indicator-position="none"
      v-if="state.roomList.length">
      <el-carousel-item v-for="(item, index) in Math.ceil(state.roomList.length / 60)" :key="index" style="color:#fff">
        <div class="room-box">
          <div class="inline" v-for="(v, i) in state.roomList.slice(index * 60, (index + 1) * 60) " :key="i"
            :class="(v.actualNumber < v.checkPersonCount) && 'error'">
            <div class="room-item">
              <div class="room">{{ v.roomName }}</div>
              <div class="room-num">归寝：{{ v.actualNumber }}/{{ v.checkPersonCount }}</div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="none-data" v-else>
      <div>暂无数据</div>
    </div>
  </div>
</template>
<script>
import { onMounted, onBeforeUnmount, reactive } from "vue"
import { useRoute } from "vue-router"
import { requestDefaultTime } from "@/utils/reqDefaultDate.js"
import { timeStr } from "@/utils/date.js"
import { ElCarousel, ElCarouselItem } from "element-plus"
import { attendanceScreenRoomInfo } from "@/applications/eccard-dorm/api"

export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      roomList: [],
      timer: null
    })
    const getData = async () => {
      let params = {
        attendancePeriodCode: 4,
        ...route.params,
        beginDate: timeStr(requestDefaultTime()[0]),
        endDate: timeStr(requestDefaultTime()[1]),
      }
      let { data } = await attendanceScreenRoomInfo(params)
      state.roomList = data.map(item => {
        return {
          ...item,
          actualNumber: item.actualNumber ? item.actualNumber : 0,
          checkPersonCount: item.checkPersonCount ? item.checkPersonCount : 0,
        }
      })
      console.log(Math.ceil(state.roomList.length / 60));
    }
    onMounted(() => {
      getData()
      state.timer = setInterval(() => {
        getData()
      }, 30000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      state
    }
  }
}
</script>
<style lang="scss" scoped>
.room-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .room-msg-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .room-box {
    background: #001034;
    width: 100%;
    height: 100%;
    overflow-y: auto;

    .inline {
      box-sizing: border-box;
      display: inline-block;
      width: 16%;
      height: 8.9%;
      margin-bottom: 1%;
      margin-right: 0.8%;
      &:nth-child(6n) {
        margin-right: 0;
      }
      &:nth-child(n+54) {
        margin-bottom: 0;
      }
    }
    .room-item {
      height: 100%;
      box-sizing: border-box;
      padding: 5px;
      background: #0f9a6c;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .room {
        font-size: 24px;
        color: #FFFFFF;
        text-align: center;
      }
      .room-num {
        font-size: 18px;
        color: #FFFFFF;
        text-align: center;
      }

    }
  }

}

.error {
  background-color: #b6731b !important;
}

.el-carousel {
  width: 100%;
  height: 100%;
}

.none-data {
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}</style>