import request from '@/service';



//疾病类型列表
export function healthyDiseasesType(params) {
  return request.post('/eccard-basic-data/healthyDiseasesType/list', params,);
}
//疾病类型新增
export function healthyDiseasesTypeAdd(params) {
  return request.post('/eccard-basic-data/healthyDiseasesType', params,);
}
//疾病类型修改
export function healthyDiseasesTypeUpdate(params) {
  return request.put('/eccard-basic-data/healthyDiseasesType', params,);
}
//疾病类型删除
export function healthyDiseasesTypeDel(params) {
  return request.delete(`/eccard-basic-data/healthyDiseasesType/${params}`,);
}


//疾病列表
export function healthyDiseases(params) {
  return request.post('/eccard-basic-data/healthyDiseases/list', params,);
}
//疾病新增
export function healthyDiseasesAdd(params) {
  return request.post('/eccard-basic-data/healthyDiseases', params,);
}
//疾病修改
export function healthyDiseasesUpdate(params) {
  return request.put('/eccard-basic-data/healthyDiseases', params,);
}
//疾病删除
export function healthyDiseasesDel(params) {
  return request.delete(`/eccard-basic-data/healthyDiseases/${params}`,);
}