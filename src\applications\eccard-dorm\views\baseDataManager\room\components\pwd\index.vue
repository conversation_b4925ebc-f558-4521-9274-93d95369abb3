<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" title="密码管理" width="800px" :before-close="handleClose">
    <div class="btn-box">
      <el-button @click="handleEdit({})" size="small" class="btn-green" icon="el-icon-plus">新增</el-button>
    </div>
    <el-table style="width: 100%;margin-bottom:20px" max-height="600px" :data="state.dataList" ref="multipleTable" v-loading="state.loading" border stripe>
      <el-table-column show-overflow-tooltip label="密码" prop="pwd" align="center">
        <template #default="scope">
          <span style="margin-right: 10px"> {{ scope.row.checked ? scope.row.pwd :"**********" }}</span><i class="el-icon-view" @click="scope.row.checked=!scope.row.checked"></i>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="类型" prop="type" align="center">
        <template #default="scope">
          {{scope.row.type==1?'人员密码':'房间密码'}}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="下发状态" prop="下发状态" align="center">
        <template #default="scope">
          {{scope.row.downStatus==1?'已下发':'未下发'}}
        </template>
      </el-table-column>
      <el-table-column width="200px" label="操作" align="center">
        <template #default="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="height:1px"></div>
    <kade-room-pwd-edit v-model:modelValue="state.isEdit" :rowData="state.rowData" @update:modelValue="close" />
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElTable, ElTableColumn, ElMessageBox, ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity'
import { roomPwdList, roomPwdDel, } from "@/applications/eccard-dorm/api";
import pwdEdit from "./edit"
import { watch } from '@vue/runtime-core';
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElTable,
    ElTableColumn,
    "kade-room-pwd-edit": pwdEdit
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      isEdit: false,
      rowData: {},
      dataList: [],
    })
    watch(() => props.modelValue, val => {
      if (val) {
        getList()
      }
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data } = await roomPwdList({ roomId: props.rowData.id })
        state.dataList = data.map(item => {
          return {
            ...item,
            checked: false
          }
        })
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleEdit = (row) => {
      state.rowData = { type: 2, ...row, roomId: props.rowData.id }
      state.isEdit = true
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await roomPwdDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }


    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    return {
      state,
      handleEdit,
      handleDel,
      handleClose,
      close
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-box {
  text-align: right;
  padding: 20px 0 10px;
}
</style>