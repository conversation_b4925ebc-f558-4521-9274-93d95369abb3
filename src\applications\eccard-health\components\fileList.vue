<template>
  <el-dialog :model-value="modelValue" title="文件列表" width="800px" :before-close="handleClose">
    <div class="padding-box">
      <el-table :data="state.fileList" size="mini" v-loading="state.loading" border stripe>
        <el-table-column label="名称" align="center" prop="fileName"></el-table-column>
        <el-table-column label="类型" align="center" prop="fileType"></el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button size="mini" type="text" @click="handleUpload(scope.row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElTable, ElTableColumn, ElButton, } from "element-plus"
import { reactive, ref, watch } from 'vue'
import { uploadUrlFile } from "@/utils"
export default {
  components: {
    ElDialog, ElTable, ElTableColumn, ElButton,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    fileList: {
      types: Object,
      default: () => ({})
    },
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      fileList: []
    });
    watch(() => props.modelValue, async val => {
      if (val) {
        state.fileList = props.fileList.map(item => {
          let list = item.split("/")
          let text = decodeURIComponent(list[list.length - 1].split("_")[0])
          let fileType = decodeURIComponent(list[list.length - 1].split(".")[1])
          let fileName = text + "." + fileType
          return {
            fileName,
            fileType,
            url: item
          }
        })
      }
    })
    const handleUpload = row => {
      uploadUrlFile(row.url, row.fileName)
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };

    return {
      formRef,
      state,
      handleUpload,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  margin: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.img-box {
  display: flex;

  img {
    width: 100px;
    height: 100px;
    margin-right: 10px;
  }
}
</style>