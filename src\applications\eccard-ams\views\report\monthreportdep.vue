<template>
  <div style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.form.atdDay" type="month" placeholder="开始时间搜索"  format="YYYY-MM" value-format="YYYY-MM-DD" :clearable="true" />
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="每月考勤部门汇总表">
      <template #extra>
        <el-button size="mini" type="primary" icon="Download" @click="handleClick(1)">导出</el-button>
        <el-button size="mini" type="danger" icon="Printer" @click="handleClick(2)">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="月份" prop="amr_month" align="center"></el-table-column>
        <el-table-column label="部门" prop="dept_name" align="center"></el-table-column>
        <el-table-column label="应签到人数" prop="user_count" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="正常签到人数" prop="attend_Num" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="迟到人数" prop="late_num" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="早退人数" prop="leave_num" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { downloadXlsx , print } from "@/utils"
  import { monthStr } from "@/utils/date.js"
  import deptSelectTree from '@/components/tree/deptSelectTree'
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    // ElInput,
    // ElSelect,
    // ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElDatePicker,
    ElMessage,
    // ElMessageBox
  } from 'element-plus';

  import { downLoadFile , getMonthDepStatis } from "@/applications/eccard-ams/api";

  export default {
    props: {
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    components: {
      ElForm,
      ElFormItem,
      // ElInput,
      // ElSelect,
      // ElOption,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      ElDatePicker,
      "kade-dept-select-tree":deptSelectTree,
    },
    setup() {
      const symbolList = useDict("ATTENTION_REPORT_ATTENDTYPE");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          atdDay:'',
          orgNameList:'',
          pageNum:1,
          pageSize:10
        },
        groupList:[],
        statsList:null,
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        columnList:[],
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        if(state.form.atdDay) {
          state.form.atdDay = monthStr(state.form.atdDay);
        }else{
          // ElMessage.warning("请输入开始时间！");state.loading=false;return;
        }
        let {data}=await getMonthDepStatis(state.form)
        state.dataList=data.list

        state.total=parseInt(data.count)
        state.loading=false;
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          pageNum:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      // const del=async (row)=>{
      //   let {code,message}=await delSysMessage(row.id)
      //   if(code===0){
      //     ElMessage.success(message)
      //     getList()
      //   }
      // }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      const handleClick=async (flag)=>{
          if (flag == 1) {
            let res = await downLoadFile("/eccard-ams/api/ams/attention-day-report/monthDepStatisExport", state.form);
            downloadXlsx(res, "每月部门汇总报表.xlsx");
          }else{
            let {code, message} = await downLoadFile("/eccard-ams/api/ams/attention-day-report/monthDepStatisExport?type=2", state.form, 2);
            if (code == 0) {
              print(message, '每月部门汇总报表')
            } else {
              ElMessage.warning(message);
            }
          }
      }

      onMounted(async ()=>{
        getList();
      })
      return {
        state,
        symbolList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        // del,
        close,
        // getRowStyle,
        handleClick,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
