<template>
  <el-dialog append-to-body="true" width="1200px" v-model="state.showValue" :title="title"  :before-close="update" destroy-on-close="true" :close-on-click-modal="false" :modal="true">
    <p>
      <el-form inline size="mini">
        <el-form-item label="设备类型">
          <el-select v-model="state.form.atdType" :clearable="true">
            <el-option v-for="(item,index) in typeList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="终端型号">
          <el-select v-model="state.form.atdModel" :clearable="true">
            <el-option v-for="(item,index) in modelList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域">
          <area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
                            @valueChange="(val) => (state.form.areaId = val.id)" />
        </el-form-item>
        <el-form-item label="机号">
          <el-input v-model="state.form.atdNum" placeholder="机号搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select v-model="state.form.atdStatus" :clearable="true">
            <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input v-model="state.form.atdName" placeholder="设备名称" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="设备IP">
          <el-input v-model="state.form.atdIp" placeholder="设备IP" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </p>
      <!--<template #extra>-->
        <!--<el-button size="mini" type="success" icon="el-icon-plus" @click="add('','add')">绑定</el-button>-->
        <!--&lt;!&ndash; <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleClick(scope.row)">编辑</el-button>-->
        <!--<el-button size="mini" type="danger" icon="el-icon-circle-close" @click="handleClick(scope.row)">删除</el-button> &ndash;&gt;-->
      <!--</template>-->
    <span v-for="(sitem) in getListKeys()" :key="sitem + 'ddd'"><el-tag class="ml-2" type="success" v-for="(item , index) in state.selectData[sitem]" :key="index" closable @close="closeselected(item)">{{item.atdName}}</el-tag></span>
    <el-table style="width: 100%"  size="small" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" @select="handleSelectionChange"  @select-all="handleSelectAll" border stripe>
      <el-table-column type="selection" width="55" />
      <el-table-column label="所属区域" prop="areaName" align="center"></el-table-column>
      <!--<el-table-column label="设备类型" prop="atdType" align="center"></el-table-column>-->
      <el-table-column label="设备机号" prop="atdNum" align="center"></el-table-column>
      <el-table-column label="设备名称" prop="atdName" align="center"></el-table-column>
      <el-table-column label="设备型号" prop="atdModel" align="center"></el-table-column>
      <el-table-column label="连接类型" prop="wstConType" align="center"></el-table-column>
      <el-table-column label="设备状态" prop="atdStatus" align="center">
        <template #default="scope">
          {{ scope.row.atdStatus ? dictionaryFilter(scope.row.atdStatus) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="设备IP" prop="atdIp" align="center"></el-table-column>
      <!--<el-table-column label="操作" prop="" align="center">-->
        <!--<template #default="scope">-->
          <!--<el-button @click="edit(scope.row , 'edit')" type="text" size="mini">考勤记录</el-button>-->
          <!--<el-button @click="edit(scope.row , 'edit')" type="text" size="mini">取消绑定</el-button>-->
        <!--</template>-->
      <!--</el-table-column>-->
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <p style="width: 100%;text-align:center;margin:20px 20px 20px 20px">
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
    </p>

    <!--<bind-dev-group-user :isShow="state.isEdit" :type="state.type" :data="state.rowData" @close="close" @edit="state.type='edit'" />-->

  </el-dialog>
</template>
<script>
import { useDict } from "@/hooks/useDict";
import { getDevInfoPage , batchBindDevInfo } from "@/applications/eccard-ams/api";
import { computed, reactive, ref, watch ,onMounted , nextTick} from "vue";
// import bindDevGroupUser from "@/applications/eccard-ams/views/attenddev/binddevgroupuser.vue";
import areaSelectTree from '@/components/tree/areaSelectTree';

import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
  ElMessage,
  ElMessageBox,
  ElTag,
} from 'element-plus';
// import { addRole, updateRole } from "@/applications/eccard-basic-data/api";
// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    type:{
      type: String,
      default: "",
    },
    data:{
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    // bindDevGroupUser,
    areaSelectTree,
    ElTag,

    // "el-button": ElButton,
    // "el-form": ElForm,
    // "el-form-item": ElFormItem,
    // "el-input": ElInput,
    // "el-switch": ElSwitch,
    // 'el-tabs': ElTabs,
    // 'el-tab-pane': ElTabPane,
    // 'el-image' :
  },
  setup(props, context) {
    const multipleTable = ref(null);
    const statusList = useDict("DEV_INFO_STATUS");
    const typeList = useDict("DORM_ROOM_DEVICE_TYPE");
    const modelList = useDict("DEV_INFO_MODEL");

    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      loading: false,
      showValue:false,
      form: {
        atdType:'',
        atdModel:'',
        atdNum:'',
        atdStatus:'',
        atdName:'',
        atdIp:'',
        degId:'',
        pageNum:1,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: "",
      type: "",
      selectData:{},
      isFresh:0,
      model: props.data,
      isNewPage:false,
    });
    const rules = {
      degName: [
        { required: true, message: "请输入设备分组名称" },
        { max: 50, message: "类别名称不能超过50个字符" },
      ],
    };


    //分页
    const getList=async ()=>{
      state.loading=true
      state.form["selectedFlag"] = '-1';
      let {data}=await getDevInfoPage(state.form)
      state.dataList=data.list;
      state.total=parseInt(data.count);
      state.loading=false;

      nextTick(() => {
        state.selectData[state.form.pageNum].forEach((item)=>{
          multipleTable.value.toggleRowSelection(state.dataList.filter((sitem)=>{return sitem.atdId == item.atdId})[0], true)
        });
      })
    }
    const reset=()=>{
      state.form={
        degId : props.data.degId,
        currentPage:1,
        pageSize:10
      }
      state.form.pageNum=1;
      state.selectData = {};
      getList();
    }
    const closeselected = (val)=>{
      let keys = getListKeys();
      keys.forEach((keyitem)=>{
        let li = state.selectData[keyitem].filter((item) => {
          return item.atdId != val.atdId
        });
        state.selectData[keyitem] = li;
      });
      nextTick(() => {
        let al = state.dataList.filter((sitem)=>{return sitem.atdId == val.atdId});
        if(al.length > 0) {
          multipleTable.value.toggleRowSelection(al[0], false);
        }
      })
    }
    const getListKeys = ()=>{
      let list = new Array();
      for(let key in state.selectData){
        list.push(key);
      }
      return list;
    }
    const getListValues = ()=>{
      let list = new Array();
      for(let key in state.selectData){
        list = list.concat(state.selectData[key]);
      }
      return list;
    }
    const search=()=>{
      state.form.pageNum=1;
      state.selectData = {};
      getList();
    }
    const handlePageChange=(val)=>{
      state.form.pageNum=val
      getList();
    }
    const handleSizeChange=(val)=>{
      state.form.pageSize=val
      getList()
    }
    const handleSelectionChange = (selection)=>{
      if(state.form.pageNum && selection) {
        state.selectData[state.form.pageNum] = selection;
      }
    }
    const handleSelectAll = (selection)=>{
      if(state.form.pageNum && selection) {
        state.selectData[state.form.pageNum] = selection;
      }
    }

    const cancel = () => {
      context.emit("update:modelValue", state.isFresh);
    };
    const submit = () => {
      if(!state.selectData){
        return ElMessage.error('请先选择要绑定的设备信息!')
      }
      ElMessageBox.confirm(`确定绑定已选择设备？`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let param = getListValues().map((item)=>item.atdId).join(',')
        let {code,message} = await batchBindDevInfo(param , props.data.degId)
        if(code===0){
          state.isFresh = '1';
          ElMessage.success(message)
          cancel();
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    onMounted(()=>{
      getList();
    });
    watch(
      () => props.modelValue,
      (n) => {
        if (n) {
          state.showValue = true;
          state.form.degId = props.data.degId;
          state.selectData = {};
          state.form.pageNum = 1;
          getList();
        }else{
          state.showValue = false;
        }
      }
    );
    return {
      attrs,
      reset,
      search,
      update,
      formRef,
      cancel,
      submit,
      rules,
      handlePageChange,
      handleSizeChange,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
      statusList,
      typeList,
      modelList,
      handleSelectionChange,
      closeselected,
      multipleTable,
      handleSelectAll,
      getListKeys,
      getListValues,
    };
  },
};
</script>
