<template>
  <el-dialog :modelValue="modelValue" :title="title" :width="'1100px'" :before-close="cancel" :append-to-body="true">
    <el-form ref="formRef" :label-width="'130px'" :rules="rules" :model="state.model" size="small">
      <el-form-item label="班次名称" prop="atcClassname">
        <el-input placeholder="请输入（不超过30字）" v-model="state.model.atcClassname" :readonly="disType == 'info'" />
      </el-form-item>
      <el-form-item label="班次类型" prop="atcType" v-if="disType != 'info'">
        <el-radio-group v-model="state.model.atcType" class="ml-4">
          <el-radio label="一天一次打卡" size="large">一天一次打卡</el-radio>
          <el-radio label="一天一次上下班" size="large">一天一次上下班</el-radio>
          <!--           <el-radio label="一天两次上下班" size="large">一天两次上下班</el-radio>
          <el-radio label="一天三次上下班" size="large">一天三次上下班</el-radio> -->
        </el-radio-group>
      </el-form-item>
      <el-form-item label="班次类型" prop="atcType" v-if="disType == 'info'">
        <el-input v-model="state.model.atcType" :readonly="disType == 'info'" />
      </el-form-item>
      <div v-if="state.model.atcType == '一天一次打卡'">
        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">一次打卡：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[0].clcUpDate" start="00:00" step="00:01" end="23:59"
                              placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpSnum" :min="0" :max="1000"
                               controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpEnum" :min="0" :max="1000"
                               controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[0].clcDownDate" start="00:00" step="00:01"
                              end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownSnum" :min="0" :max="1000"
                               controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownEnum" :min="0" :max="1000"
                               controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">休息时间：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="休息开始时间">
              <el-time-select v-model="state.model.atcRestSdate" start="00:00" step="00:01" end="23:59"
                              placeholder="休息开始时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="休息结束时间">
              <el-time-select v-model="state.model.atcRestEdate" start="00:00" step="00:01" end="23:59"
                              placeholder="休息结束时间" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div v-if="state.model.atcType == '一天一次上下班'">
        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">一次班：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[0].clcUpDate" start="00:00" step="00:01" end="23:59"
                placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[0].clcDownDate" start="00:00" step="00:01"
                end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">休息时间：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="休息开始时间">
              <el-time-select v-model="state.model.atcRestSdate" start="00:00" step="00:01" end="23:59"
                placeholder="休息开始时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="休息结束时间">
              <el-time-select v-model="state.model.atcRestEdate" start="00:00" step="00:01" end="23:59"
                placeholder="休息结束时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="午休是否需要打卡">
              <el-radio-group v-model="state.model.isNoonClock" class="ml-4">
                <el-radio :label="1" size="large">是</el-radio>
                <el-radio :label="0" size="large">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!--  <div v-if="state.model.atcType == '一天两次上下班'">
        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">一次班：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[0].clcUpDate" start="00:00" step="00:01" end="23:59"
                placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[0].clcDownDate" start="00:00" step="00:01"
                end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">二次班：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[1].clcUpDate" start="00:00" step="00:01" end="23:59"
                placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[1].clcUpSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[1].clcUpEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[1].clcDownDate" start="00:00" step="00:01"
                end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[1].clcDownSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[1].clcDownEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
      </div>


      <div v-if="state.model.atcType == '一天三次上下班'">
        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">一次班：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[0].clcUpDate" start="00:00" step="00:01" end="23:59"
                placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcUpEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[0].clcDownDate" start="00:00" step="00:01"
                end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[0].clcDownEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">二次班：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[1].clcUpDate" start="00:00" step="00:01" end="23:59"
                placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[1].clcUpSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[1].clcUpEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[1].clcDownDate" start="00:00" step="00:01"
                end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[1].clcDownSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[1].clcDownEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :sm="24">
            <span class="title">三次班：</span>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="上班">
              <el-time-select v-model="state.model.classCommutionList[2].clcUpDate" start="00:00" step="00:01" end="23:59"
                placeholder="选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：上班前">
              <el-input-number v-model="state.model.classCommutionList[2].clcUpSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="上班后">
              <el-input-number v-model="state.model.classCommutionList[2].clcUpEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="8">
            <el-form-item label="下班">
              <el-time-select v-model="state.model.classCommutionList[2].clcDownDate" start="00:00" step="00:01"
                end="23:59" placeholder="选择下班时间" />
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="打卡时段：下班前">
              <el-input-number v-model="state.model.classCommutionList[2].clcDownSnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
          <el-col :sm="8">
            <el-form-item label="下班后">
              <el-input-number v-model="state.model.classCommutionList[2].clcDownEnum" :min="0" :max="1000"
                controls-position="right" />分钟
            </el-form-item>
          </el-col>
        </el-row>
      </div> -->
    </el-form>
    <template #footer>
      <p style="text-align: center" v-if="state.disType != 'info'">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small"
          type="primary">保存</el-button>
      </p>
      <p style="text-align: center" v-if="state.disType == 'info'">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">关闭</el-button>
      </p>
    </template>

  </el-dialog>
</template>
<script>
import { reactive, watch, ref, computed } from "vue";
import { updateAttentClass, addAttentClass, getAttentionClassDetail } from "@/applications/eccard-ams/api";
import {
  ElRow,
  ElCol,
  ElTimeSelect,
  ElInputNumber,
  ElRadioGroup,
  ElRadio,
  // ElSelect,
  // ElOption,
  ElInput,
  ElForm,
  ElFormItem,
  ElButton,
  // ElPagination,
  ElMessage,
  // ElMessageBox,
  ElDialog,
} from 'element-plus';

const getDefaultModel = () => ({
  atcClassname: '',
  atcType: '一天一次上下班',
  atcTimes: '',
  atcRestSdate: '',
  atcRestEdate: '',
  classCommutionList: [
    { atcId: '', clcId: 1, clcUpDate: '', clcUpSnum: '0', clcUpEnum: '0', clcDownDate: '', clcDownSnum: '0', clcDownEnum: '0' },
    { atcId: '', clcId: 2, clcUpDate: '', clcUpSnum: '0', clcUpEnum: '0', clcDownDate: '', clcDownSnum: '0', clcDownEnum: '0' },
    { atcId: '', clcId: 3, clcUpDate: '', clcUpSnum: '0', clcUpEnum: '0', clcDownDate: '', clcDownSnum: '0', clcDownEnum: '0' },
  ],
  isNoonClock: 0
});
export default {
  emits: ["close"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: "",
    },
  },
  components: {
    ElRow,
    ElCol,
    ElRadioGroup,
    ElRadio,
    ElTimeSelect,
    ElInputNumber,
    // ElSelect,
    // ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    ElButton,
    ElDialog,
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const disType = ref(props.type);
    const state = reactive({
      model: getDefaultModel(),
      nightTimeList: null,
    });
    const rules = {
      atcClassname: [
        { required: true, message: "请输入班次名称" },
        { max: 30, message: "班次名称不能超过30个字符" },
      ],
    };

    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };

    const submit = () => {
      console.log(state.nightTimeList);
      formRef.value?.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            const fn = props.data?.atcId ? updateAttentClass : addAttentClass;
            /*
             atgName
             if(!state.model.manageUser){
            ElMessage.error('考勤组管理员信息不能为空！');return;
          }
            */
            let t = 0;
            if (state.model.atcType == '一天一次上下班') {
              if (!state.model.atcRestSdate) {
                ElMessage.error('休息开始时间不能为空！'); return;
              }
              if (!state.model.atcRestEdate) {
                ElMessage.error('休息结束时间不能为空！'); return;
              }

              if (state.model.atcRestSdate > state.model.atcRestEdate) {
                ElMessage.error('休息开始时间不能大于结束时间！'); return;
              }

              if (state.model.atcRestSdate < state.model.classCommutionList[0].clcUpDate || state.model.atcRestEdate > state.model.classCommutionList[0].clcDownDate) {
                ElMessage.error('休息时间只能在上班和下班之间时间！'); return;
              }
              t = 0;
            } else if (state.model.atcType == '一天两次上下班') {
              t = 1;
            } else if (state.model.atcType == '一天三次上下班') {
              t = 2;
            }
            for (let i = 0; i <= t; i++) {
              // if(!state.model.classCommutionList[i].clcUpDate && ){
              //   ElMessage.error('上班时间不能为空！');return;
              // }
              // if(!state.model.classCommutionList[i]. clcUpSnum){
              //   ElMessage.error('上班前分钟不能为空！');return;
              // }
              // if(!state.model.classCommutionList[i]. clcUpEnum){
              //   ElMessage.error('上班后分钟不能为空！');return;
              // }
              // if(!state.model.classCommutionList[i].clcDownDate){
              //   ElMessage.error('下班时间不能为空！');return;
              // }
              // if(!state.model.classCommutionList[i]. clcDownSnum){
              //   ElMessage.error('下班前分钟不能为空！');return;
              // }
              // if(!state.model.classCommutionList[i]. clcDownEnum){
              //   ElMessage.error('下班后分钟不能为空！');return;
              // }

              // if (state.model.classCommutionList[i].clcUpDate >= state.model.classCommutionList[i].clcDownDate) {
              //   ElMessage.error('上班时间不能大于下班时间！'); return;
              // }

            }

            let objs = { ...state.model };
            let rt = new Array();
            if (state.model.atcType == '一天一次打卡') {
              rt.push(state.model.classCommutionList[0]);
            } else if (state.model.atcType == '一天一次上下班') {
              rt.push(state.model.classCommutionList[0]);
            } else if (state.model.atcType == '一天两次上下班') {
              rt.push(state.model.classCommutionList[0]);
              rt.push(state.model.classCommutionList[1]);
            } else if (state.model.atcType == '一天三次上下班') {
              rt.push(state.model.classCommutionList[0]);
              rt.push(state.model.classCommutionList[1]);
              rt.push(state.model.classCommutionList[2]);
            }
            objs.classCommutionList = rt;
            if (objs.isNoonClock == '1') {
              if (!objs.atcRestSdate || !objs.atcRestEdate) {
                return ElMessage.error("设置午休时间打卡必须设置休息开始时间和结束时间！")
              }
            }
            const { message, code } = await fn(objs);
            if (code === 0) {
              ElMessage.success(message);
            } else {
              ElMessage.error(message);
            }
            context.emit("update:modelValue", true);
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });

    watch(
      () => props.modelValue,
      async (n) => {
        if (n) {
          state.nightTimeList = null
          if (props.data?.atcId) {
            const { code, message, data } = await getAttentionClassDetail(props.data.atcId);
            if (code != 0) {
              ElMessage.error(message); return;
            }

            let objbase = getDefaultModel();
            objbase.classCommutionList.forEach((item) => {
              let tempObj = data.classCommutionList.filter(sitem => {
                return sitem.clcId == item.clcId;
              });
              if (tempObj.length > 0) {
                item["atcId"] = tempObj[0].atcId;
                item["clcUpDate"] = tempObj[0].clcUpDate;
                item["clcUpSnum"] = tempObj[0].clcUpSnum;
                item["clcUpEnum"] = tempObj[0].clcUpEnum;
                item["clcDownDate"] = tempObj[0].clcDownDate;
                item["clcDownSnum"] = tempObj[0].clcDownSnum;
                item["clcDownEnum"] = tempObj[0].clcDownEnum;
              }
            });
            // delete data.classCommutionList;
            state.model = Object.assign(objbase, data);
            if (data.classCommutionList.length < 4) {
              for (let i = data.classCommutionList.length; i < 4; i++) {
                state.model.classCommutionList.push({ atcId: '', clcId: 1, clcUpDate: '', clcUpSnum: '0', clcUpEnum: '0', clcDownDate: '', clcDownSnum: '0', clcDownEnum: '0' });
              }
            }
            console.log(JSON.stringify(state.model))
          } else {
            state.model = getDefaultModel();
          }

          state.disType = props.type;
        }
      }
    );
    return {
      attrs,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      disType,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>
