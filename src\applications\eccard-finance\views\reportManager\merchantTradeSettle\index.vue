<template>
  <!-- 商户结算 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="商户名称:">
          <el-input placeholder="商户名称搜索" v-model="state.form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="结算类型:">
          <el-select v-model="state.form.settlementType" placeholder="请选择">
            <el-option v-for="(item, index) in state.typeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算日期:" v-if="state.form.settlementType==1">
          <el-date-picker v-model="state.dayList" type="daterange" unlink-panels :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结算日期:" v-if="state.form.settlementType==2">
          <el-date-picker v-model="state.week" format="YYYY 第 ww 周" value-format="W" type="week" placeholder="选择周">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结算日期:" v-if="state.form.settlementType==3">
          <el-date-picker v-model="state.monthList" type="monthrange" :default-time="defaultTime" range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份" unlink-panels />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="商户交易结算" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        <el-button @click="handlePrint()" icon="el-icon-printer" size="mini" class="btn-blue">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" highlight-current-row border stripe>
        <el-table-column v-for="(item) in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive, onMounted, } from "vue";
import {
  ElButton,
  ElForm,
  ElInput,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElMessage
} from "element-plus";
import { downloadXlsx, print } from "@/utils"
import { timeStr, dateStr, getWeekRange, getMonthFirst, getMonthLast, hasDateTimeRange } from "@/utils/date.js";
import { defaultTime, yesterdayTime } from "@/utils/reqDefaultDate";
import {
  merchantTradeSettlementPage,
  merchantTradeSettlementExport,
  merchantTradeSettlementPrint,
} from "@/applications/eccard-finance/api";
const column = [
  { label: "商户名称", prop: "merchantName", width: "" },
  { label: "结算类型", prop: "settlementType", width: "" },
  { label: "结算日期", prop: "settlementDate", width: "170", render: (val) => val && dateStr(val) },
  { label: "交易笔数", prop: "tradeCount", width: "" },
  { label: "交易金额", prop: "tradeAmount", width: "" },
  { label: "纠错总笔数", prop: "jcCount", width: "" },
  { label: "纠错总金额", prop: "jcAmount", width: "" },
  { label: "消费总笔数", prop: "actualCount", width: "" },
  { label: "消费总金额", prop: "actualAmount", width: "" },
]

export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElInput,
    ElDatePicker,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {
        pageNum: 1,
        pageSize: 10,
        settlementType: 1
      },
      dataList: [],
      total: 0,
      dayList: yesterdayTime(),
      week: "",
      monthList: [getMonthFirst(new Date()), getMonthLast(new Date())],
      tradeModeList: [],
      typeList: [
        { label: "按日", value: 1 },
        { label: "按周", value: 2 },
        { label: "按月", value: 3 },
      ],
    });
    const getList = async () => {
      let params = getParams()
      if (!params) return
      state.loading = true;
      try {
        let { code, data: { page: { list, total }, total: amount } } = await merchantTradeSettlementPage(params);
        if (code === 0) {
          state.dataList = list;
          state.total = total;
          if (total) {
            state.dataList.push({
              ...amount,
              merchantName: "合计"
            })
          }
        }
        state.loading = false;
      }
      catch {
        state.loading = false;

      }
    };
    const exportClick = async () => {
      let params = getParams()
      if (!params) return
      state.loading = true
      try {
        let res = await merchantTradeSettlementExport(params);
        downloadXlsx(res, "商户交易结算.xlsx")
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handlePrint = async () => {
      let params = getParams()
      if (!params) return
      state.loading = true
      try {
        let { data, code } = await merchantTradeSettlementPrint(params)
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const getParams = () => {
      let params = { ...state.form }
      if (params.settlementType == 1 && state.dayList && state.dayList.length) {
        params.startDate = timeStr(state.dayList[0])
        params.endDate = timeStr(state.dayList[1])
      } else if (params.settlementType == 2 && state.week) {
        params.startDate = getWeekRange(state.week)[0]
        params.endDate = getWeekRange(state.week)[1]
      } else if (params.settlementType == 3 && state.monthList && state.monthList.length) {
        params.startDate = timeStr(state.monthList[0])
        params.endDate = getMonthLast(state.monthList[1])
      } else {
        ElMessage.error('请选择结算日期')
        return false
      }
      if (hasDateTimeRange([params.startDate, params.endDate], 6)) {
        ElMessage.error("查询日期范围不能操作6个月")
        return false
      }
      return params
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
        settlementType: 1
      };
      state.dayList = yesterdayTime()
    };
    onMounted(() => {
      // getList();
    });
    return {
      column,
      state,
      timeStr,
      defaultTime,
      exportClick,
      handlePrint,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}

.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }

    .el-date-editor {
      width: 400px;
    }
  }
}
</style>