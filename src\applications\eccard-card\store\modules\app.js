 import { markRaw, defineAsyncComponent } from 'vue';
import { getDictionary } from '@/applications/unified_portal/api';
import { 
	actions as mixinActions,
	state as mixinState,
	getters as mixinGettgers,
} from '@/utils/tab.mixin';
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
	componentMap: {
		workStationManager: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "WorkStationManager" */ '../../views/WorkStationManager/index.vue'))),
		cardInfo: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "CardInfo" */ '../../views/CardInfo/index.vue'))),
		cardLog: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "CardLog" */ '../../views/CardLog/index.vue'))),
		virtualCard: markRaw(defineAsyncComponent(() => import(/* webpackChunkName: "VirtualCard" */ '../../views/VirtualCard/index.vue'))),
	},
	customMenus: [],  
};
const mutations = {
	updateState(state, { key, payload }) { 
		state[key] = payload;
	},
};
const actions = {
  ...mixinActions,
	async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data?data:[]));
    commit('updateState', { key: 'dictionary', payload: data?data:[] });
  },
};

const getters = {
  ...mixinGettgers,
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
  getters,
}
