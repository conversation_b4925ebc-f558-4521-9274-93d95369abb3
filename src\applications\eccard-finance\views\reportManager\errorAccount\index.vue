<template>
  <div class="padding-box" v-loading="state.loading">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="用户姓名:">
          <el-input placeholder="用户姓名搜索" v-model="state.form.userName" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="用户编号:">
          <el-input placeholder="用户编号搜索" v-model="state.form.userCode" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="负数阀值:">
          <el-input-number v-model="state.form.thresholdValue" :min="-100000" :max="0" step-strictly />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="异常账户报表">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" v-loading="state.loading" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop], rechargeReconciliationList) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" @click="detailsClick(scope.row)">查看账户明细</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-online-charge-details :rowData="state.rowData" :dialogVisible="state.dialogVisible" @close="state.dialogVisible = false" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { downloadXlsx } from "@/utils";
import { reactive } from "@vue/reactivity";
import {
  errorAccountPage,
  errorAccountExport,
} from "@/applications/eccard-finance/api";
import { onMounted, } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import details from "./components/details.vue";

const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "现金余额", prop: "cashBalance", width: "" },
  { label: "补助余额", prop: "subsidizeBalance", width: "" },
  { label: "次数余额", prop: "timesBalance", width: "" },
  { label: "最后更新时间", prop: "lastModifyTime", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElInput,
    ElInputNumber,
    "kade-dept-select-tree": deptSelectTree,
    "kade-online-charge-details": details,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      dialogVisible: false,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      rowData: {},
      tradeModeList: [],
    });
    const getList = async () => {
      state.loading = true;

      let params = { ...state.form }
      try {
        let { code, data } = await errorAccountPage(params);
        if (code === 0) {
          let { total, list } = data;
          state.detailList = list;
          state.total = total;

        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const detailsClick = (row) => {
      console.log(row);
      state.rowData = row;
      state.dialogVisible = true;
    };
    const exportClick = async () => {
      state.loading = true
      let params = { ...state.form }
      try {
        let res = await errorAccountExport(params);
        downloadXlsx(res, "异常账户报表.xlsx")
        state.loading = false;
      }
      catch {
        state.loading = false;
      }

    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
    };
    onMounted(() => {
      // getList();
    });
    return {
      column,
      state,
      timeStr,
      exportClick,
      getList,
      detailsClick,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-dialog__headerbtn) {
  font-size: 20px;
  top: 10px;
  right: 10px;
}

:deep(.el-dialog__header) {
  padding: 10px 20px;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-dialog__title) {
  font-size: 14px;
}
</style>