<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="选择人员" prop="atgUsers" v-if="state.model.disType!='info' && state.model.disType!='exam'">
        <div class="divSelectUser">
          <el-tag class="ml-2" type="success" v-if="state.model.manageUser">{{state.model.manageUser.userNum + ' ' + state.model.manageUser.userName}}</el-tag>
        </div>
        <el-button type="primary" link @click="state.model.isShowSelectedManagerUser = true;">选择</el-button>
      </el-form-item>

      <el-row :gutter="5" v-if="state.model.disType=='info' || state.model.disType=='exam'">
        <el-col :sm="12">
          <el-form-item label="用户编号">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.userNum : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="用户名称">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.userName : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="5" v-if="state.model.disType=='info' || state.model.disType=='exam'">
        <el-col :sm="12">
          <el-form-item label="组织机构">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.orgNameList : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="所属考勤组">
            <el-input :modelValue="state.model.manageUser ? state.model.manageUser.atgName : ''" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0">
        <el-col :sm="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker v-model="state.model.startDate" :value-format="'YYYY-MM-DD'" type="date" placeholder="选择开始日期"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" :size="size" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="false">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker v-model="state.model.endDate" :value-format="'YYYY-MM-DD'" type="date" placeholder="选择结束日期"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" :size="size" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="补录类型" prop="ataType" v-if="state.model.disType!='info' && state.model.disType!='exam'">
        <el-radio-group v-model="state.model.ataType">
          <el-radio label="按班次补录（根据人员所属班次进行补录）"  size="large">按班次补录（根据人员所属班次进行补录）</el-radio>
          <el-radio label="按时间补录（根据时间自动补录）" size="large">按时间补录（根据时间自动补录）</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="补录类型" prop="ataType" v-if="state.model.disType=='info' || state.model.disType=='exam'">
        <el-input v-model="state.model.ataType" :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
      </el-form-item>

      <el-row :gutter="0" v-if="state.model.ataType == '按时间补录（根据时间自动补录）'">
        <el-col :sm="2">
        </el-col>
        <el-col :sm="22">
          <el-form-item label="开始时间" prop="ataTimeStartDate">
            <el-time-picker v-model="state.model.ataTimeStartDate" :format="'HH:mm'" :readonly="state.model.disType=='info' || state.model.disType=='exam'" placeholder="选择开始时间" :size="size" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-if="state.model.ataType == '按时间补录（根据时间自动补录）'">
        <el-col :sm="2">
        </el-col>
        <el-col :sm="22">
          <el-form-item label="结束时间" prop="ataTimeEndDate">
            <el-time-picker v-model="state.model.ataTimeEndDate" :format="'HH:mm'" :readonly="state.model.disType=='info' || state.model.disType=='exam'" placeholder="选择结束时间" :size="size" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="选择班次" prop="atgManageUser" v-if="state.model.ataType == '按班次补录（根据人员所属班次进行补录）'">
        <el-row :gutter="0" v-if="!state.model.manageUser || !state.model.manageUser.attendClassObj">
          <el-col :sm="24">
            <span v-if="!state.model.manageUser">请选择补录用户！</span>
            <span v-if="state.model.manageUser && state.model.manageUser.attendClassObjList.length == 0">当前用户无班次信息！</span>
          </el-col>
        </el-row>
        <div v-if="state.model.manageUser && state.model.manageUser.attendClassObjList">
          <el-row :gutter="0" v-for="(attendObj,index1) in state.model.manageUser.attendClassObjList" :key="index1 + 'rrr'">
            <el-col :sm="5">
              <span>{{attendObj.atcType}}</span>
            </el-col>
            <el-col :sm="19">
              <el-row :gutter="0">
                <el-col :sm="4">
                  <el-row :gutter="15" v-for="(classItem1,index2) in attendObj.classCommutionList" :key="index2 + 'ttt'">
                    <el-col :sm="24" style="height:37px">
                      {{constClassList[index2]}}
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :sm="20">
                  <el-checkbox-group v-model="state.model.selectedAttendClassList">
                    <el-row :gutter="15" v-for="(classItem,index) in attendObj.classCommutionList" :key="index + 'ttt'">
                      <el-col :sm="9">
                        <el-checkbox :label="classItem.clcId + '_' + classItem.clcUpDate"  :disabled="state.model.disType=='info' || state.model.disType=='exam'" >上班：{{classItem.clcUpDate}}</el-checkbox>
                      </el-col>
                      <el-col :sm="15">
                        <el-checkbox :label="classItem.clcId + '_' + classItem.clcDownDate"  :disabled="state.model.disType=='info' || state.model.disType=='exam'" >下班：{{classItem.clcDownDate}}</el-checkbox>
                      </el-col>
                    </el-row>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </el-form-item>

      <el-form-item label="补录原因" prop="ataReson">
        <el-input v-model="state.model.ataReson"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" :rows="5" type="textarea" placeholder="请输入补录原因" />
      </el-form-item>

      <el-form-item label="是否自动审核" prop="atoExam" v-if="state.model.disType!='info' && state.model.disType!='exam'">
        <el-switch v-model="state.model.atoExam" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                   inactive-value="0" inactive-text="关" active-text="开">
        </el-switch>
      </el-form-item>


      <el-row :gutter="0"  v-if="state.model.disType=='info' || state.model.disType=='exam'">
        <el-col :sm="12">
          <el-form-item label="审核方式">
          <el-input :modelValue="state.model.atoExam == '1' ? '自动审核' : '领导审核'"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="提交时间">
          <el-input v-model="state.model.submitDate"  :readonly="state.model.disType=='info' || state.model.disType=='exam'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0"  v-if="state.model.disType=='info'">
        <el-col :sm="24">
          <el-row :gutter="0">
            <el-col :sm="24">
              审核信息
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :sm="12">
              <el-form-item label="审核结果">
                <el-input :modelValue="constExameList[state.model.exmResult]" />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item label="审核时间" prop="atoExam">
                <el-input :modelValue="state.model.exmDate"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :sm="24">
              <el-form-item label="未通过原因" prop="exmDesc" v-if="state.model.exmResult == 2">
                <el-input v-model="state.model.exmDesc" :readonly="state.model.disType=='info'" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <el-row :gutter="0"  v-if="state.model.disType=='exam'">
        <el-col :sm="24">
          <el-row :gutter="0">
            <el-col :sm="24">
              审核
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :sm="24">
              <el-form-item label="是否通过">
                <el-radio-group v-model="state.model.exmResult" class="ml-4">
                  <el-radio label="1" size="large">通过</el-radio>
                  <el-radio label="2" size="large">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="未通过原因" prop="exmDesc" v-if="state.model.exmResult == 2">
                <el-input v-model="state.model.exmDesc" :readonly="state.model.disType=='info'" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

    </el-form>
    <template #footer>
      <p style="text-align: center" v-if="state.model.disType!='info'">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>
  <selected-manageuser :modelValue="state.model.isShowSelectedManagerUser" :title="'选择人员'" @update:modelValue="selectedManagerUserClose" />
</template>
<script>
  import { reactive, watch , ref , computed , onMounted} from "vue";
import {
  // getAttentionGroupInfoPage ,
  // delAttentionGroupInfoPage ,
  examAttentionAddInfo, updateAttentionAddInfo , addAttentionAddInfo ,
  // getAttentionGroupDetail  ,
  getAttentionAddInfoDetail , getDistinctAttentionClass}
  from "@/applications/eccard-ams/api";
  import { dateStr ,hourMinStr ,timeStr } from "@/utils/date.js"
  import selectedManageuser from "@/applications/eccard-ams/views/attendaddinfo/components/selectedmanageuser.vue"
  import {
    ElRow,
    ElCol,
    ElTag,
    ElSwitch,
    // ElLink,
    // ElSelect,
    // ElOption,
  ElTimePicker,
    ElInput,
    ElForm,
    ElFormItem,
    // ElTable,
    // ElTableColumn,
    ElButton,
    // ElPagination,
    ElMessage,
    ElDatePicker,
    // ElMessageBox,
    ElCheckboxGroup,
    ElCheckbox,
    ElRadioGroup,
    ElRadio,
    ElDialog,
  } from 'element-plus';

const getDefaultModel = () => ({
  manageUser:null,
  selectedAttendClassList:[],     //选择的班次信息
  roleName: "",
  status: "ENABLE_TRUE",
  isShowSelectedManagerUser:false,
  atgType:'',
  selectedData:null,
  atoExam:0,
  disType:'info',
});
export default {
  emits: ["close"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    type :{
      type: String,
      default: "",
    },
  },
  components: {
    selectedManageuser,
    ElTimePicker,
    ElRow,
    // ElLink,
    ElCol,
    ElTag,
    ElSwitch,
    ElDatePicker,
    // ElSelect,
    // ElOption,
    ElCheckboxGroup,
    ElRadioGroup,
    ElRadio,
    ElCheckbox,
    ElInput,
    ElForm,
    ElFormItem,
    // ElTable,
    // ElTableColumn,
    ElButton,
    // selectedClassAll,
    ElDialog,
  },
  setup(props, context) {
    const weekDays = ['一','二','三','四','五','六','日'];
    const free_tableRef = ref(null);
    const fixed_tableRef = ref(null);
    const formRef = ref(null);
    const btnLoading = ref(false);
    const constClassList = ref(["一次班" , "二次班" , "三次班"]);
    const constExameList = ref(["待审核" , "已通过" , "未通过"]);
    const disType = ref(props.type);
    const state = reactive({
      model: getDefaultModel(),
    });
    const rules = {
      startDate: [
        { required: true, message: "请输入开始时间" }
      ],
      // endDate: [
      //   { required: true, message: "请输入结束时间" }
      // ],
      ataType: [
        { required: true, message: "请输入补录类型" }
      ],
      ataReson: [
        { required: true, message: "请输入补录原因" }
      ],
    };
    const selectedManagerUserClose = async (val)=>{
      if(val){
        console.log(JSON.stringify(val))
        state.model.manageUser = {};
        state.model.manageUser["userId"] = val.userId;
        state.model.manageUser["userNum"] = val.userNum;
        state.model.manageUser["userName"] = val.userName;
        state.model.manageUser["atgId"] = val.atgId;
        state.model.manageUser["atgName"] = val.atgName;
        state.model.manageUser["orgNameList"] = val.orgNameList;

        //查询此用户的班次信息
        if(val.atgId) {
          const {code, message, data} = await getDistinctAttentionClass({atgId: val.atgId});
          if (code != 0) {
            ElMessage.error(message);
          }
          state.model.manageUser["attendClassObjList"] = data;
          // state.model.manageUser.attendClassObj = data;
        }else{
          state.model.manageUser["attendClassObjList"] = [];
        }
      }else{
        state.model.manageUser = null;
      }
      state.model.isShowSelectedManagerUser = false;
    };
    const handleSelectionFreeChange = (val)=>{
      state.model.selectedFreeClassData = val;
    };
    const handleSelectionFixedChange = (val)=>{
      state.model.selectedFixedClassData = val;
    }
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const checkStr = (substring)=> {
      if(substring){
        var reg = new RegExp("[~#^$@%&!?%*]", 'g');
        if (substring.match(reg)) {
          return true;
        }
        for ( var i = 0; i < substring.length; i++) {
          var hs = substring.charCodeAt(i);
          if (0xd800 <= hs && hs <= 0xdbff) {
            if (substring.length > 1) {
              let ls = substring.charCodeAt(i + 1);
              let uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
              if (0x1d000 <= uc && uc <= 0x1f77f) {
                return true;
              }
            }
          } else if (substring.length > 1) {
            let ls = substring.charCodeAt(i + 1);
            if (ls == 0x20e3) {
              return true;
            }
          } else {
            if (0x2100 <= hs && hs <= 0x27ff) {
              return true;
            } else if (0x2B05 <= hs && hs <= 0x2b07) {
              return true;
            } else if (0x2934 <= hs && hs <= 0x2935) {
              return true;
            } else if (0x3297 <= hs && hs <= 0x3299) {
              return true;
            } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030
              || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b
              || hs == 0x2b50) {
              return true;
            }
          }
        }
      }
    };
    const submit = async () => {
      if(checkStr(state.model.ataReson)){
        ElMessage.error('补录原因不能包含特殊字符和表情图标！');
        return;
      }
      state.model.endDate = state.model.startDate;
      if(state.model.disType=='exam'){
        if (state.model.exmResult == '2' && (!state.model.exmDesc || state.model.exmDesc.toString().trim() == '')) {
          ElMessage.error('审核描述信息不能为空！');
          return;
        }
        try {
          btnLoading.value = true;
          const {message, code} = await examAttentionAddInfo(state.model);
          if (code === 0) {
            ElMessage.success(message);
          } else {
            ElMessage.error(message);
          }
          context.emit("update:modelValue", true);
        } catch (e) {
          throw new Error(e.message);
        } finally {
          btnLoading.value = false;
        }
      }else {
        formRef.value?.validate(async (valid) => {
          if (valid) {
            if (state.model.ataType == '按时间补录（根据时间自动补录）') {
              if (!state.model.ataTimeStartDate) {
                ElMessage.error('补录开始时间不能为空！');
                return;
              }
              if (!state.model.ataTimeEndDate) {
                ElMessage.error('补录结束时间不能为空！');
                return;
              }
            } else {
              if (!state.model.selectedAttendClassList || state.model.selectedAttendClassList.length == 0) {
                ElMessage.error('补录按班次信息不能为空！');
                return;
              }
            }
            // alert(JSON.stringify(state.model.selectedAttendClassList))
            try {
              btnLoading.value = true;
              const fn = props.data?.ataId ? updateAttentionAddInfo : addAttentionAddInfo;
              state.model["userId"] = state.model.manageUser.userId;
              let constObjs = Object.assign({} , state.model);
              if(!state.model["userId"]){
                ElMessage.error('补录人员不能为空！');
                return;
              }
              if (state.model.ataType == '按时间补录（根据时间自动补录）') {
                constObjs["ataTimeStartDate"] = hourMinStr(constObjs["ataTimeStartDate"]);
                constObjs["ataTimeEndDate"] = hourMinStr(constObjs["ataTimeEndDate"]);
                constObjs["ataClassInfo"] = null;

                if(constObjs["ataTimeEndDate"] < constObjs["ataTimeStartDate"]){
                  ElMessage.error('补录结束日期需大于或等于开始日期！');
                  return;
                }
              } else {
                constObjs["ataTimeStartDate"] = null;
                constObjs["ataTimeEndDate"] = null;
                constObjs["ataClassInfo"] = JSON.stringify(constObjs.selectedAttendClassList);
              }
              constObjs["startDate"] = dateStr(constObjs["startDate"]);
              constObjs["endDate"] = dateStr(constObjs["endDate"]);
              if(constObjs["atoExam"] == '1') {
                constObjs["ataExamType"] = "自动审核";
              }else{
                constObjs["ataExamType"] = "领导审核";
              }
              constObjs["submitDate"] = constObjs["submitDate"] ? new Date(constObjs["submitDate"]) : null;
              constObjs["exmDate"] = constObjs["exmDate"] ? new Date(constObjs["exmDate"]) : null;
              const {message, code} = await fn(constObjs);
              if (code === 0) {
                ElMessage.success(message);
              } else {
                ElMessage.error(message);
              }
              context.emit("update:modelValue", true);
            } catch (e) {
              throw new Error(e.message);
            } finally {
              btnLoading.value = false;
            }
          }
        });
      }
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, data, ...attrs } = props;
      return attrs;
    });

    onMounted(() => {
      //编辑状态，选中表格
    });

    watch(
      () => props.modelValue,
      async (n) => {
        if (n) {
          if (props.data?.ataId) {
            const { code, message , data } = await getAttentionAddInfoDetail(props.data.ataId);
            if(code != 0){
              ElMessage.error(message);return;
            }
            data["manageUser"] = {userId:data.userId , userNum:data.userNum , userName:data.userName , atgId:data.atgId , atgName : data.atgName , orgNameList:data.orgNameList};
            //查询此用户的班次信息
            if(data.atgId) {
              const atcRt = await getDistinctAttentionClass({atgId: data.atgId});
              if (atcRt.code != 0) {
                ElMessage.error(atcRt.message);
              }
              data.manageUser["attendClassObjList"] = atcRt.data;
            }else{
              data.manageUser["attendClassObjList"] = [];
            }

            let objbase = getDefaultModel();
            if(data.ataType == '按时间补录（根据时间自动补录）') {
              data["ataTimeStartDate"] = new Date(2022, 11, 4, parseInt(data.ataTimeStartDate.toString().substr(0 , 2)), parseInt(data.ataTimeStartDate.toString().substr(3 , 2)));
              data["ataTimeEndDate"] = new Date(2022, 11, 4, parseInt(data.ataTimeEndDate.toString().substr(0 , 2)), parseInt(data.ataTimeEndDate.toString().substr(3 , 2)));
            }else{
              data["selectedAttendClassList"] = JSON.parse(data.ataClassInfo);
            }
            if(data["exmDate"]) {
              data["exmDate"] = timeStr(data["exmDate"]);
            }
            if(data["submitDate"]) {
              data["submitDate"] = timeStr(data["submitDate"]);
            }
            state.model = Object.assign(objbase, data);
          } else {
            state.model = getDefaultModel();
          }

          state.model.disType = props.type;
        }
      }
    );
    return {
      weekDays,
      attrs,
      // update,
      formRef,
      cancel,
      submit,
      free_tableRef,
      fixed_tableRef,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      disType,
      btnLoading,
      themes: THEMEVARS,
      // editFreeClass,
      // selectedClassAllClose,
      // selectedUsersClose,
      selectedManagerUserClose,
      handleSelectionFreeChange,
      handleSelectionFixedChange,
      constClassList,
      constExameList,
      checkStr,
    };
  },
};
</script>
