<template>
  <div class="edit">
    <el-button type="primary" @click="handleEdit({})" size="mini">添加</el-button>
  </div>
  <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading" border stripe>
    <el-table-column label="病史/手术史" align="center" prop="type" width="100px" show-overflow-tooltip></el-table-column>
    <el-table-column label="描述" prop="pastMedicalHistory" align="center" show-overflow-tooltip></el-table-column>
    <el-table-column label="操作" align="center" width="200">
      <template #default="scope">
        <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
        <el-button size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <illness-edit v-model="state.isEdit" :rowData="state.rowData" @success="getList" />
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElButton, ElMessageBox, ElMessage
} from "element-plus";
import { reactive, onMounted } from "vue";
import { healthMedicalHistory, healthMedicalHistoryDel } from "@/applications/eccard-health/api/archives.js";
import edit from "./edit.vue"
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-button": ElButton,
    "illness-edit": edit,
  },
  props: {
    rowData: {
      types: Object,
      default: null
    },
  },
  setup(props) {
    const state = reactive({
      loading: false,
      isEdit: false,
      dataList: [],
      rowData: {}
    });
    const getList = async () => {
      state.loading = true
      try {
        let { data, code } = await healthMedicalHistory({ recordId: props.rowData.recordId })
        if (code === 0) {
          state.dataList = data
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const handleEdit = row => {
      state.rowData = { recordId: props.rowData.recordId, ...row }
      state.isEdit = true
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await healthMedicalHistoryDel(row.id);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      getList,
      handleEdit,
      handleDel
    };
  },
};
</script>
<style lang="scss" scoped>
.edit {
  text-align: right;
  margin-bottom: 10px;
}
</style>
