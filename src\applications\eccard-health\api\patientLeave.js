import request from '@/service';


//病休列表分页查询
export function healthSickLeavePage(params) {
  return request.post('/eccard-basic-data/healthSickLeave/page', params,);
}
//病休新增
export function healthSickLeaveAdd(params) {
  return request.post('/eccard-basic-data/healthSickLeave', params,);
}
//列表修改
export function healthSickLeaveUpdate(params) {
  return request.put('/eccard-basic-data/healthSickLeave', params,);
}
//列表删除
export function healthSickLeaveDel(params) {
  return request.delete(`/eccard-basic-data/healthSickLeave/${params}`,);
}
//病休列表导出
export function healthSickLeaveExport(params) {
  return request.post('/eccard-basic-data/healthSickLeave/export',
    params,
    {
      headers: {
        'Content-Type': 'application/octet-stream',
        Accept: "*/*"
      },
      responseType: 'blob',
    }
  );
}


//新增复课申请
export function addHealthResumeApply(params) {
  return request.post('/eccard-basic-data/healthResumeApply/addHealthResumeApply', params,);
}
//复课申请审核
export function checkHealthResumeApply(params) {
  return request.post('/eccard-basic-data/healthResumeApply/checkHealthResumeApply', params,);
}

//传染病上报
export function healthSickLeaveReport(params) {
  return request.post(`/eccard-basic-data/healthSickLeave/report/${params}`,);
}



//病例追踪列表
export function healthyCaseTrackList(params) {
  return request.post('/eccard-basic-data/healthyCaseTrack/list', params,);
}
//新增病例追踪
export function healthyCaseTrackAdd(params) {
  return request.post('/eccard-basic-data/healthyCaseTrack', params,);
}
//修改病例追踪
export function healthyCaseTrackUpdate(params) {
  return request.put('/eccard-basic-data/healthyCaseTrack', params,);
}
//删除病例追踪
export function healthyCaseTrackDel(params) {
  return request.delete(`/eccard-basic-data/healthyCaseTrack/${params}`,);
}