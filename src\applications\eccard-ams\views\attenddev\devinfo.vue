<template>
  <p style="margin:10px auto 10px 10px;">
    <el-form inline size="mini">
      <el-form-item label="所属区域">
        <area-select-tree style="width: 100%" :value="state.form.areaName" valueKey="id" :multiple="false"
                          @valueChange="(val) => (state.form.areaName = val.areaPath)" />
      </el-form-item>
      <el-form-item label="设备机号">
        <el-input v-model="state.form.atdNum" placeholder="考勤设备搜索" :clearable="true"></el-input>
      </el-form-item>
      <el-form-item label="设备名称">
        <el-input v-model="state.form.atdName" placeholder="考勤设备搜索" :clearable="true"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
  </p>
  <div>
    <p style="margin:-5px auto 10px 2px;">
      <el-button size="mini" type="success" icon="el-icon-plus" @click="add('','add')">绑定</el-button>
      <!-- <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleClick(scope.row)">编辑</el-button>
      <el-button size="mini" type="danger" icon="el-icon-circle-close" @click="handleClick(scope.row)">删除</el-button> -->
    </p>
    <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
      <el-table-column label="所属区域" prop="areaName" align="center"></el-table-column>
      <el-table-column label="连接类型" prop="wstConType" align="center"></el-table-column>
      <el-table-column label="设备类型" prop="atdType" align="center">
        <template #default="scope">
          {{dictionaryFilter(scope.row.atdType)}}
        </template>
      </el-table-column>
      <el-table-column label="设备状态" prop="atdStatus" align="center">
        <template #default="scope">
          {{ scope.row.atdStatus ? dictionaryFilter(scope.row.atdStatus) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="设备机号" prop="atdNum" align="center"></el-table-column>
      <el-table-column label="设备名称" prop="atdName" align="center"></el-table-column>
      <el-table-column label="设备型号" prop="atdModel" align="center"></el-table-column>
      <el-table-column label="设备IP" prop="atdIp" align="center"></el-table-column>
      <el-table-column label="操作" prop="" align="center">
        <template #default="scope">
          <el-button @click="kaoqing(scope.row , 'edit')" type="text" size="mini">考勤记录</el-button>
          <el-button @click="unbind(scope.row , 'edit')" type="text" size="mini">取消绑定</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </div>
  <bind-edit :modelValue="state.isEdit" :title="'选择设备'" :type="state.type" :data="state.rowData"  @update:modelValue="close" @edit="state.type='edit'" />
  <dev-edit-tab :modelValue="state.isEdit1" :selectedIndex="'record'" :title="state.rowData ? state.rowData['atdName'] + '终端详情' : ''" :type="state.type" :data="state.rowData"  @update:modelValue="close" @edit="state.type='edit'" />

</template>
<script>
  import { reactive, watch , onMounted } from 'vue';
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElMessage,
    ElButton,
    ElMessageBox,
    ElPagination,
  } from 'element-plus';
  // import {timeStr} from "@/utils/date"
  import { useDict } from "@/hooks/useDict";
  import { getDevInfoPage , unBindDevInfo } from "@/applications/eccard-ams/api";
  import bindEdit from "./components/devbind.vue"
  import devEditTab from "./components/devedittab.vue"
  import areaSelectTree from "@/components/tree/areaSelectTree.vue";

  export default {
    props: {
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    components: {
      bindEdit,
      devEditTab,
      areaSelectTree,
      ElForm,
      ElFormItem,
      ElInput,
      'el-table': ElTable,
      'el-button': ElButton,
      'el-table-column': ElTableColumn,
      'el-pagination': ElPagination,
    },
    setup(props) {
      const statusList = useDict("DEV_INFO_STATUS");
      const state = reactive({
        loading: false,
        isEdit:false,
        isEdit1:false,
        form:{
          degId:'',
          areaId: '',
          atdNum:'',
          atdName : '',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        if(!props.data.degId){
          return;
        }
        state.loading=true
        let {data}=await getDevInfoPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
        state.isEdit=false;
        state.isEdit1=false;
      }
      const add=(row,type)=>{
        if(!props.data.degId){
          ElMessage.warning("请选择左边设备分组！");return;
        }
        state.type=type
        state.rowData = props.data;
        state.isEdit=true
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit1=true
      }
      const reset=()=>{
        state.form={
          degId:props.data.degId,
          currentPage:1,
          pageSize:10
        }
        getList();
      }
      const search=()=>{
        if(!props.data.degId){
          ElMessage.warning("请选择左边设备分组！");
        }
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const del=async (row)=>{
        console.log(row.id)
        // let {code,message}=await delSysMessage(row.id)
        // if(code===0){
        //   ElMessage.success(message)
        //   getList()
        // }
      }
      const close=(val)=>{
        state.isEdit=false;
        state.isEdit1=false;
        if(val){
          getList()
        }
      }
      const unbind = async (val)=>{
        //val["degId"] = null;
        val["atdDesc"] = '';
        ElMessageBox.confirm(`确定解绑此设备？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code,message}=await unBindDevInfo(val)
          if(code===0){
            ElMessage.success(message)
            getList()
          }
        });
      };
      const kaoqing=(val , str)=>{
        state.type=str
        state.rowData=val
        state.isEdit1=true
      };

      onMounted(()=>{
        state.form.degId = props.data.degId;
        getList();
      });

      watch(
        () => props.data,
        (n) => {
          if (n) {
            state.form.degId = props.data.degId;
            getList();
          }
        }
      );

      return {
        state,
        statusList,
        add,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        del,
        close,
        unbind,
        kaoqing,
      };
    },
  };
</script>
<style lang="scss" scoped>
  .maindiv{
    width:100%;
    height:100vh;
  }
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
