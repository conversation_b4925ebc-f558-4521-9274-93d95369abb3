<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="交易来源列表">
      <template #extra>
        <el-button icon="el-icon-plus" size="small" type="success" @click="edit('add')">新增</el-button>
      </template>
      <el-table border :data="state.data" height="70vh">
        <el-table-column prop="tradeName" label="名称" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="tradeCode" label="属性值" align="center"></el-table-column>
        <el-table-column prop="sysPreset" label="是否系统预置" align="center">
          <template #default="scope">
            {{ filterDictionary(scope.row.sysPreset, bool) }}
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="添加人" align="center"></el-table-column>
        <el-table-column prop="useStatus" label="是否启用" align="center">
          <template #default="scope">
            <el-switch :model-value="scope.row.useStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE"
              @change="statusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="lastModifyTime" label="更新时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" @click="edit('edit', scope.row)" :disabled="scope.row.sysPreset=='TRUE'">
              编辑</el-button>
            <el-button type="text" size="mini" @click="edit('detail', scope.row)">查看详情</el-button>
            <el-button type="text" size="mini" @click="del(scope.row)" :disabled="scope.row.sysPreset=='TRUE'">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[10, 20, 30, 50, 100, 500]" :page-size="10"
          layout="total, sizes, prev, pager, next, jumper" :total="state.total">
        </el-pagination>
      </div>
      <kade-trade-source :type="state.type" :rowData="state.rowData" :dialogVisible="state.dialogVisible" @close="close"
        @editType="() => (state.type = 'edit')"></kade-trade-source>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import {
  getTradeSourceList,
  deleteTradeSource,
  updateTradeSource
} from "@/applications/eccard-finance/api";
import { reactive, onMounted } from "vue";
import { useDict } from "@/hooks/useDict";
import { filterDictionary } from "@/utils";
import {
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
  ElMessage,
  ElSwitch
} from "element-plus";
import edit from "./components/edit.vue";
export default {
  components: {
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElSwitch,
    "kade-trade-source": edit,
  },
  setup() {
    const bool = useDict("SYS_BOOL_STRING");
    const state = reactive({
      dialogVisible: false,
      data: [],
      total: 0,
      type: "",
      rowData: {},
      form: {
        currenPage: 1,
        pageSize: 10,
      },
    });
    const getList = () => {
      getTradeSourceList(state.form).then((res) => {
        console.log(res);
        state.data = res.data.list;
        state.total = res.data.total;
      });
    };
    const handleSizeChange = (val) => {
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.currenPage = val;
      getList();
    };
    const edit = (type, row) => {
      console.log(type, row);
      state.type = type;
      state.rowData = row;
      state.dialogVisible = true;
    };
    const statusChange = async (row) => {
      if (!row.id) return
      let params = { ...row }
      if (params.useStatus === 'ENABLE_TRUE') {
        params.useStatus = "ENABLE_FALSE"
      } else {
        params.useStatus = "ENABLE_TRUE"
      }
      let { message, code } = await updateTradeSource(params)
      if (code === 0) {
        ElMessage.success(message)
        getList();
      }
    }
    const del = (row) => {
      console.log(row);
      ElMessageBox.confirm("确定删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let { code } = await deleteTradeSource(row.id);
          if (code === 0) {
            ElMessage({
              type: "success",
              message: "删除成功",
            });
            getList();
          }
        })
        .catch(() => {
          ElMessage({
            type: "info",
            message: "已取消",
          });
        });
    };

    const close = (val) => {
      if (val) {
        getList();
      }
      state.dialogVisible = false;
    };
    onMounted(() => {
      getList();
    });
    return {
      state,
      edit,
      del,
      statusChange,
      close,
      getList,
      handleSizeChange,
      handleCurrentChange,
      filterDictionary,
      bool,
    };
  },
};
</script>

<style lang="scss" scoped>
.trade-source {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;

  .table-box {
    width: 100%;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    padding: 0;

    .title-box {
      margin: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-icon-s-fold {
        margin-right: 10px;
      }
    }

    .el-table {
      margin-top: 20px;
    }

    .pagination {
      margin: 20px 15px;
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 0;
  }
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}
</style>