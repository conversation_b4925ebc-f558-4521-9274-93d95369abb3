<template>
  <el-dialog :model-value="dialogVisible" :title="type + '人员名单'" width="900px" :before-close="handleClose">
    <el-table border :data="state.data" v-loading="state.loading">
      <el-table-column prop="userCode" label="人员编号" align="center"></el-table-column>
      <el-table-column prop="userName" label="人员姓名" align="center"></el-table-column>
      <el-table-column prop="userSex" label="性别" align="center">
        <template #default="scope">
          {{ dictionaryFilter(scope.row.userSex) }}
        </template>
      </el-table-column>
      <el-table-column prop="deptName" label="组织机构" align="center"></el-table-column>
      <el-table-column prop="attendanceDate" label="考勤日期" align="center"></el-table-column>
      <el-table-column prop="attendancePeriodName" label="考勤时段" align="center"></el-table-column>
      <el-table-column prop="attendanceResult" label="考勤结果" align="center">
        <template #default="scope">
          {{ dictionaryFilter(scope.row.attendanceResult) }}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import { ElDialog, ElTable, ElTableColumn, } from "element-plus"
import { reactive } from '@vue/reactivity'
import { watch } from 'vue';
// import { dateStr } from "../../../../../../utils/date";
import { notReturnOrLateList } from '@/applications/eccard-dorm/api.js'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    tableRow: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog,
    ElTable,
    ElTableColumn,
  },
  setup(props, context) {
    const state = reactive({
      data: [],
      loading: false
    });
    const handleClose = () => {
      context.emit("close", false)
    }
    const getList = async () => {
      state.loading = true
      let { attendanceDate, attendancePeriodId, deptId } = { ...props.tableRow }
      let params = { attendanceDate, attendancePeriodId, deptId, }
      if (props.type === "未归") {
        params.attendanceResult = "NOT_RETURN"
      } else if (props.type === "晚归") {
        params.attendanceResult = "LATE_RETURN"
      } else if (props.type === "未离") {
        params.attendanceResult = "NOT_LEAVE"
      } else if (props.type === "晚离") {
        params.attendanceResult = "LATE_LEAVE"
      }
      try {
        let { data } = await notReturnOrLateList(params)
        state.data = data
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    watch(() => props.dialogVisible, val => {
      if (val) {
        getList()
      }
    })
    return {
      state,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table {
  margin-top: 15px;
}
</style>