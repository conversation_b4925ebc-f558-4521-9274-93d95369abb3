<template>
  <el-dialog :model-value="dialogVisible" title="考勤记录详情" width="700px" :before-close="handleClose">
    <el-form inline label-width="100px" size="mini">
      <el-form-item :label="item.label" v-for="(item, index) in state.tableList" :key="index">
        <el-input v-if="item.isDict" readonly :model-value="dictionaryFilter(selectRow[item.filed])"></el-input>
        
        <el-input v-else readonly :model-value="selectRow[item.filed]"></el-input>
      </el-form-item>
      <el-form-item label="考勤照片：">
        <el-image style="width: 100px; height: 100px" :src="selectRow.attendanceImg"
          :preview-src-list="[selectRow.attendanceImg]" :initial-index="0" fit="cover"></el-image>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { ElDialog, ElForm, ElFormItem, ElInput, ElImage } from "element-plus"
import { reactive } from '@vue/reactivity'
import Logo from '@/assets/logo.png';
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      types: String,
      default: {}
    }
  },
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElImage
  },
  setup(props, context) {
    const state = reactive({
      tableList: [
        { label: "人员编号：", filed: "userCode" },
        { label: "人员姓名：", filed: "userName" },
        { label: "考勤日期：", filed: "attendanceDate" },
        { label: "考勤时段：", filed: "attendancePeriodName" },
        { label: "考勤结果：", filed: "attendanceResult", isDict: true },
        // { label: "记录来源：", filed: "" },
        { label: "组织机构：", filed: "deptName" },
        { label: "区域：", filed: "areaName" },
        { label: "楼栋：", filed: "buildName" },
        { label: "单元：", filed: "unitNum" },
        { label: "楼层：", filed: "floorNum" },
        { label: "房间：", filed: "roomName" },
        { label: "签到时间：", filed: "attendanceTime" },
        { label: "创建时间：", filed: "createTime" }
      ]
    });
    const handleClose = () => {
      context.emit("close", false)
    }
    return {
      Logo,
      state,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>