/* 个人账户数据 */
import { requestDate } from "@/utils/reqDefaultDate.js"
import {
    getPersonWalletInfo,
    getPersonTradeList,
    getPersonCardInfo,
    getPersonCardLog,
    getPersonWalletTradeSum,
    getPersonFamilyList
} from "@/applications/eccard-finance/api";
const state = {
    walletManagerLoading: false,
    allFrame: false,
    isActiveOne: false, //判断第一个tabs是否请求
    personWalletInfo: [],
    selectPerson: "",
    title: "",
    transactionTabIndex: "0",
    personTradeList: [],
    personTradeTotal: 0,
    amountTotal: '',
    activeItem: 0,
    personCardInfo: {},
    cardLogList: [],
    cardLogTotal: 0,
    personWalletTradeSum: [],
    personFamilyList: [],
    watchExportData: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
    },
};
const mutations = {
    updateState(state, { key, payload }) {
        state[key] = payload;
    },
    clearUserData(state) {
        state.personWalletInfo = []
        state.personTradeList = []
        state.personTradeTotal = 0
        state.amountTotal = ""

        state.personCardInfo = {}
        state.cardLogList = []
        state.cardLogTotal = 0

        state.personWalletTradeSum = []
        state.personFamilyList = []


    }
};
const actions = {
    queryPersonWalletInfo({ commit }, data) {
        console.log(state.selectPerson);
        if (state.selectPerson) {
            if (!(state.selectPerson.acctStatus == "NO_ACCOUNT")) {
                commit("updateState", { key: 'walletManagerLoading', payload: true })
                getPersonWalletInfo(data).then(res => {
                    commit("updateState", { key: 'personWalletInfo', payload: res.data })
                    commit("updateState", { key: 'walletManagerLoading', payload: false })
                }).catch(() => {
                    commit("updateState", { key: 'personWalletInfo', payload: [] })
                    commit("updateState", { key: 'walletManagerLoading', payload: false })
                })
            } else {
                commit("updateState", { key: 'personWalletInfo', payload: [] })
            }
        }

    },
    queryPersonTradeList({ commit }) {
        console.log(state.selectPerson);
        if (state.selectPerson) {
            state.watchExportData.userId = state.selectPerson.userId
            state.watchExportData.amountType = state.transactionTabIndex == "1" ? 2 : 1;
            getPersonTradeList(state.watchExportData).then(res => {
                commit("updateState", { key: 'personTradeList', payload: res.data.list })
                commit("updateState", { key: 'personTradeTotal', payload: res.data.total })
                commit("updateState", { key: 'amountTotal', payload: res.totalAmount })
            })
        }

    },
    queryPersonCardInfo({ commit }, { data1, data2 }) {
        
        if (state.selectPerson) {
            if (!(state.selectPerson.cardStatus == "NO_CARD")) {
                getPersonCardInfo(data1).then(res => {
                    commit("updateState", { key: 'personCardInfo', payload: res.data })
                    getPersonCardLog({ ...data2, userId: res.data.userId, physicalcardId: res.data.physicalcardId }).then(res => {
                        commit("updateState", { key: 'cardLogList', payload: res.data.list })
                        commit("updateState", { key: 'cardLogTotal', payload: res.data.total })
                    }).catch(() => {
                        commit("updateState", { key: 'cardLogList', payload: [] })
                        commit("updateState", { key: 'cardLogTotal', payload: 0 })
                    })
                }).catch(() => {
                    commit("updateState", { key: 'personCardInfo', payload: {} })
                    commit("updateState", { key: 'cardLogList', payload: [] })
                    commit("updateState", { key: 'cardLogTotal', payload: 0 })
                })
            } else {
                commit("updateState", { key: 'personCardInfo', payload: {} })
                commit("updateState", { key: 'cardLogList', payload: [] })
                commit("updateState", { key: 'cardLogTotal', payload: 0 })
            }
        }
    },
    queryPersonWalletTradeSum({ commit }, data) {
        if (state.selectPerson) {
            getPersonWalletTradeSum(data).then(res => {
                commit("updateState", { key: 'personWalletTradeSum', payload: res.data })
            }).catch(() => {
                commit("updateState", { key: 'personWalletTradeSum', payload: [] })
            })
        }

    },
    queryPersonFamilyList({ commit }, data) {
        if (state.selectPerson) {
            getPersonFamilyList(data).then(res => {
                commit("updateState", { key: 'personFamilyList', payload: res.data })
            }).catch(() => {
                commit("updateState", { key: 'personFamilyList', payload: [] })
            })
        }

    },

};
export default {
    namespaced: true,
    state,
    mutations,
    actions
}