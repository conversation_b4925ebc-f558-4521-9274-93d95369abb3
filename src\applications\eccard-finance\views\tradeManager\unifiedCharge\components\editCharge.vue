<template>
  <div class="cash-add">
    <el-dialog :model-value="isEditData.isShow" :title="(isEditData.isEdit ? '编辑' : '新建') + '统一收费'" width="90%"
      :before-close="beforeClose" :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form ref="form" :model="state.form" :rules="state.rules" inline size="small" label-width="120px">
            <el-form-item label="收费类型:" prop="unifiedChangeType">
              <el-select v-model="state.form.unifiedChangeType" placeholder="请选择">
                <el-option v-for="(item, index) in ChargeTypeList" :key="index" :label="item.uctName"
                  :value="item.uctId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="收费时间段:" prop="requestDate">
              <el-date-picker v-model="state.form.requestDate" type="daterange" range-separator="~"
                start-placeholder="请选择日期" end-placeholder="请选择日期" unlink-panels @change="changeDate">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="120px">
            <el-form-item label="收费说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.unifiedChangeRemark"></el-input>
            </el-form-item>
            <el-form-item label="收费方式:">
              <el-radio-group v-model="state.form.unifiedChangeMode">
                <el-radio label="GENERATE">生成清单</el-radio>
                <el-radio label="IMPORT">导入清单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <el-form v-show="state.form.unifiedChangeMode == 'GENERATE'" inline :model="state.grantForm" ref="grantForm"
            :rules="state.grantRules" size="small" label-width="120px">
            <el-form-item label=" 身份类别:" prop="userRole">
              <el-select clearable v-model="state.grantForm.userRole" placeholder="请选择">
                <el-option v-for="(item, index) in roleList" :key="index" :label="item.roleName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目收费金额:" prop="uniAmount">
              <el-input placeholder="请输入" v-model="state.grantForm.uniAmount"></el-input>
            </el-form-item>
          </el-form>
          <div class="select-input-lang">
            <el-form v-show="state.form.unifiedChangeMode == 'GENERATE'" inline size="small" label-width="120px">
              <el-form-item>
                <template #label>
                  <el-dropdown trigger="click" @command="dropdownChange">
                    <span class="el-dropdown-link">
                      {{ state.dropdownType }}:<el-icon class="el-icon--right"></el-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="组织机构">组织机构</el-dropdown-item>
                        <el-dropdown-item command="卡片类型">卡片类型</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
                <kade-dept-select-tree v-if="state.dropdownType === '组织机构'" style="width: 100%"
                  :value="state.grantForm.deptPaths" valueKey="deptPath" :multiple="true"
                  @valueChange="(val) => (state.grantForm.deptPaths = val)" />
                <el-select v-if="state.dropdownType === '卡片类型'" v-model="state.grantForm.cardTypes" multiple
                  collapse-tags placeholder="请选择">
                  <el-option v-for="item in cardTypeList" :key="item.id" :label="item.ctName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-button size="small" type="primary" @click="defineGenerateListClick()">确认生成清单</el-button>
              <el-button size="small" @click="state.grantForm = {}">重置</el-button>
            </el-form>
          </div>
          <el-form v-show="state.form.unifiedChangeMode == 'IMPORT'" inline size="small" label-width="120px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl"
                :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-success="handleSuccess"
                :on-remove="handleRemove" :on-exceed="handleExceed" :limit="1" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="green" style="margin-left: 20px" @click="download()">下载样例</div>
            </el-form-item>
          </el-form>
          <div style="margin-bottom: 10px" v-if="state.listData.errorCount > 0">
            <span class="red"> • </span>异常数据
            <span class="red"> {{ state.listData.errorCount }}</span>
            条，请修改确认
          </div>
          <el-table style="width: 100%" :data="state.personList" v-loading="false" border stripe>
            <el-table-column label="校检结果" prop="validInfo" align="center">
              <template #default="scope">
                <div :style="scope.row.error && 'color:#f00'">{{ scope.row.validInfo }}</div>
              </template>
            </el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="收费金额（元）" prop="uniAmount" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <!-- <span @click="delClick(scope.row)" style="margin-right: 10px"
                  >编辑</span
                > -->
                <span @click="delClick(scope.row)" class="green">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="cash-add-pagination">
            <div>
              <span>合计：<span class="blue">{{ state.listData.totalCount }}</span>人，<span class="blue">{{
                  state.listData.totalAmount
              }}</span>元</span>
            </div>
            <el-pagination background :current-page="state.grantParam.currentPage"
              :page-size="state.grantParam.pageSize" layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[6, 10, 20, 50, 100]" :total="state.listData.totalCount" @current-change="handlePageChange"
              @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitClick()" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { onMounted, reactive, ref, watch, nextTick, computed } from "vue";
import { useStore } from "vuex";
import { getToken, downloadXlsx } from "@/utils";
import { useDict } from "@/hooks/useDict";
import { timeStr } from "@/utils/date.js";
import { generateUni, getUniListByPage, getWaitUniListByPage, deleteWaitUni, exportUniList, addUnifiedCharge, editUnifiedCharge } from "@/applications/eccard-finance/api";
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElRadio,
  ElRadioGroup,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElMessage,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";

export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElRadio,
    ElRadioGroup,
    ElUpload,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElDatePicker,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const store = useStore();
    const uploadDom = ref(null);
    const form = ref(null);
    const grantForm = ref(null);
    const rechargeType = useDict("SYS_RECHARGE_MODE");
    const state = reactive({
      IsEditFlag: store.state.chargeData.isEditData.isEdit ? true : false,
      form: {
        requestDate: [],
      },
      grantForm: {},
      dropdownType: "组织机构",
      rules: {
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        unifiedChangeType: [
          {
            required: true,
            message: "请选择收费类型",
            trigger: "change",
          },
        ],
        requestDate: [
          {
            type: 'array',
            required: true,
            message: "请选择收费时间段",
            trigger: "blur",
          },
        ],
      },
      grantRules: {
        uniAmount: [
          {
            required: true,
            message: "请输入每人收费金额",
            trigger: "blur",
          },
        ],
        userRole: [
          {
            required: true,
            message: "请选择身份类别",
            trigger: "change",
          },
        ],
      },
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/uniRecords/importUniList`,
      grantParam: {
        currentPage: 1,
        pageSize: 6,
      },
      personList: [],
      listData: {
        totalCount: 0,
        totalAmount: 0,
      },
    });

    const isEditData = computed(() => {
      return store.state.chargeData.isEditData
    })

    const ChargeTypeList = computed(() => {
      return store.state.chargeData.ChargeTypeList
    })

    const roleList = computed(() => {
      return store.state.chargeData.roleList
    })
    const cardTypeList = computed(() => {
      return store.state.chargeData.cardTypeList
    })
    //监听清单生成类型切换
    watch(
      () => state.form.grantMode,
      (val) => {
        if (val === "GENERATE") {
          nextTick(() => {
            grantForm.value.clearValidate();
          });
        }
      }
    );
    watch(() => store.state.chargeData.isEditData.isShow, val => {
      if (val) {
        nextTick(() => {
          form.value.clearValidate();
        })
      }
    })

    watch(() => store.state.chargeData.isEditData.isEdit, val => {
      if (val) {
        let { unifiedChangeType, unifiedChangeMode, projectName, beginTime, endTime, auditStatus, id, unifiedChangeRemark } = store.state.chargeData.selectRow
        state.form = {
          unifiedChangeType, unifiedChangeMode, projectName, requestDate: [beginTime, endTime], beginTime: timeStr(beginTime), endTime: timeStr(endTime), auditStatus, unifiedChangeRemark, id
        }
        state.IsEditFlag = true
        state.grantParam.projectId = id
        getList()

      }
    })

    watch(
      () => state.dropdownType,
      (val) => {
        if (val === "组织机构") {
          delete state.grantForm.cardTypes;
        } else if (val === "卡片类型") {
          delete state.grantForm.deptPaths;
        }
      }
    );
    const changeDate = (val) => {
      if (val && val.length) {
        state.form.beginTime = timeStr(val[0])
        state.form.endTime = timeStr(val[1])
      } else {
        delete state.form.beginTime
        delete state.form.endTime
      }

    }
    const dropdownChange = (val) => {
      state.dropdownType = val;
    };
    //获取未入库人员列表
    const getNotGrantList = async () => {
      let { data: { generateUniRes, pageInfo } } = await getWaitUniListByPage(state.grantParam)
      state.personList = pageInfo
      state.listData = generateUniRes
      state.form.projectNo = generateUniRes.projectNo
    }
    //获取已入库人员列表
    const getGrantList = async () => {
      let { data: { generateUni, pageInfo, dept, role } } = await getUniListByPage(state.grantParam)
      state.personList = pageInfo.list
      state.listData = generateUni
      state.grantForm.userRole = role
      state.grantForm.deptPaths = dept
    }
    const getList = () => {
      state.IsEditFlag ? getGrantList() : getNotGrantList()
    }
    //点击生成清单
    const defineGenerateListClick = () => {
      grantForm.value.validate(async (valid) => {
        if (valid) {
          let { data } = await generateUni(state.grantForm)
          state.IsEditFlag = false
          state.grantParam.projectNo = data.projectNo
          state.form.projectNo = data.projectNo
          getList()
        } else {
          return false
        }
      })
    }
    const delClick = async (row) => {
      let params = {
        projectNo: state.grantParam.projectNo,
        id: row.id
      }
      let { code, message } = await deleteWaitUni(params)
      if (code === 0) {
        ElMessage.success(message)
        getList()
      }
    }
    const download = async () => {
      let res = await exportUniList()
      downloadXlsx(res, "统一收费导入清单样例.xlsx")
    }
    //上传文件个数超出限制提示
    const handleExceed = (files, fileList) => {
      if (fileList.length === 1) {
        ElMessage.error("最多上传一个文件！");
      }
    };
    const handleSuccess = ({ data }) => {
      state.IsEditFlag = false
      state.grantParam.projectNo = data.projectNo
      getList()
    }
    const handlePageChange = (val) => {
      state.grantParam.currentPage = val;
      getList()
    };
    const handleSizeChange = (val) => {
      state.grantParam.currentPage = 1;
      state.grantParam.pageSize = val;
      getList()
    };

    const submitClick = () => {
      if (!state.form.projectNo) {
        return ElMessage.error("请先生成清单！");
      }
      form.value.validate(async valid => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(state.form))
          delete params.requestDate
          let fn = store.state.chargeData.isEditData.isEdit ? editUnifiedCharge : addUnifiedCharge
          let { code, message } = await fn(params)
          if (code === 0) {
            ElMessage.success(message)
            beforeClose()
            store.commit("chargeData/updateState", {
              key: "isReq",
              payload: true,
            });
          }
        } else {
          return false
        }
      })
    }
    const beforeClose = () => {
      store.commit("chargeData/updateState", {
        key: "isEditData",
        payload: {
          isShow: false,
          isEdit: false
        },
      });
      state.IsEditFlag = false
      state.form = {
        requestDate: [],
      }
      state.grantForm = {}
      state.grantParam = {
        currentPage: 1,
        pageSize: 6,
      }
      state.personList = []
      state.listData = {
        totalCount: 0,
        totalAmount: 0,
      }
    }

    onMounted(() => { });
    return {
      rechargeType,
      uploadDom,
      form,
      grantForm,
      state,
      isEditData,
      ChargeTypeList,
      roleList,
      cardTypeList,
      changeDate,
      dropdownChange,
      download,
      defineGenerateListClick,
      delClick,
      handleExceed,
      handleSuccess,
      handlePageChange,
      handleSizeChange,
      submitClick,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.cash-add {
  .cash-add-pagination {
    padding-left: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.select-input-lang) {
  .el-select {
    width: 500px;
  }
}

:deep(.el-popper.is-light) {
  width: 500px !important;
}
</style>
