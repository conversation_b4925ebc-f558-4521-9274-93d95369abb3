<template>
  <!-- 商户结算 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="汇总类型:">
          <el-select v-model="state.form.summaryType" placeholder="请选择">
            <el-option v-for="(item, index) in state.typeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="汇总时间:" v-if="state.form.summaryType==1">
          <el-date-picker v-model="state.dayList" type="daterange" unlink-panels :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="汇总时间:" v-if="state.form.summaryType==2">
          <el-date-picker v-model="state.week" format="YYYY 第 ww 周" value-format="W" type="week" placeholder="选择周">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="汇总时间:" v-if="state.form.summaryType==3">
          <el-date-picker v-model="state.monthList" type="monthrange" :default-time="defaultTime" range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份" unlink-panels />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="转账分类汇总" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive, onMounted, } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElMessage
} from "element-plus";
import { downloadXlsx, } from "@/utils"
import { timeStr, dateStr, getWeekRange, getMonthFirst, getMonthLast, hasDateTimeRange } from "@/utils/date.js";
import { defaultTime, yesterdayTime } from "@/utils/reqDefaultDate";
import {
  queryTransferSummaryPage,
  queryTransferSummaryExport,
} from "@/applications/eccard-finance/api";
const column = [
  { label: "汇总类型", prop: "summaryType", width: "" },
  { label: "汇总时间", prop: "summaryDate", width: "170", render: (val) => val && dateStr(val) },
  { label: "转账类型", prop: "transferTypeName", width: "" },
  { label: "转账笔数", prop: "transferCount", width: "" },
  { label: "转账金额", prop: "transferAmount", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {
        pageNum: 1,
        pageSize: 10,
        summaryType: 1
      },
      dataList: [],
      total: 0,
      dayList: yesterdayTime(),
      week: "",
      monthList: [getMonthFirst(new Date()), getMonthLast(new Date())],
      tradeModeList: [],
      typeList: [
        { label: "按日", value: 1 },
        { label: "按周", value: 2 },
        { label: "按月", value: 3 },
      ],
    });
    const getList = async () => {
      let params = getParams()
      if (!params) return
      state.loading = true;
      try {
        let { code, data: { page: { list, total }, total: amount } } = await queryTransferSummaryPage(params);
        if (code === 0) {
          state.dataList = list;
          state.total = total;
          if (total) {
            state.dataList.push({
              ...amount,
              summaryType: "合计"
            })
          }
        }
        state.loading = false;
      }
      catch {
        state.loading = false;

      }
    };
    const exportClick = async () => {
      let params = getParams()
      if (!params) return
      state.loading = true
      try {
        let res = await queryTransferSummaryExport(params);
        downloadXlsx(res, "转账分类汇总.xlsx")
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const getParams = () => {
      let params = { ...state.form }
      if (params.summaryType == 1 && state.dayList && state.dayList.length) {
        params.startTime = timeStr(state.dayList[0])
        params.endTime = timeStr(state.dayList[1])
      } else if (params.summaryType == 2 && state.week) {
        params.startTime = getWeekRange(state.week)[0]
        params.endTime = getWeekRange(state.week)[1]
      } else if (params.summaryType == 3 && state.monthList && state.monthList.length) {
        params.startTime = timeStr(state.monthList[0])
        params.endTime = getMonthLast(state.monthList[1])
      } else {
        ElMessage.error('请选择汇总时间')
        return false
      }
      if (hasDateTimeRange([params.startTime, params.endTime], 6)) {
        ElMessage.error("查询日期范围不能操作6个月")
        return false
      }

      return params
    }
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
        summaryType: 1
      };
      state.dayList = yesterdayTime()
    };
    onMounted(() => {
      // getList();
    });
    return {
      column,
      state,
      timeStr,
      defaultTime,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0 0 10px 0;
}

.cashRecharge {
  :deep(.search-box) {
    .el-input__inner {
      width: 193px;
    }

    .el-date-editor {
      width: 400px;
    }
  }
}
</style>