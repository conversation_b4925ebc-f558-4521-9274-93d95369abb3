<template>
  <el-dialog :model-value="modelValue" :title="title" width="700px" :before-close="handleClose">
    <el-form v-loading="state.loading" :model="state.form" inline label-width="100px" size="mini" ref="formRef" :rules=" type!=='details'&&rules">
      <el-form-item v-if="type=='details'" label="所属区域：">
        <el-input :model-value="state.form.areaName" readonly />
      </el-form-item>
      <el-form-item v-if="type=='details'" label="楼栋：">
        <el-input :model-value="state.form.buildName" readonly />
      </el-form-item>
      <el-form-item v-if="type=='details'" label="单元：">
        <el-input :model-value="state.form.unitName" readonly />
      </el-form-item>
      <el-form-item v-if="type=='details'" label="楼层：">
        <el-input :model-value="state.form.floorName" readonly />
      </el-form-item>
      <el-form-item v-if="type=='details'" label="房间：">
        <el-input :model-value="state.form.roomName" readonly />
      </el-form-item>
      <kade-linkage-select v-if="type!=='details'" :isEdit="modelValue?true:false" :data="linkageData" :value="state.form"  @change="linkageChange" />
      <el-form-item label="报修人:" prop="repairPersonId">
        <el-input v-if="type=='details'" :model-value="state.form.repairPersonName" readonly />
        <el-select v-else v-model="state.form.repairPersonId" placeholder="请选择人员">
          <el-option v-for="(item,index) in state.personList" :key="index" :label="item.userName" :value="item.userId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报修人电话:" prop="repairPersonTel">
        <el-input v-if="type=='details'" :model-value="state.form.repairPersonTel" readonly />
        <el-input v-else v-model="state.form.repairPersonTel" placeholder="请输入检查人"></el-input>
      </el-form-item>
      <el-form-item label="报修项目:" prop="repairProjectId">
        <el-input v-if="type=='details'" :model-value="state.form.repairProjectName" readonly />
        <el-select v-else v-model="state.form.repairProjectId" placeholder="请选择报修项目">
          <el-option v-for="(item,index) in state.repairList" :key="index" :label="item.repairProjectName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="情况描述:">
        <el-input v-if="type=='details'" :model-value="state.form.remark" type="textarea" :rows="4" readonly />
        <el-input v-else v-model="state.form.remark" type="textarea" :rows="4" maxlength="200" placeholder="请输入问题描述"></el-input>
      </el-form-item>
      <div class="url">
        <el-form-item label="附件：">
          <el-image v-if="type=='details'" style="width: 100px; height: 100px" :src="state.form.resourceUrl" :preview-src-list="[state.form.resourceUrl]" :initial-index="0" fit="cover"></el-image>
          <kade-single-image-upload v-else v-model="state.form.resourceUrl" :action="uploadApplyLogo" icon="el-icon-plus"   storagePath="dorm/repairImages"  />
        </el-form-item>
      </div>
      <el-form-item label="维修状态:" v-if="type=='details'">
        <el-input :model-value="dictionaryFilter(state.form.repairStatus)" readonly></el-input>
      </el-form-item>
      <el-form-item label="维修时间:" v-if="type=='details'">
        <el-input :model-value="state.form.lastModifyTime" readonly />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="type!=='details'">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" v-if="type!='handle'" @click="submit" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElImage,  ElMessage } from "element-plus"
import SingleImageUpload from "@/components/singleImageUpload";
import { reactive, computed, ref, watch, nextTick,onMounted } from 'vue'
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { addRepair, editRepair,getRepairItemList,getRoomStayInfo } from "@/applications/eccard-dorm/api.js"
import { uploadApplyLogo } from "@/applications/unified_portal/api";

const linkageData = {
  area: { label: '所属区域:', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋:', valueKey: 'buildId' },
  unit: { label: '单元:', valueKey: 'unitNum' },
  floor: { label: '楼层:', valueKey: 'floorNum' },
  room: { label: '房间:', valueKey: 'roomId' }
}
const rules = {
  roomId: [{ required: true, message: "请选择房间", trigger: "change" }],
  repairPersonId: [{ required: true, message: "请选择报修人", trigger: "change" }],
  repairPersonTel: [{ required: true, message: "请输入报修人电话", trigger: "blur" },{max:20,message:'手机号不能超过20字符'}],
  repairProjectId: [{ required: true, message: "请选择报修项目", trigger: "change" }]
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    rowData: {
      type: String,
      default: ""
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElImage,
    "kade-linkage-select": linkageSelect,
    "kade-single-image-upload": SingleImageUpload,
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      form: {
        resourceUrl: '',
      },
      personList:[],
      repairList:[],
      loading: false
    })
    //获取保修项目
    const queryRepair=()=>{
      getRepairItemList().then((res)=>{
        console.log(res)
        state.repairList=res.data
      })
    }
    //获取房间人员
    const queryPerson=(roomId)=>{
      getRoomStayInfo({roomId}).then((res)=>{
        console.log(res)
        

        state.personList=res.data
      })
    }
    const title = computed(() => {
      if (props.type == 'add') {
        return '新建报修单'
      } else if (props.type == 'edit') {
        return '编辑报修单'
      } else {
        return '报修单详情'
      }
    })
    const linkageChange=(val)=>{
      state.form={...state.form,...val}
      delete state.form.userId
      state.personList=[]
      if(val.roomId){
        queryPerson(val.roomId)
      }
    }
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.type == 'add') {
          state.form = {
            resourceUrl: ''
          }
        } else {
          let { id,repairProjectCode,goodsOwnerName,lastModifyTime,repairPersonId,repairProjectName,areaId,unitName,areaName,buildId,buildName,unitNum,floorNum,floorName,roomId,roomName,repairPersonName,repairPersonTel,repairProjectId,remark,resourceUrl,repairStatus } = JSON.parse(JSON.stringify(props.rowData))
          state.form={ id,repairProjectCode,goodsOwnerName,lastModifyTime,repairPersonId,repairProjectName,unitName,areaId,areaName,buildId,buildName,unitNum,floorNum,floorName,roomId,roomName,repairPersonName,repairPersonTel,repairProjectId,remark,resourceUrl,repairStatus }
          queryPerson(roomId)
          console.log(state.form)
        }
        console.log(state.form)
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const handleClose = () => {
      context.emit('update:modelValue', false)
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let param = { ...state.form }
          delete param.lastModifyTime
          console.log(param,state.form)
          let fn = props.type == 'add' ? addRepair : editRepair
          state.loading = true
          try {
            let { code, message } = await fn(param)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch{
            state.loading = false
          }
          
        }
      })
    }
    onMounted(()=>{
      queryRepair()
    })
    return {
      state,
      title,
      rules,
      submit,
      formRef,
      linkageData,
      handleClose,
      uploadApplyLogo,
      linkageChange
    }
  }
}
</script>

<style lang="scss" scoped>
.url {
  width: 507px;
}
:deep(.el-textarea__inner) {
  width: 506px;
}
</style>