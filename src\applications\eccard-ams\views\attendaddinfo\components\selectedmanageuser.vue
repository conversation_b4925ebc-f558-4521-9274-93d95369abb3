<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true">
    <p style="margin:10px auto 0px 10px;">
      <el-form inline size="mini">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="用户编号或名称搜索"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </p>
      <el-table style="width: 100%;overflow:auto" :data="state.dataList" ref="multipleTable" v-loading="state.loading" highlight-current-row
                height="55vh" @current-change="handleSelectionChange" border stripe>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    <template #footer>
      <p style="text-align: center">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>
</template>
<script>
  import { reactive , onMounted  , watch} from "vue";
  // import { useDict } from "@/hooks/useDict";
  import { getAttentionUserAttendPage } from "@/applications/eccard-ams/api";
  // import deptSelectTree from "@/components/tree/deptSelectTree.vue";
  // import userEdit from "@/applications/eccard-ams/views/userattend/components/edit.vue"
  import {
    // ElSelect,
    // ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    // ElMessage,
    // ElMessageBox,
    ElDialog
  } from 'element-plus';

  export default {
    components: {
      // "kade-dept-select-tree": deptSelectTree,
      // userEdit,
      // ElSelect,
      // ElOption,
      ElInput,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      // ElMessage,
      // ElMessageBox,
      ElDialog
    },
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      type:{
        type: String,
        default: "",
      },
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    setup(props , context) {
      // const ideTypeList = useDict("ATTENTION_USER_IDE");
      // let {data1} = await getAttentionUserAttendAll('');
      // const atgIdList = data1;
      const state = reactive({
        loading: false,
        isEdit:false,
        isFresh:'0',
        isEdit1:false,
        form:{
          keyWord:'',
          currentPage:1,
          userDisType:'2',
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        selectData:[],
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionUserAttendPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }
      const reset=()=>{
        state.form={
          currentPage:1,
          userDisType:'2',
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSelectionChange = (val)=>{
        state.selectData = val;
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const cancel = () => {
        context.emit("update:modelValue", '');
      };
      const submit = () => {
        if(state.selectData){
          context.emit("update:modelValue", state.selectData);
        }
      };

      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            getList();
          }
        }
      );

      return {
        state,
        // ideTypeList,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        handleSelectionChange,
        cancel,
        close,
        submit,
        // attrs,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
