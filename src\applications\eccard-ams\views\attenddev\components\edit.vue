<template>
  <el-dialog append-to-body="true" :model-value="modelValue" width="500px" :title="state.model.degId ? '修改设备分组' : '新增设备分组'"  :before-close="update" :close-on-click-modal="false" :modal="true">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small" style="margin-top:20px;">
      <el-form-item label="设备组名称" prop="degName">
        <el-input placeholder="请输入设备组名称" v-model="state.model.degName" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
// import Modal from "@/components/modal";
// import {dateStr} from "@/utils/date.js"
import { computed, reactive, ref, watch } from "vue";

import {
  // ElTabPane,
  // ElTabs,
  // ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  // ElSwitch,
  ElButton,
  ElDialog
} from "element-plus";

import { addAttentionAttendDevGroupAll, editAttentionAttendDevGroupAll } from "@/applications/eccard-ams/api";
// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });

const rules = {
  degName: [
    { required: true, message: "请输入设备分组名称" , trigger: 'blur'},
    { min:1 , max: 20, message: "类别名称不能超过20个字符" , trigger: 'blur'},
  ],
};

export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    type:{
      type: String,
      default: "",
    },
    data:{
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElDialog
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: {
        "degName" : '',
        "degId" : ''
      }
    });
    const cancel = () => {
      // formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            const fn = props.data?.degId ?  editAttentionAttendDevGroupAll : addAttentionAttendDevGroupAll;
            const { message, code } = await fn(state.model);
            if (code === 0) {
              ElMessage.success(message);
            } else {
              ElMessage.error(message);
            }
            context.emit("update:modelValue", true);
            context.emit("change");
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }else{
          console.log("验证不通过！")
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("close", v);
    };
    watch(
      () => props.data,
      (n) => {
        if (n) {
          state.model = props.data;
        }else{
          state.model = {'degName' : ''};
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>
