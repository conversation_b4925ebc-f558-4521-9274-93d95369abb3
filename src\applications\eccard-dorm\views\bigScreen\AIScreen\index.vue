<template>
  <div class="box ScaleBox" ref="ScaleBox">
    <kade-header-box :title="route.params.buildName+route.params.unitNum+'单元AI监控大屏'" />
    <div class="main">
      <!--       <el-row :gutter="15">
        <el-col :span="24">
          <el-row :gutter="15">
            <el-col class="margin-bt" :span="6" v-for="(item, index) in state.countList" :key="index">
              <div class="person-box">
                <div class="box-title">{{ item.label }}</div>
                <div class="box-num">1295</div>
              </div>
            </el-col>
            <el-col class="margin-bt" :span="24">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
                        <el-col class="margin-bt" :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
            <el-col :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
            <el-col :span="12">
              <img src="http://tva3.sinaimg.cn/large/bf976b12gy1g51fau0uvdg208c0784a1.gif" alt="">
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <kade-border-box style="height:100%;padding: 10px 0;">
            <kade-contrast-list />
          </kade-border-box>
        </el-col>
      </el-row> -->
      <div class="person-box">
        <div class="person-item" v-for="(item, index) in state.countList" :key="index" :style="{ backgroundColor: item.color }">
          <div class="box-title">{{ item.label }}</div>
          <div class="box-num">{{ state.personData[item.key]?state.personData[item.key]:0 }}</div>
        </div>
      </div>
      <iframe class="iframe" :src="`http://${route.params.ip}/#/structure`" frameborder="0">
        您当前的浏览器不支持页面上的功能，请升级您当前的浏览器版本或使用谷歌浏览器访问当前页面
      </iframe>
    </div>
  </div>
</template>
<script>
import { reactive, onMounted } from "vue"
import { useRoute } from "vue-router"
// import { ElCol, ElRow, } from "element-plus"
import { dateStr } from "@/utils/date.js"
import { bigScreenSummary } from "@/applications/eccard-dorm/api"


import headerBox from "../components/headerBox.vue"
/* import borderBox from "../components/borderBox.vue"
import contrastList from "./components/contrastList.vue" */

export default {
  components: {
    // ElCol, ElRow,
    "kade-header-box": headerBox,
    /*     "kade-border-box": borderBox,
        "kade-contrast-list": contrastList, */

  },
  setup() {
    const route = useRoute()
    const state = reactive({
      countList: [
        { label: "入住人数", key: "dueNumber", color: "#3399ff" },
        { label: "在寝人数", key: "inRoomNumber", color: "#02d200" },
        { label: "离寝人数", key: "outRoomNumber", color: "#ff8726" },
        { label: "请假人数", key: "leaveNumber", color: "#ae00df" },
      ],
      personData: {}
    })
    const getCount = async () => {
      let { data } = await bigScreenSummary({
        beginDate: dateStr(new Date()),
        endDate: dateStr(new Date()),
        buildId: route.params.buildId,
      })
      console.log(data);
      state.personData = data
    }
    onMounted(() => {
      getCount()
      console.log(route);

    })
    return {
      route,
      state,
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  background: #001034;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .main {
    flex: 1;
    padding: 0 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .margin-bt {
    margin-bottom: 15px;
  }

  .person-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .person-item {
      width: 23%;
      color: #fff;
      padding: 10px;
      background-color: rgb(51, 153, 255);
      box-sizing: border-box;

      .box-title {
        font-weight: 400;
        font-size: 14px;
      }

      .box-num {
        margin: 20px 0;
        font-weight: 700;
        font-size: 56px;
        color: rgb(255, 255, 255);
        text-align: center;
      }
    }
  }

  .iframe {
    margin-top: 20px;
    flex: 1;
    box-sizing: border-box;
    width: 100%;

    border: 1px solid #00bfbf;
  }

  .border-box {
    border: 1px solid #00bfbf;
    box-sizing: border-box;
    margin-bottom: 15px;
  }
}
</style>