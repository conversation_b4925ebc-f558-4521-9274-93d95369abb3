<template>
  <kade-route-card>
    <div class="card-content">
      <kade-table-wrap title="类型列表">
        <el-table style="width: 100%" :data="options.dataList" v-loading="options.loading" border stripe>
          <el-table-column label="ID" prop="cardTypeId" align="center" width="70"></el-table-column>
          <el-table-column label="卡类名称" prop="ctName" align="center" width="150"></el-table-column>

          <el-table-column label="启用押金" align="center" width="100">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.ctDepositEnable) }}
            </template>
          </el-table-column>
          <el-table-column label="押金(元)" align="center">
            <template #default="scope">
              {{ scope.row.ctDepositEnable === 'ENABLE_TRUE' ? scope.row.ctDeposit : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="退押金比例" align="center">
            <template #default="scope">
              {{ (scope.row.ctDepositEnable === 'ENABLE_TRUE'&&scope.row.refundDepositRate )? scope.row.refundDepositRate + '%' : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="启用有效期" width="100" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.ctValidDateEnable) }}
            </template>
          </el-table-column>
          <el-table-column label="有效期(年)" align="center">
            <template #default="scope">
              {{ scope.row.ctValidDateEnable === 'ENABLE_TRUE' ? scope.row.ctValidDate : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="充值赠送" width="100" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.giveEnable) }}
            </template>
          </el-table-column>
          <el-table-column label="赠送比例" align="center">
            <template #default="scope">
              {{ scope.row.giveEnable === 'ENABLE_TRUE' ? scope.row.giveRate+'%' : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.ctState) }}
            </template>
          </el-table-column>
          <el-table-column label="工本费" prop="ctFlatCost" align="center" width="150"></el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background v-model:current-page="options.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="options.total" v-model:page-size="options.pageSize" @current-change="(val) => pageChange(val)" @size-change="(val) => sizeChange(val)">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </div>
    <kade-type-edit title="编辑" v-model="showCreateModal" :id="id" @change="loadData" />
  </kade-route-card>
</template>
<script>
import {
  ElButton,
  ElPagination,
  ElTable,
  ElTableColumn,
} from 'element-plus';
import { ref } from 'vue';
import { usePagination } from '@/hooks/usePagination';
import { getCardTypeByPage } from '@/applications/eccard-basic-data/api';
import Edit from './components/edit';
export default {
  components: {
    'el-button': ElButton,
    'el-table': ElTable,
    'el-table-column': ElTableColumn,
    'el-pagination': ElPagination,
    'kade-type-edit': Edit,
  },
  setup() {
    const { options, loadData, pageChange, sizeChange } = usePagination(getCardTypeByPage);
    const showCreateModal = ref(false);
    const id = ref(null);
    const handleEdit = ({ cardTypeId }) => {
      id.value = cardTypeId;
      showCreateModal.value = true;
    }
    return {
      options,
      loadData,
      showCreateModal,
      handleEdit,
      id,
      pageChange,
      sizeChange
    }
  }
}
</script>
<style scoped lang="scss"></style>