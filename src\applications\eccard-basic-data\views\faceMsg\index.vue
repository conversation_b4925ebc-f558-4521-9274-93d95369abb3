<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="100px" size="small" inline>
        <el-form-item label="关键字">
          <el-input size="small" v-model="state.form.keyWord" placeholder="姓名或编号关键字搜索" />
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="身份类别">
          <el-select clearable v-model="state.form.roleId">
            <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="照片状态">
          <el-select clearable v-model="state.form.uploadStatus">
            <el-option v-for="(item, index) in uploadStatusList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="特征值状态">
          <el-select clearable v-model="state.form.faceFeatureStatus">
            <el-option v-for="(item, index) in faceFeatureStatusList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="更新时间">
          <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="state.defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="人员信息列表">
      <template #extra>
        <el-button icon="el-icon-plus" @click="state.isConfig=true" size="small" class="btn-deep-blue">算法配置</el-button>
      </template>
      <el-table v-loading="state.loading" :data="state.dataList" border>
        <el-table-column show-overflow-tooltip label="学号" prop="userCode" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="姓名" prop="userName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="身份类别" prop="roleName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="照片状态" prop="hasFaceImage" align="center">
          <template #default="scope">
            {{ scope.row.hasFaceImage ? '已上传' : '未上传' }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="人脸特征值状态" prop="hasFaceFeature" align="center">
          <template #default="scope">
            {{ scope.row.hasFaceFeature ? '已生成' : '未生成' }}
          </template>
        </el-table-column>

        <el-table-column show-overflow-tooltip label="更新时间" prop="lastModifyTime" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="操作" align="center">
          <template #default="scope">
            <a v-if="scope.row.hasFaceImage" :href="scope.row.faceImg" target="_blank">查看照片</a>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-face-config v-model="state.isConfig"/>
  </kade-route-card>
</template>
<script>
import { ref, reactive, onMounted } from "vue";
import { timeStr } from "@/utils/date.js"
import {
  getRolelist,
  userFaceListByPage
} from "@/applications/eccard-basic-data/api";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import config from "./components/config"
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElButton
} from "element-plus";
const uploadStatusList = [
  { label: '已上传', value: 'UPLOAD_YES' },
  { label: '未上传', value: 'UPLOAD_NO' },
]

const faceFeatureStatusList = [
  { label: '已生成', value: 'FEATURE_YES' },
  { label: '未生成', value: 'FEATURE_NO' },
]

export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElButton,
    "kade-dept-select-tree": deptSelectTree,
    "kade-face-config":config
  },
  setup() {
    const showCreateModal = ref(false);
    const state = reactive({
      loading: false,
      isConfig: false,
      roleList: [],
      defaultTime: [
        new Date(new Date(new Date().toLocaleDateString()).getTime()),
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
        ),
      ],
      requestDate: "",
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      dataList: [],
      total: 0,
      rowData: "",
    });
    const queryRolelist = async () => {
      let { data } = await getRolelist();
      state.roleList = data;
    };

    const getList = async () => {
      state.loading = true;
      let { data: { list, total } } = await userFaceListByPage(state.form)
      state.dataList = list
      state.total = total
      state.loading = false
    };

    const changeDate = val => {
      if (val) {
        state.form.startDate = timeStr(val[0])
        state.form.endDate = timeStr(val[1])
      } else {
        delete state.form.startDate
        delete state.form.endDate
      }
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      }
      state.requestDate = ""
    }
    const handleCurrentChange = val => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    onMounted(() => {
      getList();
      queryRolelist()
    });
    return {
      uploadStatusList,
      faceFeatureStatusList,
      showCreateModal,
      state,
      changeDate,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange
    };
  },
};
</script>
<style lang="scss" scoped>
.role {
  width: 100%;
  height: 100%;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>