import request from '@/service';


//健康申报项目分页查询
export function healthDeclarationPage(params) {
  return request.post('/eccard-basic-data/healthDeclaration/page', params,);
}
//健康申报项目列表查询
export function healthDeclarationList(params) {
  return request.post('/eccard-basic-data/healthDeclaration/list', params,);
}
//健康申报项目新增
export function healthDeclarationAdd(params) {
  return request.post('/eccard-basic-data/healthDeclaration', params,);
}
//健康申报项目修改
export function healthDeclarationUpdate(params) {
  return request.put('/eccard-basic-data/healthDeclaration', params,);
}
//健康申报项目删除
export function healthDeclarationDel(params) {
  return request.delete(`/eccard-basic-data/healthDeclaration/${params}`,);
}


//健康申报记录分页查询
export function healthDeclarationRecordPage(params) {
  return request.post('/eccard-basic-data/healthDeclarationRecord/page', params,);
}

//健康申报查询当时普通请假信息
export function getLeaveInfoByRecord(params) {
  return request.post('/eccard-basic-data/healthDeclarationRecord/getLeaveInfoByRecord', params,);
}
//健康申报查询当时当时病休信息
export function getSickLeaveInfoByRecord(params) {
  return request.post('/eccard-basic-data/healthDeclarationRecord/getSickLeaveInfoByRecord', params,);
}