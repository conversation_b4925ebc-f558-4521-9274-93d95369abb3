<template>
  <div style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="开始时间">
          <el-date-picker v-model="state.form.attendMonthBegin" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="true" />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker v-model="state.form.attendMonthEnd" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="true" />
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgId" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgId = val.deptPath)" />
        </el-form-item>
        <el-form-item label="所属考勤组">
          <el-select v-model="state.form.atgId" :clearable="true">
            <el-option v-for="(item,index) in state.groupList" :key="index" :label="item.atgName" :value="item.atgId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打卡状态">
          <el-select v-model="state.form.checkState" :clearable="true">
            <el-option v-for="(item,index) in [{name:'已打卡' , key:1} , {name:'未打卡' , key:0}]" :key="index" :label="item.name" :value="item.key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字" :clearable="true">
          <el-input v-model="state.form.keyWord" placeholder="请输入姓名或编号搜索"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap :title="'每日汇总表' + (state.statsList && state.statsList['atrNum'] && state.statsList['atrAttendNum'] != null ? '：今日应到' + (state.statsList['atrNum'] ? state.statsList['atrNum'] : '0') + '人，实到' +
          (state.statsList['atrAttendNum'] ? state.statsList['atrAttendNum'] : '0') + '人，迟到' + (state.statsList['atrLateNum'] ? state.statsList['atrLateNum'] : '0') +
      '人，未到' + (state.statsList['atrLeaveNum'] ? state.statsList['atrLeaveNum'] : '0') + '人，请假' + (state.statsList['atrAbseNum'] ? state.statsList['atrAbseNum'] : '0') + '人' : '')">
      <template #extra>
        <el-button size="mini" type="primary" icon="Download" @click="handleClick(1)">导出</el-button>
        <el-button size="mini" type="danger" icon="Printer" @click="handleClick(2)">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属考勤组" prop="atgName" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="考勤日期" prop="atdDay" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="工作时长" prop="armWorkTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.armWorkTimes ? scope.row.armWorkTimes + '分钟' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="班次" prop="atcClassname" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="一次班">
          <el-table-column label="上班" prop="atdOneStime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdOneStime) }}
            </template>
          </el-table-column>
          <el-table-column label="中午" prop="atdOneStime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdOneNtime) }}
            </template>
          </el-table-column>
          <el-table-column label="下班" prop="atdOneEtime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdOneEtime) }}
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column label="二次班">
          <el-table-column label="上班" prop="atdTwoStime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdTwoStime) }}
            </template>
          </el-table-column>
          <el-table-column label="下班" prop="atdTwoEtime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdTwoEtime) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="三次班">
          <el-table-column label="上班" prop="atdThreeStime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdThreeStime) }}
            </template>
          </el-table-column>
          <el-table-column label="下班" prop="atdThreeEtime" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getRowStyle(scope.row.atdThreeEtime) }}
            </template>
          </el-table-column>
        </el-table-column> -->
        <el-table-column label="迟到时长" prop="armLateTimes" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="早退时长" prop="armLeaveTimes" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="加班时长" prop="atdOverTimes" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.atdOverTimes ? scope.row.atdOverTimes + '分钟' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="缺勤天数" prop="armAbseDays" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { downloadXlsx , print } from "@/utils"
  import { dateStr } from "@/utils/date.js"
  import deptSelectTree from '@/components/tree/deptSelectTree'
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElDatePicker,
    ElMessage,
    // ElMessageBox
  } from 'element-plus';

  import { getAttentionDayReportPage , getAttentionGroupInfoPageAll,downLoadFile , getCurrentDayAttentReport } from "@/applications/eccard-ams/api";

  export default {
    props: {
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    components: {
      ElForm,
      ElFormItem,
      ElInput,
      ElSelect,
      ElOption,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      ElDatePicker,
      "kade-dept-select-tree":deptSelectTree,
    },
    setup() {
      const symbolList = useDict("ATTENTION_REPORT_ATTENDTYPE");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          attendMonthBegin:new Date(),
          attendMonthEnd:new Date(),
          orgId:'',
          atgId:'',
          keyWord:'',
          checkState:null,
          pageNum:1,
          pageSize:10
        },
        groupList:[],
        statsList:null,
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        columnList:[],
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        if(state.form.attendMonthBegin) {
          state.form.attendMonthBegin = dateStr(state.form.attendMonthBegin);
        }else{
          // ElMessage.warning("请输入开始时间！");state.loading=false;return;
        }
        if(state.form.attendMonthEnd) {
          state.form.attendMonthEnd = dateStr(state.form.attendMonthEnd);
        }else{
          // ElMessage.warning("请输入结束时间！ ");state.loading=true;return;
        }

        let {data}=await getAttentionDayReportPage(state.form)
        state.dataList=data.list

        let tempObj = await getCurrentDayAttentReport();
        if(tempObj.code == 0){
          //成功
          state.statsList = tempObj.data;
        }

        state.total=parseInt(data.count)
        state.loading=false;
      }
      const getRowStyle = (item)=>{
        if(!item){
          return "";
        }
        if(item == '漏刷'){
          return "<span style='color:#FF3F3F'>漏刷</span>";
        }else if(item.toString().indexOf('早退') > -1){
          return "<span style='color:#FF8726'>" + item + "</span>";
        }else if(item.toString().indexOf('迟到') > -1){
          return "<span style='color:#FF8726'>" + item + "</span>";
        }
        return item;
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          pageNum:1,
          pageSize:10,
          attendMonthBegin:new Date(),
          attendMonthEnd:new Date(),
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      // const del=async (row)=>{
      //   let {code,message}=await delSysMessage(row.id)
      //   if(code===0){
      //     ElMessage.success(message)
      //     getList()
      //   }
      // }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      const handleClick=async (flag)=>{
          if (flag == 1) {
            let res = await downLoadFile("/eccard-ams/api/ams/attention-day-report/export", state.form);
            downloadXlsx(res, "每日汇总报表.xlsx");
          }else{
            let {code, message} = await downLoadFile("/eccard-ams/api/ams/attention-day-report/export?type=2", state.form, 2);
            if (code == 0) {
              print(message, '每日汇总报表')
            } else {
              ElMessage.warning(message);
            }
          }
      }

      onMounted(async ()=>{
        getList();
        let {data}=await getAttentionGroupInfoPageAll();
        state.groupList = data;


      })
      return {
        state,
        symbolList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        // del,
        close,
        getRowStyle,
        handleClick,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
