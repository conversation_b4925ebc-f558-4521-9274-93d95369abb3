<template>
  <div class="person-msg">
    <div class="now-person-num">
      <div class="title">入住人数</div>
      <div class="num">{{ state.details.dueNumber }}</div>
    </div>
    <div class="person-total">
      <div class="title">归寝率</div>
      <div class="total-box">
        <div class="total-left">
          <kade-label-box class="label-box">
            <div class="label">未归人数</div>
            <div class="value">{{ state.details.notReturnNumber }}</div>
          </kade-label-box>
          <kade-label-box class="label-box">
            <div class="label">已归人数</div>
            <div class="value">{{ state.details.actuaNumber }}</div>
          </kade-label-box>
        </div>
        <kade-progress style="margin:0 50px" :details="state.details" />
        <div class="total-right">
          <kade-label-box class="label-box">
            <div class="label">晚归人数</div>
            <div class="value">{{ state.details.lateNumber }}</div>
          </kade-label-box>
          <kade-label-box class="label-box">
            <div class="label">请假人数</div>
            <div class="value">{{ state.details.leaveNumber }}</div>
          </kade-label-box>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { onMounted, onBeforeUnmount, reactive } from "vue"
import { attendanceBuildingSummary, buildingStayInfo } from "@/applications/eccard-dorm/api"
import { dateStr } from "@/utils/date.js"
import { useRoute } from "vue-router"
import progress from "./progress.vue"
import labelBox from "../../components/labelBox.vue"

export default {
  components: {
    "kade-label-box": labelBox,
    "kade-progress": progress,
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      timer: null,
      details: {
        dueNumber: 0,
        actuaNumber: 0,
        notReturnNumber: 0,
        lateNumber: 0,
        leaveNumber: 0,
      }
    })


    const getData = async () => {
      let params = {
        attendancePeriodCode: 4,
        ...route.params,
        beginDate: dateStr(new Date()),
        endDate: dateStr(new Date()),
        currentPage: 1,
        pageSize: 10
      }
      let { data: { list } } = await attendanceBuildingSummary(params)
      if (list.length) {
        state.details = list[0]
      }
    }
    const getBuildingStayInfo = async () => {
      let { data: { dataList } } = await buildingStayInfo({
        ...route.params,
        beginDate: dateStr(new Date()),
        endDate: dateStr(new Date()),
        currentPage: 1,
        pageSize: 10
      })
      if (dataList.length) {
        state.details.dueNumber = dataList[0].checkInPersonCount
      }
    }
    onMounted(() => {
      getData()
      getBuildingStayInfo()
      state.timer = setInterval(() => {
        getData()
        getBuildingStayInfo()
      }, 10000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      state
    }
  }
}
</script>
<style lang="scss" scoped>
.person-msg {
  .now-person-num {
    text-align: center;

    .title {
      font-weight: 400;
      font-size: 20px;
      color: #0EE4F9;
      margin-bottom: 20px;
    }

    .num {
      font-weight: 700;
      font-size: 58px;
      color: #FFFFFF;
    }
  }

  .person-total {
    .title {
      text-align: center;

      font-weight: 400;
      font-size: 20px;
      color: #0EE4F9;
      margin: 20px 0 30px;
    }

    .total-box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .total-left,
      .total-right {
        height: 100%;
        flex: 1;

        :first-child {
          margin-bottom: 40px;
        }
      }

      .label-box {
        box-sizing: border-box;
        width: 100%;
        padding: 10px;

        .label {
          font-weight: 400;
          font-size: 14px;
          color: #0EE4F9;
          margin-bottom: 0;
        }

        .value {
          font-weight: 700;
          font-size: 30px;
          color: #FE7007;
          text-align: center;
        }
      }
    }
  }
}
</style>