<template>
  <div style="height: auto">
    <el-form inline size="mini" label-width="100px">
      <el-form-item label="考勤时间">
        <el-date-picker
          v-model="state.form.dateRange"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0)]"
          :clearable="true"
        />
      </el-form-item>
      <el-form-item label="考勤状态">
        <el-select v-model="state.form.atiAnalyResult"  :clearable="true">
          <el-option :label="'未分析'" :value="'-1'"></el-option>
          <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="考勤设备">
        <el-input v-model="state.form.atdName" placeholder="考勤设备搜索" :clearable="true"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="search" icon="el-icon-search">搜索</el-button>
        <el-button @click="reset" icon="el-icon-refresh-right" size="small">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
      <el-table-column label="分析时间" prop="atiDate" align="center"></el-table-column>
      <el-table-column label="打卡时间" prop="atiCheckDate" align="center"></el-table-column>
      <el-table-column label="分析状态" prop="atiAnalyResult" align="center">
        <template #default="scope">
          {{ scope.row.atiAnalyResult ? '已分析' : '未分析' }}
        </template>
      </el-table-column>
      <el-table-column label="考勤设备" prop="atdName" align="center"></el-table-column>
      <el-table-column label="操作" prop="" align="center">
        <template #default="scope">
          <el-button @click="edit(scope.row , 'edit')" type="text" size="mini">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <attend-edit :modelValue="state.isEdit" :type="state.type" :userinfo="state.dataObj" :data="state.rowData"  @update:modelValue="close" @edit="state.type='edit'" />
  </div>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import {timeStr} from "@/utils/date"
  import {
    // ElSwitch,
    ElDatePicker,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
  } from 'element-plus';
  import { useDict } from "@/hooks/useDict";
  import { getAttentionInfoPage } from "@/applications/eccard-ams/api";
  import attendEdit from "@/applications/eccard-ams/views/userattend/components/attendedit.vue"

  export default {
    props: {
      data:{
        type: Object,
        default: null,
      },
    },
    components: {
      attendEdit,
      ElDatePicker,
      ElForm,
      ElFormItem,
      ElInput,
      ElSelect,
      ElOption,
      'el-table': ElTable,
      'el-button': ElButton,
      'el-table-column': ElTableColumn,
      'el-pagination': ElPagination,
    },
    setup(props) {
      const statusList = useDict("USER_ATTENDINFO_STATUS");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          userId: parseInt(props.data.userId),
          dateRange:'',
          createTimeStart:'',
          createTimeEnd:'',
          atiAnalyResult : '',
          atdName: '',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        dataObj:props.data,
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true;
        if(state.form.dateRange) {
          state.form.createTimeStart = timeStr(state.form.dateRange[0]);
          state.form.createTimeEnd = timeStr(state.form.dateRange[1]);
        }else{
          state.form.createTimeStart = '';
          state.form.createTimeEnd = '';
        }
        let {data}=await getAttentionInfoPage({userId:state.form.userId , createTimeStart:state.form.createTimeStart ,
          createTimeEnd:state.form.createTimeEnd , atiAnalyResult : state.form.atiAnalyResult, atdName : state.form.atdName ,
          currentPage : state.form.currentPage , pageSize:state.form.pageSize })

        // let {data}=await getAttentionInfoPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          currentPage:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const close=()=>{
        state.isEdit=false
      }

      onMounted(()=>{
        getList();
        state.isEdit=false;
      })
      return {
        state,
        statusList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        close
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
