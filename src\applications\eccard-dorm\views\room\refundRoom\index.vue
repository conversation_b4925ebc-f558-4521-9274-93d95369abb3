<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="120px" size="small">
        <kade-linkage-select :value="state.form" :data="linkageData" @change="linkageChange" />
        <el-form-item label="人员编号">
          <el-input v-model="state.form.userCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input v-model="state.form.userName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="人员组织">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="辅导员编号/姓名">
          <el-input v-model="state.form.counsellorKey" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="退宿列表">
      <template #extra>
        <el-button icon="el-icon-daorutupian" @click="state.isaAllRefund = true" size="small" class="btn-yellow">批量退宿
        </el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" height="55vh" border stripe v-loading="state.loading">
        <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :prop="item.prop" :label="item.label" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="180px">
          <template #default="scope">
            <el-button class="green" size="mini" @click="handleRefund(scope.row)" type="text">退宿
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-refund :isShow="state.isRefund" :rowData="state.rowData" @close="close" />
    <kade-all-refund :isShow="state.isaAllRefund" @close="close" />
  </kade-route-card>
</template>
<script>
import { ElButton, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination } from "element-plus";
import { onMounted, reactive } from 'vue';
import { getCheckOutList } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import refund from "./components/refund"
import allRefund from "./components/allRefund"
const column = [
  { prop: "areaName", label: "区域", width: "" },
  { prop: "roomString", label: "房间", width: "" },
  { prop: "bedNum", label: "床位", width: "" },
  { prop: "userCode", label: "人员编号", width: "" },
  { prop: "userName", label: "人员姓名", width: "" },
  { prop: "deptName", label: "人员组织", width: "" },
  { prop: "stayStatus", label: "住宿状态", width: "" },
]
const linkageData = {
  area: { label: '区域', valueKey: "areaPath", key: "areaPath" },
  building: { label: '楼栋', valueKey: "buildId" },
  unit: { label: '单元', valueKey: "unitNum" },
  floor: { label: '楼层', valueKey: "floorNum" },
  room: { label: '房间', valueKey: "roomId" },
}
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-dept-select-tree": deptSelectTree,
    "kade-refund": refund,
    "kade-all-refund": allRefund,
  },
  setup() {
    const state = reactive({
      loading: false,
      isRefund: false,
      isaAllRefund: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: {},
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getCheckOutList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleRefund = row => {
      state.rowData = row
      state.isRefund = true
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      getList()
    }

    const handlePageChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const close = (val) => {
      if (val) {
        getList()

      }
      state.isRefund = false
      state.isaAllRefund = false
    }
    onMounted(() => {
      getList()
    })
    return {
      column,
      linkageData,
      state,
      handleRefund,
      linkageChange,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      close
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }

  .el-input__inner {
    width: 200px;
  }
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
  border-top: none;
}

:deep(.el-dialog) {
  padding-bottom: 20px;
  border-radius: 8px;
}
</style>