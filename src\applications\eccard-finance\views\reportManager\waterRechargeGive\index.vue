<template>
  <div class="padding-box">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="统计时间">
          <el-date-picker v-model="state.requestDate" :default-time="defaultTime" unlink-panels type="datetimerange" range-separator="~" start-placeholder="请选择时间" end-placeholder="请选择时间" :clearable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用户编号:">
          <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名:">
          <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="交易类型:">
          <el-select clearable v-model="state.form.costType" placeholder="请选择">
            <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName" :value="item.costCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="水控充值赠送明细报表" v-loading="state.loading">
      <template #extra>
        <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
      </template>
      <el-table height="55vh" border :data="state.dataList">
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" :small="small" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage
} from "element-plus";
import { requestDefaultTime, defaultTime } from "@/utils/reqDefaultDate";
import deptSelectTree from "@/components/tree/deptSelectTree";
import { downloadXlsx } from "@/utils";
import { timeStr } from "@/utils/date.js";
import { waterRechargeGiftPage, costType, waterRechargeGiftExport } from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
const column = [
  { prop: "userCode", label: "用户编号", },
  { prop: "userName", label: "用户姓名" },
  { prop: "deptName", label: "组织机构" },
  { prop: "tradeAmount", label: "充值金额" },
  { prop: "giftAmount", label: "赠送金额" },
  { prop: "giftWalletBalance", label: "赠送钱包余额" },
  { prop: "tradeDate", label: "交易时间" },
  { prop: "costTypeName", label: "交易类型" },
  { prop: "tradeSourceName", label: "交易来源" },
  { prop: "operatorName", label: "操作员" },
  { prop: "deviceName", label: "充值终端" },
]

export default {
  components: {
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {
        pageSize: 10,
        currentPage: 1,
      },
      checked: true,
      dataList: [],
      total: 0,
      requestDate: requestDefaultTime(),

    });
    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data.filter(item => item.costCode == 101 || item.costCode == 401);
      });
    };

    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.startTime = timeStr(state.requestDate[0]);
        params.endTime = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择统计时间")
        return false
      }
      return params
    }
    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let { data: { page: { list, total } } } = await waterRechargeGiftPage(params)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await waterRechargeGiftExport(params);
        downloadXlsx(res, "水控充值赠送明细报表.xlsx")
        state.loading = false;
      }
      catch {
        state.loading = false
      }
    };
    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = requestDefaultTime()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    onMounted(() => {
      // getList();
      getCostTypeList()
    });
    return {
      column,
      defaultTime,
      state,
      search,
      reset,
      exportClick,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>