<template>
  <el-dialog :model-value="dialogVisible" :title="title" width="700px" :before-close="handleClose">
    <el-form label-width="120px" :rules="rules" ref="formRef" size="small" :model="state.form" v-loading="state.loading">
      <el-row>
        <el-col :span="12">
          <el-form-item label="组名称：" prop="name">
            <el-input style="width: 100%" v-model="state.form.name" placeholder="请输入组名称" maxlength="20"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考勤开始日期：" prop="beginDate">
            <el-date-picker style="width: 100%" v-model="state.form.beginDate" type="date" placeholder="请选择日期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考勤结束日期：" prop="endDate">
            <el-date-picker style="width: 100%" v-model="state.form.endDate" type="date" placeholder="请选择日期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="启用状态：" prop="status">
            <el-select style="width: 100%" v-model="state.form.status" placeholder="请选择">
              <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="考勤班级：" prop="deptIds">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptIds" valueKey="id" :multiple="true"
              @valueChange="(val) => (state.form.deptIds = val)" />

            <!--             <el-select v-model="state.form.deptIds" placeholder="请选择" multiple>
              <el-option v-for="(item, index) in state.deptGradeList" :key="index" :label="item.deptPathName"
                :value="item.id" />
            </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公休模式：" prop="holidayMode">
            <el-select style="width: 100%" v-model="state.form.holidayMode" placeholder="请选择">
              <el-option v-for="(item, index) in patternList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公休周期：" prop="cycle" v-if="state.form.holidayMode === '多周'">
            <el-input-number style="width: 100%" v-model="state.form.cycle" :min="1" :max="10" @change="handleChange" size="mini" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="考勤时段：" prop="time">
        <span>可分别设置上课期间和休息期间的考勤时段。</span>
      </el-form-item>
      <el-table :data="state.weekList" border style="width: 524px">
        <el-table-column prop="week" label="星期" align="center" width="70px">
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" width="70px"></el-table-column>
        <el-table-column label="考勤时段" align="center" width="382px">
          <template #default="scope">
            <div class="checkbox">
              <el-checkbox v-for="(item, index) in state.timeList" :key="index" v-model="scope.row.value"
                :label="item.code" size="mini" multiple>{{ item.name }}</el-checkbox>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-form-item label="备注：">
        <el-input v-model="state.form.remarks" type="textarea" style="width: 523px" maxlength="200"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit()" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElRow,
  ElCol,
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElInputNumber,
  ElCheckbox,
  ElMessage,
} from "element-plus";
import { timeStr } from "@/utils/date.js"
import { reactive, ref, nextTick, watch, computed, onMounted } from "vue";
import { useDict } from '@/hooks/useDict'
import {
  addAttendanceGroup,
  editAttendanceGroup,
  attendancePeriodList
} from '@/applications/eccard-dorm/api.js'
import {
  userDeptGradeTree
} from '@/applications/eccard-sys/api.js'
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const rules = {
  name: { required: true, message: "请输入组名称", trigger: "blur" },
  beginDate: { required: true, message: "请选择日期", trigger: "change" },
  endDate: { required: true, message: "请选择日期", trigger: "change" },
  status: { required: true, message: "请选择", trigger: "change" },
  deptIds: { required: true, message: "请选择", trigger: "change" },
  holidayMode: { required: true, message: "请选择", trigger: "change" },
  cycle: { required: true, message: "请选择", trigger: "change" },
};
const patternList = [
  { label: "单周", value: "单周" },
  { label: "多周", value: "多周" },
];
const weekListFnc = () => {
  return [
    { week: "星期一", filed: "monday", status: '上课', value: [] },
    { week: "星期二", filed: "tuesday", status: '上课', value: [] },
    { week: "星期三", filed: "wednesday", status: '上课', value: [] },
    { week: "星期四", filed: "thursday", status: '上课', value: [] },
    { week: "星期五", filed: "friday", status: '上课', value: [] },
    { week: "星期六", filed: "saturday", status: '休息', value: [] },
    { week: "星期日", filed: "sunday", status: '休息', value: [] },
  ]
}
export default {
  components: {
    ElRow,
    ElCol,
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElInputNumber,
    ElCheckbox,
    "kade-dept-select-tree": deptSelectTree,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogType: {
      type: String,
      default: "",
    },
    rowData: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const statusList = useDict('SYS_ENABLE')
    const formRef = ref(null);
    const state = reactive({
      loading: false,
      timeList: [],
      deptGradeList: [],
      form: {},
      weekList: weekListFnc()
    });
    const getPeriodList = async () => {
      let { data } = await attendancePeriodList()
      state.timeList = data
    };
    const getUserDeptGradeTree = async () => {
      let { data } = await userDeptGradeTree()
      state.deptGradeList = data
    };
    const handleClose = () => {
      context.emit("close", false);
    };
    const submit = () => {
      console.log(state.weekList, state.form);
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = {
            ...state.form,
            beginDate: timeStr({ ...state.form }.beginDate),
            endDate: timeStr({ ...state.form }.endDate),
          }
          state.weekList.forEach(item => {
            params[item.filed] = `${item.status == '上课' ? 1 : 0}|${item.value.join(',')}`
          })
          console.log(params);
          let fn = props.dialogType == 'add' ? addAttendanceGroup : editAttendanceGroup
          try {
            state.loading = true
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message);
              context.emit("close", true);
              state.loading = false
            }
          }
          catch {
            state.loading = false
          }

        }
      })
    };
    const title = computed(() => {
      if (props.dialogType === "add") {
        return "添加分组";
      } else {
        return "编辑分组";
      }
    });
    watch(
      () => props.dialogVisible,
      (val) => {
        if (val) {
          if (props.dialogType === "add") {
            state.form = {};
            state.weekList = weekListFnc()
          } else {
            state.form = { ...props.rowData }
            state.form.deptIds = state.form.deptIds.map(item => Number(item))
            state.weekList = weekListFnc()
            state.weekList.forEach(item => {
              console.log(state.form);
              item.value = state.form[item.filed].substring(2, state.form[item.filed].length).split(",")
            })
          }
          nextTick(() => {
            formRef.value.clearValidate();
          });
        }
      }
    );
    onMounted(() => {
      getPeriodList()
      getUserDeptGradeTree()
    })
    return {
      state,
      rules,
      title,
      submit,
      formRef,
      handleClose,
      patternList,
      statusList,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-table {
  margin: 0 20px 15px 120px;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-table-column {
  line-height: 20px;
}





.checkbox {
  display: flex;
}

:deep(.el-checkbox) {
  margin-right: 10px;
}

:deep(.el-table--enable-row-transition .el-table__body td) {
  padding: 2px;
}

:deep(.el-checkbox__label) {
  font-size: 10px;
  padding-left: 5px;
}
</style>