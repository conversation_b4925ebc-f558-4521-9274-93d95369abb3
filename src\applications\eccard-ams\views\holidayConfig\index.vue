<template>
  <div class="padding-box">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="时间">
          <el-date-picker v-model="state.form.holidayYear" type="year" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="节假日配置">
      <template #extra>
        <el-button size="mini" type="primary" icon="Download" @click="handleEdit({})">新增</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" border stripe>
        <el-table-column label="配置名称" prop="holidayName" align="center"></el-table-column>
        <el-table-column label="类型" prop="holidayType" align="center"></el-table-column>
        <el-table-column label="时间" align="center">
          <template #default="scope">
            {{ scope.row.startDate + '~' + scope.row.endDate }}
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)" type="text" size="mini">修改配置名称</el-button>
            <el-button @click="handleDel(scope.row)" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-holiday-config-edit :isShow="state.isEdit" :everyDayList="state.everyDayList" :rowData="state.rowData"
      @close="closeEdit" />
  </div>
</template>
<script>
import { reactive, onMounted } from "vue";
import { attentionholidayList, attentionholidayDel } from "@/applications/eccard-ams/api";
import { formatEveryDay } from "@/utils/date.js"
import edit from "./components/edit.vue"
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElMessageBox,
  ElMessage
} from 'element-plus';

export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElTable,
    ElTableColumn,
    ElButton,
    ElForm,
    ElFormItem,
    ElDatePicker,
    'kade-holiday-config-edit': edit
  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      form: {},
      dataList: [],
      rowData: {},
      everyDayList: []
    });
    const getList = async () => {
      let params = {
        holidayYear: state.form.holidayYear ? new Date(state.form.holidayYear).getFullYear() : ""
      }
      let { data } = await attentionholidayList(params)

      state.everyDayList = data.reduce((prev, cur) => {
        let list = formatEveryDay(cur.startDate, cur.endDate).map(item => new Date(item).getTime())
        list = list.map(item => {
          return {
            timeStamp: item,
            type: cur.holidayType
          }
        })
        prev.push(...list)
        return prev
      }, [])
      state.everyDayList = state.everyDayList.reduce((prev, cur) => {
        if (!prev.includes(cur.timeStamp)) {
          prev.push(cur)
        }
        return prev
      }, [])
      state.dataList = data
    }
    const handleEdit = row => {
      state.rowData = row
      state.isEdit = true
    }
    //删除
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await attentionholidayDel(row.holidayId);
        if (code === 0) {
          ElMessage.success(message);
          getList()
        }
      });
    };
    const closeEdit = val => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }
    const search = () => {
      getList()
    }
    const reset = () => {
      state.form = {}
    }
    onMounted(async () => {
      getList()
    })
    return {
      state,
      handleEdit,
      closeEdit,
      handleDel,
      reset,
      search
    };
  },
};
</script>
<style lang="scss" scoped></style>
