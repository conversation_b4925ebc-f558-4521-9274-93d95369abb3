<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="人员编号">
          <el-input v-model="state.form.userCode" placeholder="请输入">
          </el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input v-model="state.form.userName" placeholder="请输入">
          </el-input>
        </el-form-item>
        <el-form-item label="复核日期">
          <el-date-picker v-model="state.requestDate" type="daterange"  range-separator="~" start-placeholder="请选择开始日期"
            end-placeholder="请选择结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="复核时段">
          <el-select clearable v-model="state.form.attendancePeriodName" placeholder="全部">
            <el-option v-for="(item, index) in state.checkPeriodIdList" :key="index" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="复核结果">
          <el-select clearable v-model="state.form.checkResult" placeholder="全部">
            <el-option v-for="(item, index) in resultList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="复核列表">
      <template #extra>
        <el-button icon="el-icon-plus" type="success" size="mini" @click="add()">新增</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="userCode" label="人员编号" align="center"></el-table-column>
        <el-table-column prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column prop="deptName" label="组织机构" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column show-overflow-tooltip prop="checkDate" label="复核日期" align="center"></el-table-column>
        <el-table-column prop="checkPeriodName" label="复核时段" align="center"></el-table-column>
        <el-table-column prop="checkResult" label="复核结果" align="center">
          <template #default="scope">
            {{dictionaryFilter(scope.row.checkResult)}}
          </template>
        </el-table-column>
        <el-table-column prop="checkPerson" label="复核人员" align="center"></el-table-column>
        <el-table-column prop="areaName" label="区域" align="center"></el-table-column>
        <el-table-column prop="roomString" label="房间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="80px">
          <template #default="scope">
            <el-button type="text" @click="edit(scope.row, scope.$index)" size="mini" class="green">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper"
          :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-check-edit :selectRow="state.selectRow" :dialogVisible="state.dialogVisible" @close="close"></kade-check-edit>
    <kade-check-add :dialogAdd="state.dialogAdd" @close="close"></kade-check-add>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElInput,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import {  dateStr } from "@/utils/date";
import deptSelectTree from '@/components/tree/deptSelectTree'
import edit from "./components/edit.vue"
import add from "./components/add.vue"
import { useDict } from "../../../../../hooks/useDict";
import { onMounted } from '@vue/runtime-core';
import linkageSelect from "../../../components/linkageSelect.vue";
import { manualCheckList, attendancePeriodList } from '@/applications/eccard-dorm/api.js'
const linkageData = {
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  building: { label: "楼栋", valueKey: "buildId" },
  unit: { label: "单元", valueKey: "unitNum" },
  floor: { label: "楼层", valueKey: "floorNum" },
};
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElDatePicker,
    ElInput,
    "kade-check-edit": edit,
    "kade-check-add": add,
    "kade-dept-select-tree": deptSelectTree,
    'kade-linkage-select': linkageSelect
  },


  setup() {
    const resultList = useDict('DORM_ATTENDANCE_RESULT')
    const state = reactive({
      form: {
        pageSize: 10,
        currentPage: 1
      },
      total: 0,
      requestDate: [],
      selectRow: "",
      rowIndex: 0,
      dialogVisible: false,
      dialogAdd: false,
      data: [],
      loading: false,
      checkPeriodIdList:''
    });
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const getCheckList = async()=>{
      let { data } = await attendancePeriodList()
      state.checkPeriodIdList=data
    }
    const getList = async () => {
      console.log(state.form)
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.data.beginDate
        delete state.data.endDate
      }
      state.loading = true
      try {
        let { data: { list, total } } = await manualCheckList(state.form)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = ""
      getList()
    };
    const edit = (row, index) => {
      console.log(row)
      state.rowIndex = index
      state.selectRow = row
      state.dialogVisible = true
    };
    const add = () => {
      state.dialogAdd = true
    };
    const close = (val) => {
      if (val) {
        getList()
      }
      state.dialogVisible = false
      state.dialogAdd=false
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    onMounted(() => {
      getList()
      getCheckList()
    })
    return {
      linkageData,
      resultList,
      state,
      edit,
      add,
      close,
      linkageChange,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-icon-time:before) {
  display: none;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 210px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
</style>