<template>
  <el-dialog :model-value="modelValue" title="批量修改人员卡类（已发卡用户不能修改卡类）" width="90%" v-loading.fullscreen.lock="state.loading"
    :before-close="beforeClose" :close-on-click-modal="false">
    <div style="color:#f00">
      
    </div>
    <kade-table-wrap title="选择人员" style="margin-top:20px">
      <el-divider></el-divider>
      <kade-select-table :isShow="modelValue" :value="[]" :reqFnc="getPersonList"
        :selectCondition="state.selectCondition" :column="column" :isCurrentSelect="true" :params="params"
        @change="personChange" />
    </kade-table-wrap>
    <kade-table-wrap title="选择卡类" style="margin-top:20px;margin-bottom:20px">
      <el-divider></el-divider>
      <el-form inline size="mini" label-width="120px">
        <el-form-item label="卡片类型:">
          <el-select :disabled="state.isDisabled" clearable v-model="state.form.acctType" placeholder="请选择">
            <el-option v-for="(item, index) in cardTypeList" :key="index" :label="item.ctName" :value="item.ctCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-wrap>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElDivider,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { watch } from "@vue/runtime-core";
import {
  getPersonList,
  batchPersonCardType
} from "@/applications/eccard-finance/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "userRoleName", isDict: false, width: "" },
  { label: "账户状态", prop: "acctStatus", isDict: true, width: "" },
  { label: "卡片类别", prop: "ctName", isDict: false, width: "" },
  { label: "卡片状态", prop: "cardStatus", isDict: true, width: "" },
];


const params = {
  currentPageKey: "currentPage",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
    acctStatus: "NORMAL_ACCOUNT",
    cardStatus: "SYS_NOT_CARD"
  },
  tagNameKey: "userName",
  valueKey: "userId",
};
export default {
  components: {
    ElDialog,
    ElDivider,
    ElForm,
    ElFormItem,
    ElButton,
    ElSelect,
    ElOption,
    "kade-select-table": selectTable,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    cardTypeList: {
      type: Array,
      default: null
    },
    roleList: {
      types: Array,
      default: null
    },
  },
  setup(props, context) {
    const state = reactive({
      selectCondition: [
        {
          label: "关键字",
          valueKey: "keyWord",
          placeholder: "姓名或编号关键字搜索",
          isSelect: false,
        },
        {
          label: "组织机构",
          valueKey: "deptPath",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRoleId",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: [],
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },

      ],

      form: {},
    });
    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          state.form = {
            userIds: []
          }
        }
      }
    );
    watch(
      () => props.roleList,
      (val) => {
        state.selectCondition[2].select.list = val;
      }
    );
    const personChange = (val) => {
      state.form.userIds = val.list.map((item) => item.userId);
    };

    const submit = async () => {
      if (!state.form.userIds.length) {
        return ElMessage.error("请选择需要修改卡类的人员！")
      }
      if (!state.form.acctType) {
        return ElMessage.error("请选择卡类！")
      }
      state.loading = true
      try {
        let { code, message } = await batchPersonCardType(state.form)
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const beforeClose = () => {
      context.emit("update:modelValue", false)
    }
    return {
      getPersonList,
      column,
      params,
      state,
      personChange,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}

.kade-table-wrap {
  padding-bottom: 0;
}
</style>