<template>
  <div class="padding-box">
    <kade-table-wrap title="房间类型列表" v-loading="state.loading">
      <template #extra>
        <el-button @click="handleEdit({},'add')" icon="el-icon-plus" size="small" class="btn-green">新建房间类型</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" @row-click="selectCurrentRow" ref="multipleTable" border stripe>
        <el-table-column show-overflow-tooltip label="类型名称" prop="roomTypeName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="床位数" prop="bedCount" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="价格" prop="price" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="面积" prop="space" align="center"></el-table-column>
        <el-table-column width="120px" show-overflow-tooltip label="独立卫生间" prop="aloneToilet" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.aloneToilet) }}
          </template>
        </el-table-column>
        <el-table-column width="120px" show-overflow-tooltip label="房间性质" prop="aloneToilet" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.roomNature) }}
          </template>
        </el-table-column>
        <el-table-column width="120px" show-overflow-tooltip label="房间用途" prop="aloneToilet" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.roomUse) }}
          </template>
        </el-table-column>
        <el-table-column width="150px" label="床位图" prop="merchantName" align="center">
          <template #default="scope">
            <el-image style="width: 60px; height: 60px" :src="scope.row.resourceUrl" :preview-src-list="[scope.row.resourceUrl]" :initial-index="0" fit="cover"></el-image>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="系统预置" prop="sysPreset" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.sysPreset) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="启用状态" prop="useStatus" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.useStatus) }}
          </template>
        </el-table-column>
        <el-table-column width="200px" label="操作" align="center">
          <template #default="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row,'edit')">编辑</el-button>
            <el-button size="mini" type="text" @click="handleEdit(scope.row,'details')">详情</el-button>
            <el-button size="mini" type="text" @click="handleDeploy(scope.row)">物品配置</el-button>
            <el-button v-if="scope.row.sysPreset=='FALSE'" size="mini" type="text" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100]" :total="state.total" @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-room-type-edit v-model:modelValue="state.isEdit" :editType="state.editType" :selectRow="state.selectRow" @update:modelValue="close" />
    <kade-room-deploy v-model:modelValue="state.isRoomDeploy" :selectRow="state.selectRow" @update:modelValue="close" />
  </div>

</template> 
<script>
import { ElButton, ElTable, ElTableColumn, ElPagination, ElImage,ElMessageBox,ElMessage } from "element-plus"
import { reactive } from '@vue/reactivity'
import { getRoomTypeList,roomTypeDelete } from "@/applications/eccard-dorm/api";

import edit from "./components/edit"
import roomDeploy from "./components/roomDeploy"
import { onMounted } from '@vue/runtime-core';
export default {
  components: {
    ElButton, ElTable, ElTableColumn, ElPagination, ElImage,
    'kade-room-type-edit': edit,
    'kade-room-deploy': roomDeploy,

  },
  setup() {
    const state = reactive({
      loading: false,
      isEdit: false,
      isRoomDeploy: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getRoomTypeList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }
    const handleEdit = (row, type) => {
      state.editType = type
      state.selectRow = row
      state.isEdit = true
    }
    const handleDeploy = (row) => {
      state.selectRow = row
      state.isRoomDeploy = true
    }
    const handleDel = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await roomTypeDelete(row.roomTypeId);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()

    };
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
      state.isRoomDeploy = false
    }
    onMounted(() => {
      getList()
    })
    return {
      state,
      handleEdit,
      handleDeploy,
      handleDel,
      handleSizeChange,
      handleCurrentChange,
      close
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 8px;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}
:deep(.el-table--border) {
  border: 1px solid #eeeeee;
}
</style>