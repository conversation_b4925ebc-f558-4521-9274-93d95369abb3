<template>
  <div class="box-dialog">
    <kade-table-wrap title="人员基本信息">
      <el-divider></el-divider>
      <el-form inline size="small" label-width="100px">
          <el-form-item label="用户编号:">
            <el-input disabled :modelValue="personDetail.userCode"></el-input>
          </el-form-item>
          <el-form-item label="姓名:">
            <el-input disabled :modelValue="personDetail.userName"></el-input>
          </el-form-item>
          <el-form-item label="组织机构:">
            <el-input disabled :modelValue="personDetail.deptName"></el-input>
          </el-form-item>
          <el-form-item label="身份类别:">
            <el-input disabled :modelValue="personDetail.userRoleName"></el-input>
          </el-form-item>
          <el-form-item label="账户状态:">
            <el-input disabled :modelValue="filterDictionary(personDetail.acctStatus,state.accountStatusList)"></el-input>
          </el-form-item>
          <el-form-item label="卡片类别:">
            <el-input disabled :modelValue="personDetail.acctType"></el-input>
          </el-form-item>
          <el-form-item label="卡片状态:">
            <el-input disabled :modelValue="filterDictionary(personDetail.cardStatus, state.cardStatusList)"></el-input>
          </el-form-item>
          <el-form-item label="物理卡号:">
            <el-input disabled :modelValue="personDetail.cardNo"></el-input>
          </el-form-item>
          <el-form-item label="联系方式:">
            <el-input disabled :modelValue="personDetail.userTel"></el-input>
          </el-form-item>
          <el-form-item label="开户人员:">
            <el-input disabled :modelValue="personDetail.createUser"></el-input>
          </el-form-item>
          <el-form-item label="开户时间:">
            <el-input disabled :modelValue="personDetail.openAccountTime &&timeStrDate(personDetail.openAccountTime)"></el-input>
          </el-form-item>
      </el-form>
    </kade-table-wrap>
    <kade-table-wrap title="账户信息" style="margin-top: 10px">
      
      <el-table style="width: 100%" :data="personDetail.acctWallets" v-loading="false" border stripe>
        <el-table-column label="钱包名称" prop="walletName" align="center"></el-table-column>
        <el-table-column label="钱包类型" prop="walletType" align="center">
          <template #default="scope">
            {{ filterDictionary(scope.row.walletType, state.walletTypeList) }}
          </template>
        </el-table-column>
        <el-table-column label="钱包状态" prop="walletStatus" align="center">
          <template #default="scope">
            <div class="green" v-if="filterDictionary(scope.row.walletStatus, state.walletStatusList)=='正常'">
              {{ filterDictionary(scope.row.walletStatus, state.walletStatusList) }}
            </div>
            <div class="red" v-if="filterDictionary(scope.row.walletStatus, state.walletStatusList)=='冻结'">
              {{ filterDictionary(scope.row.walletStatus, state.walletStatusList) }}
            </div>
            <div class="blue" v-if="filterDictionary(scope.row.walletStatus, state.walletStatusList)=='未激活'">
              {{ filterDictionary(scope.row.walletStatus, state.walletStatusList) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="有效期至" width="300" align="center">
          <template #default="scope">
            {{
              scope.row.walletValidityDate &&
              timeStrDate(scope.row.walletValidityDate)&&scope.row.walletStatus!=='WALLET_NOT_ACTIVE'?scope.row.walletValidityDate &&
              timeStrDate(scope.row.walletValidityDate):'未激活'
            }}
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">关&nbsp;&nbsp;闭</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElDivider,
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElButton,
  ElInput,
} from "element-plus";
import { dateStr } from "@/utils/date.js";
// import { reactive } from "@vue/reactivity";
import { computed, reactive } from "@vue/runtime-core";

export default {
  components: {
    ElDivider,
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
  },
  props: {
    personDetail: {
      types: Object,
      default: {},
    },
  },
  setup(props, context) {
    const state = reactive({
      walletTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_TYPE"), //钱包类型
      walletStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_STATUS"), //钱包状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const timeStrDate = computed(() => {
      return dateStr;
    });
    const off = () => {
      context.emit("off", false);
    };
    return {
      state,
      timeStrDate,
      off,
      filterDictionary,
    };
  },
};
</script>
<style lang="scss" scoped>
.box-dialog {
  padding: 10px 0;

  .el-table__row {
    height: 30px !important;
  }

  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}

.table-box {
  padding: 10px;
}

.submit-btn {
  text-align: center;
  margin: 10px auto;
}
</style>
