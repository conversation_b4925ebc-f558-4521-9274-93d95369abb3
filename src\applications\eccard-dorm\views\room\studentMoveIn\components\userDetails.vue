<template>
  <el-dialog :model-value="isShow" title="人员详细信息" width="1500px" :before-close="beforeClose" :close-on-click-modal="false">
    <div class="flex-box">
      <div class="upload-photo">
        <div style="margin-bottom:20px">照片</div>
        <el-image class="img" :src="state.form.userPhoto" fit="contain">
          <template #error>
            <div class="image-slot">
              <i class="el-icon-picture"></i>
            </div>
          </template>
        </el-image>
      </div>
      <el-form ref="formRef" :model="state.form" :rules="rules" label-width="120px" style="flex:1" size="small">
        <el-row>
          <el-col :span="6">
            <el-form-item label="人员编号：" prop="userCode">
              <el-input :model-value="state.form.userCode" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="人员姓名：" prop="userName">
              <el-input :model-value="state.form.userName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="性别：" prop="userSex">
              <el-input :model-value="dictionaryFilter(state.form.userSex)" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="民族：">
              <el-input :model-value="state.form.userNationText" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="籍贯：">
              <el-input :model-value="state.form.userNativePlace" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="出生地：">
              <el-input :model-value="state.form.userBirthPlaceText" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="出生日期：">
              <el-input :model-value="state.form.userBirthday" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="政治面貌：">
              <el-input :model-value="dictionaryFilter(state.form.userPoliticalFace)" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="证件类型：">
              <el-input :model-value="dictionaryFilter(state.form.userIdType)" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="证件号码：">
              <el-input :model-value="state.form.userIdNo" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号码：">
              <el-input :model-value="state.form.userTel" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="邮箱：">
              <el-input :model-value="state.form.mailAddress" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="户籍地址：">
              <el-row :gutter="20">
                <el-col :span="4">
                  <el-input :model-value="state.addressData.householdRegisterProvinceStr" readonly></el-input>
                </el-col>
                <el-col :span="4">
                  <el-input :model-value="state.addressData.householdRegisterCityStr" readonly></el-input>
                </el-col>
                <el-col :span="4">
                  <el-input :model-value="state.addressData.householdRegisterAreaStr" readonly></el-input>
                </el-col>
                <el-col :span="12">
                  <el-input :model-value="state.addressData.householdRegisterAddress" readonly></el-input>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="组织机构：">
              <el-input :model-value="state.form.deptName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="岗位：">
              <el-input :model-value="state.form.postName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份类别：" prop="userRole">
              <el-input :model-value="state.form.roleName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否住校：">
              <el-input :model-value="dictionaryFilter(state.form.userIsBoarders)" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="人员状态：" prop="userState" clearable>
              <el-input :model-value="state.form.userStateName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="入校时间：">
              <el-input :model-value="state.form.userSchoolTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="离校时间：">
              <el-input :model-value="state.form.userLeaveTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交易密码：">
              <el-input :model-value="state.form.userTransPwd" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="家庭住址：">
              <el-row :gutter="20">
                <el-col :span="4">
                  <el-input :model-value="state.addressData.familyProvinceStr" readonly></el-input>
                </el-col>
                <el-col :span="4">
                  <el-input :model-value="state.addressData.familyCityStr" readonly></el-input>
                </el-col>
                <el-col :span="4">
                  <el-input :model-value="state.addressData.familyAreaStr" readonly></el-input>
                </el-col>
                <el-col :span="12">
                  <el-input :model-value="state.addressData.familyAddress" readonly></el-input>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：">
              <el-input :model-value="state.form.userRemark" type="textarea" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否启用：">
              <el-switch v-model="state.form.userAccountState" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElImage,
  ElInput,
  ElSwitch,
} from "element-plus";
import { reactive, watch, ref, nextTick, onMounted } from "vue";
import {
  getBaseUserInfo, nationList, getAddressList, stationList, getUserAddressInfo
} from "@/applications/eccard-basic-data/api";
export default {
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElImage,
    ElInput,
    ElSwitch,
  },
  props: {
    isShow: Boolean,
    rowData: Object
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      loading: false,
      form: {},
      nationList: [],
      provinceList: [],
      stationList: [],
      addressData: {}
    });
    watch(
      () => props.isShow,
      (val) => {
        if (val) {
          getDetails()
          getUserAddress();
        }
        nextTick(() => {
          formRef.value.clearValidate();
        });
      }
    );
    const getDetails = async () => {
      let params = {
        userCode: props.rowData.userCode,
        userId: props.rowData.userId,
      };
      let { data, code } = await getBaseUserInfo(params);
      if (code === 0) {

        state.form = data;
        state.form.userNationText = state.nationList.find(item => item.code == state.form.userNation)?.name
        state.form.userBirthPlaceText = state.provinceList.find(item => item.code == state.form.userBirthPlace)?.name
        state.form.postName = state.stationList.find(item => item.id == state.form.userPostId)?.postName
      }
    };

    const getUserAddress = async () => {
      let { data } = await getUserAddressInfo(props.rowData.userId);
      if (data) {
        state.addressData = data;
        data.householdRegisterProvince &&
          getCityList(data.householdRegisterProvince, "house");
        data.householdRegisterCity &&
          getAreaList(data.householdRegisterCity, "house");
        data.familyProvince && getCityList(data.familyProvince, "family");
        data.familyCity && getAreaList(data.familyCity, "family");
      } else {
        state.addressData = {};
      }
    };


    const getNationList = async () => {
      let { data } = await nationList();
      state.nationList = data;
    };
    const getProvinceList = async () => {
      let { data } = await getAddressList({ level: 1 });
      state.provinceList = data;
    };

    const getCityList = async (val, type) => {
      if (!val) return;
      let { data } = await getAddressList({ parentCode: val, level: 2 });
      if (type === "house") {
        state.householdCityList = data;
      } else {
        state.familyCityList = data;
      }
    };
    const getAreaList = async (val, type) => {
      if (!val) return;
      let { data } = await getAddressList({ parentCode: val, level: 3 });
      if (type === "house") {
        state.householdAreaList = data;
      } else {
        state.familyAreaList = data;
      }
    };
    const getStationList = async () => {
      let { data } = await stationList();
      state.stationList = data;
    };
    const beforeClose = () => {
      context.emit("close", false)
    }
    onMounted(() => {
      getNationList()
      getProvinceList()
      getStationList()
    })
    return {
      formRef,
      state,
      beforeClose
    };
  },
};
</script>
<style lang="scss" scoped>
.flex-box {
  margin-top: 20px;
  display: flex;

  .upload-photo {
    width: 160px;
    margin-right: 20px;

    .img {
      width: 100%;
      height: 224px;
      background: #fff;
    }
  }
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  color: #8a8a8a;
  font-size: 30px;
}

.image-slot .el-icon {
  font-size: 30px;
}
</style>
