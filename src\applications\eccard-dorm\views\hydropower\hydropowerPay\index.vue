<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form label-width="90px" size="mini" inline>
        <!--         <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange"></kade-linkage-select>
        <el-form-item label="缴费类型">
          <el-select v-model="state.form.hydropowerPayType" placeholder="全部">
            <el-option v-for="(item, index) in hydropowerPayTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="关键字">
          <el-input clearable v-model="state.form.keyWord" placeholder="请输入人员姓名或编号查询">
          </el-input>
        </el-form-item>
        <el-form-item label="缴费方式">
          <el-select v-model="state.form.payType" placeholder="全部" clearable>
            <el-option v-for="(item, index) in payTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="缴费时间">
          <el-date-picker v-model="state.requestDate" :defaultTime="defaultTime" type="datetimerange" range-separator="~"
            start-placeholder="请选择" end-placeholder="请选择">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <div class="total-amount">
      <div class="water-charge">
        水费缴存总额:&nbsp;&nbsp;{{ state.totalList.totalWaterBalance ? state.totalList.totalWaterBalance : 0 }}元</div>
      <div>
        电费缴存总额:&nbsp;&nbsp;{{ state.totalList.totalElectricBalance ? state.totalList.totalElectricBalance : 0
        }}&nbsp;&nbsp;元
      </div>
    </div>
    <kade-table-wrap title="记录列表" v-loading="state.loading">
      <el-table :data="state.dataList" border>
        <el-table-column v-for="(item, index) in column" :key="index" :width="item.width" :prop="item.prop"
          :label="item.label" align="center" show-overflow-tooltip>
          <template #default="scope" v-if="item.isDict">
            {{ dictionaryFilter(scope.row[item.prop]) }}
          </template>
          <template #default="scope" v-else-if="item.isDate">
            {{timeFilter(scope.row[item.prop])}}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100, 200]" :total="state.total" :page-size="state.form.pageSize"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from '@vue/reactivity';
import { requestDate } from "@/utils/reqDefaultDate.js";
import { timeStr } from "@/utils/date.js";
import { useDict } from "@/hooks/useDict.js"
import { onMounted } from '@vue/runtime-core';
import { getHydropowerPay, getPayTotal } from '@/applications/eccard-dorm/api.js'
// import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect";
const linkageData = {
  area: { label: '区域', valueKey: 'areaId', key: 'id' },
  buildingType: { label: '楼栋类型', valueKey: 'buildType' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  floor: { label: '楼层', valueKey: 'floorNum' },
  room: { label: '房间', valueKey: 'roomId' }
}
const column = [
  /*   { label: "区域", prop: "areaName" },
    { label: "房间", prop: "roomNo" }, */
  { label: "订单号", prop: "orderNo" },
  { label: "缴费类型", prop: "hydropowerPayType", isDict: true },
  { label: "缴费人编号", prop: "userCode" },
  { label: "缴费人姓名", prop: "userName" },
  { label: "缴费时间", prop: "tradeDate", isDate: true },
  { label: "缴费金额", prop: "tradeAmount" },
  { label: "缴费方式", prop: "payType" },
  { label: "同步状态", prop: "syncResult" },
  // { label: "缴费前余额", prop: "tradeBeforeBalance" },
  // { label: "缴费后余额", prop: "tradeAfterBalance" },
]
const defaultTime = [
  new Date(new Date(new Date().toLocaleDateString()).getTime()),
  new Date(
    new Date(new Date().toLocaleDateString()).getTime() +
    24 * 60 * 60 * 1000 -
    1
  )
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    // "kade-linkage-select": linkageSelect
  },
  setup() {
    const payTypeList = [
      { label: "微信支付", value: "微信支付" },
      { label: "现金钱包转入", value: "现金钱包转入" },
      { label: "补助钱包转入", value: "补助钱包转入" },
    ]
    const hydropowerPayTypeList = useDict('DORM_HYDROPOWER_PAY_TYPE')
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      dataList: [],
      totalList: {},
      total: 0,
      requestDate: requestDate()
    });
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.startTime = timeStr(state.requestDate[0])
        state.form.endTime = timeStr(state.requestDate[1])

      } else {
        delete state.form.startTime
        delete state.form.endtTime
      }
      state.loading = true
      let { data: { list, total } } = await getHydropowerPay(state.form)
      state.dataList = list
      state.total = total
      queryPay()
      state.loading = false
    };
    //获取水电费余额汇总
    const queryPay = async () => {
      let { data, code } = await getPayTotal(state.form)
      if (code === 0) {
        state.totalList = data
      }
    }
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.requestDate = requestDate()
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val
      getList()
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    onMounted(() => {
      getList()
    })
    return {
      column,
      state,
      queryPay,
      payTypeList,
      hydropowerPayTypeList,
      linkageData,
      defaultTime,
      handleSearch,
      handleReset,
      linkageChange,
      handlePageChange,
      handleSizeChange
    }
  }
};
</script>
<style lang="scss" scoped>
.total-amount {
  display: flex;
  align-items: center;
  padding: 0px 0 15px 10px;

  .water-charge {
    margin-right: 100px;
  }
}

:deep(.el-form-item__content) {
  .el-input__inner {
    width: 182px;
  }

  .el-date-editor {
    width: 400px;
  }
}
</style>