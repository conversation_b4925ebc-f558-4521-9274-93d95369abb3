<template>
  <!-- 线上充值对账明细 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="账单类型:">
          <el-select clearable v-model="state.form.billType" placeholder="请选择">
            <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="对账结果:">
          <el-select clearable v-model="state.form.billResult" placeholder="请选择">
            <el-option v-for="(item, index) in rechargeReconciliationList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账单日期:">
          <el-date-picker v-model="state.requestDate" type="daterange" unlink-panels range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="线上充值对账明细表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" v-loading="state.loading" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template #header v-if="item.tip">
            <el-tooltip class="item" effect="dark" :content="item.tip" placement="top-start">
              <div>{{ item.label }}<i class="el-icon-question"></i></div>
            </el-tooltip>
          </template>
          <template v-if="item.render" #default="scope">
            {{item.render(scope.row[item.prop],rechargeReconciliationList)}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" @click="detailsClick(scope.row)">查看订单明细</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-online-charge-details :rowData="state.rowData" :dialogVisible="state.dialogVisible" @close="state.dialogVisible = false" />
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElTooltip,
  ElMessage,
} from "element-plus";
import { downloadXlsx } from "@/utils"
import { timeStr, dateStr } from "@/utils/date.js";
import { filterDictionary } from "@/utils";
import { reactive } from "@vue/reactivity";
import { requestDate } from "@/utils/reqDefaultDate";
import {
  getPaymentReconcileList,
  paymentReconcileExport,
  tradeMode,
} from "@/applications/eccard-finance/api";
import { onMounted, } from "@vue/runtime-core";
// import { useDict } from "@/hooks/useDict"
import details from "./components/details.vue";

const column = [
  { label: "账单日期", tip: "交易发生的日期", prop: "billDate", width: "170", render: (val) => val && dateStr(val) },
  { label: "账单类型", tip: "", prop: "billTypeName", width: "" },
  { label: "账单总金额", tip: "支付平台交易的总金额", prop: "billTotalAmount", width: "" },
  { label: "系统总金额", tip: "充值后一卡通到账的总金额", prop: "sysTotalAmount", width: "" },
  // { label: "账单充值金额", prop: "billPaymentAmount", width: "" },
  // { label: "系统充值金额", prop: "sysPaymentAmount", width: "" },
  { label: "账单退款金额", tip: "账单日期内产生的支付退款金额", prop: "billRefundAmount", width: "" },
  { label: "系统退款金额", tip: "针对账单日期内充值记录产生的退款金额", prop: "sysRefundAmount", width: "" },
  { label: "对账时间", tip: "", prop: "createTime", width: "170", render: (val) => val && timeStr(val) },
  { label: "对账结果", tip: "", prop: "billResult", render: (val, list) => filterDictionary(val, list) },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElTooltip,
    "kade-online-charge-details": details,
  },
  setup() {
    // const rechargeReconciliationList = useDict("RECHARGE_RECONCILIATION_STATUS").filter((item) => item.label != "未对账")
    const rechargeReconciliationList = [
      { label: "对账正常", value: "1" },
      { label: "对账异常", value: "0" }
    ]
    const state = reactive({
      loading: false,
      column,
      dialogVisible: false,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      rowData: {},
      requestDate: requestDate(),
      tradeModeList: [],
    });
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode({ costType: 101 }).then((res) => {
        state.tradeModeList = res.data.filter(
          (item) =>
            item.code == 2 ||
            item.code == 3 ||
            item.code == 4 ||
            item.code == 5 ||
            item.code == 6
        );
      });
    };
    const getParams = () => {
      let params = { ...state.form }
      for (let key in params) {
        if (!params[key]) {
          delete params[key];
        }
      }
      if (state.requestDate && state.requestDate.length) {
        params.startDate = dateStr(state.requestDate[0]) + " 00:00:00";
        params.endDate = dateStr(state.requestDate[1]) + " 23:59:59";
      } else {
        ElMessage.error("请选择账单日期")
        return false
      }
      return params
    }
    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data } = await getPaymentReconcileList(params);
        if (code === 0) {
          let {
            pageInfo: { total, list },
          } = data;
          state.detailList = list;
          state.total = total;

        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const detailsClick = (row) => {
      state.rowData = row;
      state.dialogVisible = true;
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await paymentReconcileExport(params);
        downloadXlsx(res, '线上充值对账明细表.xlsx')
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
      };
      state.requestDate = requestDate()
    };
    onMounted(() => {
      // getList();
      getTradeModeList();
    });
    return {
      rechargeReconciliationList,
      column,
      state,
      timeStr,
      filterDictionary,
      exportClick,
      getList,
      detailsClick,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}
:deep(.el-dialog__headerbtn) {
  font-size: 20px;
  top: 10px;
  right: 10px;
}
:deep(.el-dialog__header) {
  padding: 10px 20px;
}
:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
}
:deep(.el-dialog__footer) {
  text-align: center;
}
:deep(.el-dialog__title) {
  font-size: 14px;
}
</style>