<template>
  <div class="person-msg-list">
    <kade-table-filter @search="queryPersonList()" @reset="reset()">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="关键字:">
          <el-input placeholder="姓名或编号关键字搜索" v-model="state.form.keyWord"></el-input>
        </el-form-item>
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="身份类别:">
          <el-select clearable v-model="state.form.userRoleId" placeholder="请选择">
            <el-option v-for="(item, index) in state.roleList" :key="index" :label="item.roleName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账户状态:">
          <el-select clearable v-model="state.form.acctStatus" placeholder="请选择">
            <el-option v-for="(item, index) in state.accountStatusList" :key="index" :label="item.dictValue"
              :value="item.dictCode"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="人员信息列表"> 
      <template #extra>
        <el-button @click="dialogBtn('openAccount')" icon="el-icon-plus" size="small" class="btn-yellow">开户</el-button>
        <el-button @click="state.isOpenAllAccount = true; queryWalletActiveList(); queryAccountStrategyList();"
          icon="el-icon-plus" size="small" class="btn-purple">批量开户</el-button>
        <el-button @click="dialogBtn('activeWallet')" size="small" icon="el-icon-money" class="btn-green">钱包激活
        </el-button>
        <el-button @click="state.isActiveAllWallet = true" icon="el-icon-money" size="small" class="btn-blue">批量钱包激活
        </el-button>
        <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
        <el-button @click="state.isEditCardType = true" icon="el-icon-money" size="small" class="btn-purple">批量修改卡类
        </el-button>
      </template>
      <el-table :data="state.personList" @row-click="selectCurrentRow" ref="multipleTable"
        v-loading="state.getPersonLoading" highlight-current-row border stripe>
        <el-table-column show-overflow-tooltip width="150" label="用户编号" prop="userCode" align="center">
        </el-table-column>
        <el-table-column show-overflow-tooltip width="153" prop="userName" label="姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column label="账户状态" align="center">
          <template #default="scope">
            <div v-if="scope.row.dictValue == '正常'">
              <el-switch inline-prompt :model-value="true" border-color="#ffffff"></el-switch>
            </div>
            <div v-else-if="scope.row.dictValue == '冻结'">
              <el-switch inline-prompt :model-value="false" inactive-color="#ff4949"></el-switch>
            </div>
            <div v-else>{{ scope.row.dictValue }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="卡片状态" align="center">
          <template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="物理卡号" prop="cardNo" align="center" width="120"></el-table-column>
        <el-table-column show-overflow-tooltip label="联系方式" prop="userTel" align="center" width="120"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="listDetails(scope.row, 'see')" size="mini">
              查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>


    <el-dialog :model-value="state.isOpenAccount" title="开户" width="60%" :before-close="offOpenAccount"
      :close-on-click-modal="false">
      <kade-open-account :cardTypeList="state.cardTypeList" :accountStrategyList="state.accountStrategyList"
        :selectPerson="state.selectPerson" :allWalletList="state.allWalletList" :isOpenAccount="state.isOpenAccount"
        @off="state.isOpenAccount = false" @success="
  queryPersonList();
state.isOpenAccount = false;
        " />
    </el-dialog>
    <el-dialog :model-value="state.isOpenAllAccount" title="批量开户" width="90%" :before-close="offOpenAllAccount"
      :close-on-click-modal="false">
      <kade-open-all-account :departCheckList="state.departCheckList" :drovinceCategoryList="state.drovinceCategoryList"
        :accountStrategyList="state.accountStrategyList" :cardTypeList="state.cardTypeList" :roleList="state.roleList"
        :isOpenAllAccount="state.isOpenAllAccount" :allWalletList="state.allWalletList"
        @off="state.isOpenAllAccount = false" @success="
  queryPersonList();
state.isOpenAllAccount = false;
        " />
    </el-dialog>
    <el-dialog :model-value="state.isActiveWallet" title="钱包激活" width="60%" :before-close="offActiveWallet"
      :close-on-click-modal="false">
      <kade-active-wallet :cardTypeList="state.cardTypeList" :accountStrategyList="state.accountStrategyList"
        :selectPerson="state.selectPerson" :walletList="state.walletList" :allWalletList="state.allWalletList"
        @off="state.isActiveWallet = false" @success="
  queryPersonList();
state.isActiveWallet = false;
        " />
    </el-dialog>
    <el-dialog :model-value="state.isActiveAllWallet" title="批量钱包激活" width="90%" :before-close="offActiveAllWallet"
      :close-on-click-modal="false">
      <kade-active-all-wallet :departCheckList="state.departCheckList"
        :drovinceCategoryList="state.drovinceCategoryList" :accountStrategyList="state.accountStrategyList"
        :cardTypeList="state.cardTypeList" :roleList="state.roleList" :isActiveAllWallet="state.isActiveAllWallet"
        @off="state.isActiveAllWallet = false" @success="
  queryPersonList();
state.isActiveAllWallet = false;
        " />
    </el-dialog>
    <el-dialog :model-value="state.isPersonDetail" title="人员信息详情" width="60%" :before-close="offPersonDetail"
      :close-on-click-modal="false">
      <kade-person-details :personDetail="state.personDetail" @off="state.isPersonDetail = false" />
    </el-dialog>
    <kade-edit-card-type v-model="state.isEditCardType" :cardTypeList="state.cardTypeList" :roleList="state.roleList" @update:modelValue="cardClose" />
  </div>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElPagination,
  ElMessage,
  ElMessageBox,
  ElLoading
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { reactive, ref } from "@vue/reactivity";
import openAccount from "./components/openAccount.vue";
import openAllAccount from "./components/openAllAccount.vue";
import activeWallet from "./components/activeWallet.vue";
import activeAllWallet from "./components/activeAllWallet.vue";
import personDetails from "../../components/personDetails.vue";
import editCardType from "./components/editCardType.vue";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
import {
  getPersonList,
  getAccountStrategyList,
  getCardTypeList,
  getPersonAccountInfo,
  getWalletActiveList,
  getRolelist,
  exportPersonList
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
import {  downloadXlsx } from "@/utils/index.js";
import { useStore } from "vuex";
import { requestDate, } from "@/utils/reqDefaultDate.js";
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "el-switch": ElSwitch,
    ElDialog,
    "kade-open-account": openAccount,
    "kade-open-all-account": openAllAccount,
    "kade-active-wallet": activeWallet,
    "kade-active-all-wallet": activeAllWallet,
    "kade-person-details": personDetails,
    "kade-dept-select-tree": deptSelectTree,
    "kade-edit-card-type": editCardType
  },

  setup() {
    const store = useStore();
    const multipleTable = ref(null);
    const state = reactive({
      isOpenAccount: false,
      isOpenAllAccount: false,
      isActiveWallet: false,
      isActiveAllWallet: false,
      isPersonDetail: false,
      isEditCardType: false,
      total: 0,
      form: {
        currentPage: 1,
        pageSize: 6,
        acctStatus: null,
        deptPath: null,
        keyWord: null,
        userRoleId: null,
      },
      personList: [],
      getPersonLoading: false,
      departCheckList: [], //组织机构列表
      roleList: [], //身份类别列表
      accountStatusList: [], //账户状态列表
      accountStrategyList: [], //开户策略列表
      cardTypeList: [], //卡片类型列表
      selectPerson: "",
      personDetail: {},
      walletList: [], //个人钱包列表
      allWalletList: [],
      personTradeForm: {
        beginDate: timeStr(
          new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 7)
        ),
        endDate: timeStr(new Date()),
        currentPage: 1,
        pageSize: 6,
        userId: null,
      },
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    //获取人员信息列表
    const queryPersonList = () => {
      state.getPersonLoading = true;
      console.log(1);
      store.commit("data/updateState", {
        key: "selectPerson",
        payload: "",
      });
      console.log(store.state.data.selectPerson);
      store.commit("data/clearUserData")
      getPersonList(state.form)
        .then((res) => {
          state.personList = res.data.list;
          console.log(state.accountStatusList);
          state.personList.forEach((item) => {
            state.accountStatusList.forEach((val) => {
              if (item.acctStatus == val.dictCode) {
                item.dictValue = val.dictValue;
              }
            });
          });
          state.total = res.data.total;
          state.getPersonLoading = false;
        })
        .catch(() => {
          state.getPersonLoading = false;
        });
    };
    //获取账户状态列表
    const queryDictionaryList = () => {
      state.accountStatusList = JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => {
        return item.dictType == "PERSON_ACCOUNT_STATUS";
      });
    };
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        state.roleList = res.data;
      });
    };

    //获取开户策略
    const queryAccountStrategyList = () => {
      getAccountStrategyList().then((res) => {
        state.accountStrategyList = res.data;
      });
    };

    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };
    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data.map((item) => {
          return {
            walletCode: item.walletCode,
            walletName: item.walletName,
            walletStatus: "WALLET_NOT_ACTIVE",
            walletType: item.walletType,
            walletValidityDate: "",
            walletValidityDateNum: 5,
          };
        });
      });
    };

    const cascaderChange = (val) => {
      if (val) {
        state.form.deptPath = val.length && val[val.length - 1];
      } else {
        state.form.deptPath = "";
      }
    };
    const dialogBtn = (val) => {
      if (store.state.data.selectPerson) {
        if (val == "openAccount") {
          if (store.state.data.selectPerson.acctStatus == "NO_ACCOUNT") {
            queryAccountStrategyList();
            queryWalletActiveList();
            state.isOpenAccount = true;
          } else {
            ElMessage.error("当前选中用户已开户!");
          }
        } else if (val == "activeWallet") {
          if (!(store.state.data.selectPerson.acctStatus == "NO_ACCOUNT")) {
            getPersonAccountInfo({ userId: state.selectPerson.userId }).then(
              (res) => {
                state.walletList = res.data.acctWallets.map((item) => {
                  return {
                    ...item,
                    walletValidityDateNum:
                      item.walletStatus == "WALLET_NOT_ACTIVE" ? 5 : "",
                  };
                });
                getWalletActiveList().then((res) => {
                  state.allWalletList = res.data.map((item) => {
                    return {
                      walletCode: item.walletCode,
                      walletName: item.walletName,
                      walletStatus: "WALLET_NOT_ACTIVE",
                      walletType: item.walletType,
                      walletValidityDate: "",
                      walletValidityDateNum: 5,
                    };
                  });
                  for (let i = 0; i < state.allWalletList.length; i++) {
                    for (let j = 0; j < state.walletList.length; j++) {
                      if (
                        state.allWalletList[i].walletCode ==
                        state.walletList[j].walletCode
                      ) {
                        state.allWalletList[i] = state.walletList[j];
                      }
                    }
                  }
                  state.walletList = state.allWalletList.map((item) => {
                    return {
                      walletCode: item.walletCode,
                      walletName: item.walletName,
                      walletStatus: item.walletStatus,
                      walletType: item.walletType,
                      walletValidityDate: item.walletValidityDate,
                      walletValidityDateNum: item.walletValidityDateNum,
                    };
                  });

                  state.isActiveWallet = true;
                });
              }
            );
          } else {
            ElMessage.error("当前选中用户未开户!");
          }
        }
      } else {
        ElMessage.error("请选择人员！");
      }
    };

    const listDetails = (val, type) => {
      if (type == "see") {
        getPersonAccountInfo({ userId: val.userId }).then((res) => {
          state.personDetail = res.data;
          getWalletActiveList().then((res) => {
            let data = res.data.map((item) => {
              return { ...item, walletStatus: "WALLET_NOT_ACTIVE" };
            });
            for (let i = 0; i < data.length; i++) {
              for (let j = 0; j < state.personDetail.acctWallets.length; j++) {
                if (
                  data[i].walletCode ==
                  state.personDetail.acctWallets[j].walletCode
                ) {
                  data[i] = state.personDetail.acctWallets[j];
                }
              }
            }
            console.log(data);
            state.personDetail.acctWallets = data;
            state.isPersonDetail = true;
          });
        });
      }
    };

    const selectCurrentRow = (val) => {
      state.selectPerson = val;
      let activeItem = store.state.data.activeItem;
      store.commit("data/updateState", { key: "selectPerson", payload: val });
      if (activeItem === 0) {
        store.commit("data/updateState", { key: "allFrame", payload: true });
        store.dispatch("data/queryPersonWalletInfo", {
          userId: store.state.data.selectPerson.userId,
        });
      } else if (activeItem === 1) {
        store.dispatch("data/queryPersonTradeList");
      } else if (activeItem === 2) {
        let data2 = {
          beginDate: requestDate()[0],
          endDate: requestDate()[1],
          currentPage: 1,
          pageSize: 6,
        };
        store.dispatch("data/queryPersonCardInfo", {
          data1: { userId: val.userId },
          data2: data2,
        });
      } else if (activeItem === 3) {
        let data = {
          beginDate: requestDate()[0],
          endDate: requestDate()[1],
          userId: store.state.data.selectPerson.userId,
        };
        store.dispatch("data/queryPersonWalletTradeSum", data);
      } else if (activeItem === 4) {
        store.dispatch("data/queryPersonFamilyList", {
          userId: store.state.data.selectPerson.userId,
        });
      }
    };

    const exportClick = () => {
      ElMessageBox.confirm(
        '本次导出只能导出5000条数据，建议对数据进行筛选后再进行导出。确认导出?',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          let loaading = ElLoading.service({
            text: "正在导出..."
          })
          try {
            let res = await exportPersonList(state.form)
            downloadXlsx(res, "人员列表.xlsx")
            loaading.close()
          }
          catch{
            loaading.close()
          }
        })
    }


    const handlePageChange = (val) => {
      state.form.currentPage = val;
      queryPersonList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      queryPersonList();
    };
    //重置
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 6,
        acctStatus: "",
        deptPath: "",
        keyWord: "",
        userRoleId: "",
      };
    };
    //关闭模态框
    const offOpenAccount = () => {
      state.isOpenAccount = false;
    };
    const offOpenAllAccount = () => {
      state.isOpenAllAccount = false;
    };
    const offActiveWallet = () => {
      state.isActiveWallet = false;
    };
    const offActiveAllWallet = () => {
      state.isActiveAllWallet = false;
    };
    const offPersonDetail = () => {
      state.isPersonDetail = false;
    };
    const cardClose=(val)=>{
      if(val){
        queryPersonList()
      }
      state.isEditCardType=false
    }
    onMounted(() => {
      // queryPersonList();
      queryDictionaryList();
      queryCardTypeList();
      queryRolelist();
      console.log(multipleTable.value.setCurrentRow);
      multipleTable.value.setCurrentRow(store.state.data.selectPerson);
      console.log(1);
    });
    return {
      state,
      cascaderChange,
      multipleTable,
      filterDictionary,
      queryPersonList,
      handlePageChange,
      handleSizeChange,
      reset,
      offOpenAccount,
      offOpenAllAccount,
      offActiveWallet,
      offActiveAllWallet,
      selectCurrentRow,
      offPersonDetail,
      dialogBtn,
      listDetails,
      queryWalletActiveList,
      queryAccountStrategyList,
      exportClick,
      cardClose
    };
  },
};
</script>
<style lang="scss" scoped>
.details {
  color: #1abc9c;
}

.person-msg-list {

  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  .search-box {
    padding: 0 20px;
  }

  .el-dialog__header {
    border-bottom: 1px solid #efefef;
  }

  .el-dialog__headerbtn {
    font-size: 20px;
    top: 10px;
    right: 10px;
  }

  .el-dialog__header {
    padding: 10px 20px;
  }

  .el-overlay {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .el-dialog__footer {
    text-align: center;
  }

  .el-dialog__title {
    font-size: 14px;
  }

  .el-table {
    border-left: none;

    tr>th:last-child,
    tr>td:last-child {
      border-right: none !important;
    }

    .el-table--border,
    .el-table--group {
      border: none;
    }

    &::after {
      background-color: transparent;
    }

    .el-table__row {
      height: 48px;
    }
  }
}

.money-range {
  display: flex;
  align-items: center;
}
</style>
