<template>
  <div>
    <el-dialog :model-value="dialogVisible" title="导入冲正" width="90%" :before-close="beforeClose" :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <el-form inline size="small" ref="ruleForm" :model="state.form" :rules="state.rules" label-width="100px">
            <el-form-item label="补助类型:" prop="subsidyType">
              <el-select clearable v-model="state.form.subsidyType" placeholder="请选择">
                <el-option v-for="(item, index) in subsidyTypeList" :key="index" :label="item.stName" :value="item.stId">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="项目名称:" prop="projectName">
              <el-input placeholder="请输入" v-model="state.form.projectName"></el-input>
            </el-form-item>

            <el-form-item label="交易类型:" prop="costType">
              <el-select disabled v-model="state.form.costType" placeholder="请选择">
                <el-option v-for="(item, index) in state.tradeTypeList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属年月:" prop="subsidyMonth">
              <el-date-picker v-model="state.form.subsidyMonth" type="month" placeholder="请选择日期" @change="changeDate">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-form size="small" label-width="100px">
            <el-form-item label="发放说明:">
              <el-input placeholder="请输入" type="textarea" v-model="state.form.grantRemark"></el-input>
            </el-form-item>
          </el-form>
          <el-form inline size="small" label-width="100px">
            <el-form-item label=" 上传文件:">
              <el-upload class="upload-demo" :action="state.requestUrl" :headers="{ Authorization: `bearer ${state.requestHeader}` }" :on-preview="handlePreview" :on-remove="handleRemove" :on-success="handleSuccess" :before-remove="beforeRemove" :limit="3" :on-exceed="handleExceed" :file-list="fileList" ref="uploadDom">
                <el-button size="small" type="primary">上传充值清单</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div class="blue" @click="uploadSample()">下载样例</div>
            </el-form-item>
          </el-form>
          <div v-if="state.abnormalData > 0">
            <span class="red">•</span> 异常数据
            <span class="red">{{ state.abnormalData }}</span> 条，请修改确认
          </div>
          <el-table style="width: 100%" :data="state.WaitSubsidyList" v-loading="WaitSubsidyLoading" border stripe>
            <el-table-column label="校检结果" prop="verifyResult" align="center"></el-table-column>
            <el-table-column label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
            <el-table-column label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column label="发放金额（元）" prop="rightingAmount" align="center"></el-table-column>
            <el-table-column label="有效期至" prop="invalidTime" align="center"></el-table-column>
            <el-table-column label="操作" prop="userName" align="center">
              <template #default="scope">
                <div @click="delClick(scope.row)">删除</div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box">
            <div>
              <span>合计：<span>{{ state.totalCount }}</span>人，<span>{{ state.totalAmount }}</span>元</span>
            </div>
            <div class="pagination">
              <el-pagination background :current-page="state.WaitSubsidyForm.currentPage" :page-size="state.WaitSubsidyForm.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.WaitSubsidyListTotal" @current-change="handlePageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="beforeClose()" size="mini">关&nbsp;&nbsp;闭</el-button>
          <el-button @click="submitForm()" :loading="state.loading" size="mini" type="primary">确&nbsp;&nbsp;认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { onMounted, reactive, ref, watch, computed } from "vue";
import { getToken } from "@/utils";
import { timeStr } from "@/utils/date.js";
import {
  saveImportSubsidyRightingList,
  getWaitSubsidyRightingList,
  deleteWaitSubsidyRighting,
  exportSubsidyRightingList,
} from "@/applications/eccard-finance/api";

import { useStore } from "vuex";

import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElUpload,
  ElDatePicker,
  ElMessage,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElUpload,
    ElDatePicker,
  },
  props: {
    dialogVisible: {
      types: Boolean,
      default: false,
    },
    personListData: {
      types: Object,
      default: {},
    },
  },
  setup(props, context) {
    const uploadDom = ref(null);
    const store = useStore();
    let ruleForm = ref(null);
    const state = reactive({
      loading: false,
      WaitSubsidyLoading: false,
      grantType: "IMPORT",
      form: {
        costType: 102
      },
      generateSubsidyForm: {},
      rules: {
        subsidyType: [
          {
            required: true,
            message: "请选择补助类型",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        costType: [
          {
            required: true,
            message: "请选择交易类型",
            trigger: "change",
          },
        ],
        subsidyMonth: [
          {
            required: true,
            message: "请选择所属年月",
            trigger: "change",
          },
        ],
        grantType: [
          {
            required: true,
            message: "请选择发放方式",
            trigger: "change",
          },
        ],
      },
      WaitSubsidyForm: {
        currentPage: 1,
        pageSize: 6,
      },
      WaitSubsidyList: [],
      WaitSubsidyListTotal: 0,
      totalCount: 0,
      totalAmount: 0,
      requestHeader: getToken(),
      requestUrl: `${CONFIG.BASE_API_PATH}eccard-finance/subsidyRecord/importSubsidyRightingList`,
      generateListForm: {
        deptPaths: [],
        rechargeAmount: 0,
        userRole: 0,
        cardTypes: [],
      },

      personList: props.personListData.list,
      total: props.personListData.total,
      roleList: [],
      departCheckList: [],
      subsidyTypeList: [],
      tradeTypeList: [
        // { label: "充值", value: 101 },
        {
          label: "冲正",
          value: 102,
        },
      ],
    });
    const subsidyTypeList = computed(() => {
      return store.state.subsidyData.subsidyTypeList;
    });
    watch(
      () => props.dialogVisible,
      (val) => {
        if (!val) {
          state.WaitSubsidyForm = {
            currentPage: 1,
            pageSize: 6,
          };
          state.WaitSubsidyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.WaitSubsidyList = [];
          ruleForm.value.resetFields();
          uploadDom.value.clearFiles();
          state.WaitSubsidyListTotal = 0;
          state.totalCount = 0;
          state.totalAmount = 0;
          state.abnormalData = 0;
        }
      }
    );

    //获取名单
    const queryWaitSubsidyListByPage = () => {
      state.WaitSubsidyLoading = true;
      getWaitSubsidyRightingList(state.WaitSubsidyForm)
        .then(({ data: { generateSubsidy, pageInfo } }) => {
          state.totalCount = generateSubsidy.totalCount;
          state.totalAmount = generateSubsidy.totalAmount;
          state.abnormalData = generateSubsidy.errorCount;
          state.WaitSubsidyList = pageInfo;
          state.WaitSubsidyListTotal = generateSubsidy.totalCount;
          state.WaitSubsidyLoading = false;
        })
        .catch(() => { });
      state.WaitSubsidyLoading = false;
    };

    const delClick = (val) => {
      console.log(val);
      let data = {
        projectNo: state.form.projectNo,
        id: parseInt(val.id),
      };
      deleteWaitSubsidyRighting(data).then((res) => {
        let { totalCount, totalAmount, errorCount } = res.data.data;
        state.totalCount = totalCount;
        state.totalAmount = totalAmount;
        state.abnormalData = errorCount;
        queryWaitSubsidyListByPage();
      });
    };

    const uploadSample = async () => {
      let res = await exportSubsidyRightingList();
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "导入补助冲正清单样例.xlsx");
      document.body.appendChild(link);
      link.click();
    };

    const submitForm = async () => {
      console.log(ruleForm);
      ruleForm.value.validate(async (valid) => {
        if (valid) {
          if (!state.form.projectNo) {
            return ElMessage.error("请先生成冲正清单！");
          }
          state.form.subsidyMonth = timeStr(state.form.subsidyMonth);
          state.form.grantMode = "IMPORT";
          let data = { ...state.form, ...state.generateSubsidyForm };
          data.relationProjectId = store.state.subsidyData.selectRow.id;
          console.log(data);
          state.loading = true
          let { code, message } = await saveImportSubsidyRightingList(data);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("offImportReversal", true);
          } else {
            ElMessage.error(message);
          }
          state.loading = false
        } else {
          return false;
        }
      });
    };
    const handleSuccess = (res) => {
      if (res.code === 0) {
        state.WaitSubsidyForm.projectNo = res.data.projectNo;
        state.form.projectNo = res.data.projectNo;
        state.abnormalData = res.data.errorCount;
        state.totalCount = res.data.totalCount;
        state.totalAmount = res.data.totalAmount;
        queryWaitSubsidyListByPage();
      } else {
        ElMessage.error(res.message);
      }
      console.log(res);

    };
    const handleRemove = () => {
      state.WaitSubsidyList = [];
      state.WaitSubsidyListTotal = 0;
      state.abnormalData = 0;
      state.totalCount = 0;
      state.totalAmount = 0;
    };

    const beforeClose = () => {
      context.emit("offImportReversal", false);
    };

    const handlePageChange = (val) => {
      state.WaitSubsidyForm.currentPage = val;
      queryWaitSubsidyListByPage();
    };
    const handleSizeChange = (val) => {
      state.WaitSubsidyForm.currentPage = 1;
      state.WaitSubsidyForm.pageSize = val;
      queryWaitSubsidyListByPage();
    };

    onMounted(() => { });
    return {
      state,
      subsidyTypeList,
      uploadDom,
      ruleForm,
      submitForm,
      beforeClose,
      uploadSample,
      delClick,
      handlePageChange,
      handleSizeChange,
      handleSuccess,
      handleRemove,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.select-input-lang {
  .el-select {
    width: 500px;
  }
}

.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>