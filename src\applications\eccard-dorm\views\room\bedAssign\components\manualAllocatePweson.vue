<template>
  <el-dialog :model-value="isShow" :title="data ? '调配人员' : '分配人员'" width="1650px" :before-close="beforeClose" :close-on-click-modal="false">
    <div class="main-box" v-loading="state.loading">
      <div class="box-left">
        <div class="left-item" v-for="(item, index) in state.roomList" :key="index">
          <el-checkbox v-model="item.isSelect" :label="item.roomName" @change="checkedChange(item, index)" size="large">
          </el-checkbox>
          <div style="width: 100px;">床位：{{ item.bedCount - item.surplusBedCount }}/{{ item.bedCount }}</div>
          <div style="flex:1">{{ item.deptName }}</div>
        </div>
      </div>
      <div class="box-right" style="padding-top:20px">
        <kade-select-table :isMultiple="true" :isCurrentSelect="true" :isShow="isShow" :isDefaultRequest="false" :value="[]" :reqFnc="getUserInfoListByPage" :selectCondition="state.selectCondition" :column="column" :params="params" @rowChange="rowChange" @change="personChange" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">取&nbsp;&nbsp;消</el-button>
        <el-button @click="submit()" type="primary" size="mini">确&nbsp;&nbsp;认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElDialog, ElButton, ElCheckbox, ElMessage } from "element-plus"
import { reactive, watch, onMounted } from 'vue'
import { useDict } from "@/hooks/useDict.js";
import {
  getUserInfoListByPage, getRolelist
} from "@/applications/eccard-basic-data/api";
import { userBedAllocate, getBuildRoomInfo } from "@/applications/eccard-dorm/api";

import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "roleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "beginPage",
  pageSizeKey: "rowNum",
  resListKey: "dataList",
  resTotalKey: "totalCount",
  value: {
    userState: "STATE_IN"
  },
  tagNameKey: "userName",
  valueKey: "id",
};
export default {
  components: {
    ElDialog,
    ElButton,
    ElCheckbox,
    "kade-select-table": selectTable,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    },
    roomList: {
      type: Array,
      defalut: null
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      selectCondition: [
        {
          label: "组织机构",
          valueKey: "userDept",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRole",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: [],
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
        {
          label: "性别",
          valueKey: "userSex",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: useDict("SYS_SEX"),
            option: {
              label: "label",
              value: "value",
            },
          },
        },
        { label: '关键字', valueKey: 'userName', placeholder: '姓名或编号关键字搜索', isSelect: false },
      ],
      roomList: [],
      form: {
        userIds: []
      },
      isSave: false,
    })
    watch(() => props.isShow, val => {
      if (val) {
        state.isSave = false
        state.form = {}
        state.roomList = props.roomList.map(item => {
          return {
            ...item,
            isSelect: false
          }
        })
      }
    })
    //获取身份类别
    const queryRolelist = () => {
      getRolelist().then((res) => {
        state.selectCondition[1].select.list = res.data
      });
    };
    const checkedChange = (row) => {
      if (row.isSelect) {
        state.roomList.forEach(item => {
          if (item.roomId != row.roomId) {
            item.isSelect = false
          }
        })
      }
    }
    //获取房间列表
    const getRoomList = () => {
      state.loading = true
      getBuildRoomInfo(props.data).then(({ data }) => {
        state.roomList = data.map(item => {
          return {
            ...item,
            isShow: false,
            isSelect: false
          }
        })
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    }
    const personChange = (val) => {
      console.log(val);
      state.form.userIds = val.list.map(item => item.id);
    };

    const submit = async () => {
      let arr = state.roomList.filter(item => item.isSelect)
      if (!arr.length) {
        return ElMessage.error('请选择房间！')
      }
      if (arr[0].surplusBedCount < state.form.userIds.length) {
        return ElMessage.error('当前选择人员数量已超过当前选择房间剩余床位数！')
      }
      state.form.roomId = arr[0].roomId
      console.log(state.form);
      state.loading = true
      try {
        let { code, message } = await userBedAllocate(state.form)
        if (code === 0) {
          state.isSave = true
          ElMessage.success(message)
          state.form = {}
          getRoomList()
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }

    const beforeClose = () => {
      context.emit("close", state.isSave)
    }
    onMounted(() => {
      queryRolelist()
    })
    return {
      getUserInfoListByPage,
      column,
      params,
      state,
      checkedChange,
      personChange,
      submit,
      beforeClose
    }
  }
}
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0;
}

.main-box {
  margin-top: 20px;
  border: 1px solid #eeeeee;
  display: flex;

  .box-left {
    width: 400px;
    border-right: 1px solid #eeeeee;
    height: 470px;
    overflow-y: auto;

    .left-item {
      box-sizing: border-box;
      width: 100%;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eeeeee;

      :deep(.el-checkbox) {
        margin-right: 20px;
      }
    }
  }

  .box-right {
    flex: 1;
  }
}

.pagination {
  padding: 10px;
}

:deep(.el-table--border) {
  border-left: none;
  border-right: none;
}
</style>