<template>
  <div class="personnelInformation">
    <kade-route-card>
      <kade-table-filter @search="search" @reset="reset">
        <el-form inline label-width="120px" size="small">
          <el-form-item label="编号/姓名">
            <el-input size="small" v-model="state.queryListForm.keyWord" placeholder="请输入用户编号或姓名" />
          </el-form-item>
          <el-form-item label="物理卡号">
            <el-input size="small" v-model="state.queryListForm.cardNo" placeholder="请输入物理卡号" />
          </el-form-item>
          <el-form-item label="卡操作类型">
            <el-select clearable v-model="state.queryListForm.operationType" placeholder="请选择">
              <el-option v-for="(item, index) in state.PERSON_CARD_OPERATION_TYPE_LIST" :key="index"
                :label="item.dictValue" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="卡类">
            <el-select clearable v-model="state.queryListForm.cardType" placeholder="请选择">
              <el-option v-for="(item, index) in state.CardTypeList" :key="index" :label="item.ctName"
                :value="item.ctCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作员">
            <el-select clearable v-model="state.queryListForm.operationUser" placeholder="请选择">
              <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName"
                :value="item.userName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择日期:">
            <el-col :span="24">
              <el-date-picker v-model="state.requestDate" unlink-panels type="datetimerange" range-separator="~"
                start-placeholder="请选择日期" end-placeholder="请选择日期" @change="changeDate">
              </el-date-picker>
            </el-col>
            <el-col :span="3" class="date" v-for="(item, index) in state.defaultDateList" :key="index"
              @click="changeDefaultDate(item.value)">
              {{ item.label }}
            </el-col>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <div>
        <el-form inline>
          <el-form-item label="总发卡数:">{{ state.sendCardTotal }}</el-form-item>
          <el-form-item label="退卡数:">{{ state.refundTotal }}</el-form-item>
        </el-form>
      </div>
      <kade-table-wrap title="卡片日志列表">
        <template #extra>
          <el-button icon="el-icon-daochu" size="small" type="primary" @click="exportClick()">导出</el-button>
        </template>
        <el-table style="width: 100%" :data="state.dataList" border stripe>
          <el-table-column label="人员编号" prop="userCode" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="人员姓名" prop="userName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="组织机构" prop="departName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="卡类" prop="ctName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="卡片类别" prop="cardCategoryName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="使用人员" prop="userNameOfUse" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="物理卡号" prop="cardNo" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="卡操作类型" prop="operationTypeName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="卡操作时间" prop="operationTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作员" prop="operationUser" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background v-model:current-page="state.queryListForm.currentPage"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200]"
            :total="state.dataListTotal" v-model:page-size="state.queryListForm.pageSize"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElInput,
  ElPagination,
  ElSelect,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElDatePicker,
  ElButton,
  ElCol,
} from "element-plus";
import { onMounted, reactive } from "vue";
import {
  cardLogExport,
  getCardLogByPage,
  workStationActive,
  workStationUpdate,
} from "@/applications/eccard-card/api";
import { timeStr } from "@/utils/date";
import { requestDate } from "@/utils/reqDefaultDate";
import { getCardTypeList, getSystemUser } from "@/applications/eccard-finance/api";

export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-pagination": ElPagination,
    "el-select": ElSelect,
    "el-date-picker": ElDatePicker,
    "el-option": ElOption,
    "el-button": ElButton,
    "el-col": ElCol,
  },
  setup() {
    const state = reactive({
      dialogVisible: false,
      dataList: [],
      dataListTotal: 0,
      sendCardTotal: 0,
      refundTotal: 0,
      selectRow: "",
      requestDate: requestDate(),
      queryListForm: {
        currentPage: 1,
        pageSize: 10,
        cardType: "",
        keyWord: "",
        operationType: "",
        startTime: requestDate()[0],
        endTime: requestDate()[1],
        cardNo: "",
      },
      editForm: {
        wstName: "",
        expiryDate: "",
        wstId: "",
      },
      activeForm: {
        wstId: 0,
      },
      CardTypeList: [],
      CardTypeListQueryForm: {
        beginPage: 1,
        rowNum: 100,
      },
      PERSON_CARD_OPERATION_TYPE_LIST: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_OPERATION_TYPE"), //卡片操作类型
      systemUserList: []
    });
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        console.log(res)
        state.systemUserList = res.data;
      });
    };
    const getList = () => {
      getCardLogByPage(state.queryListForm).then((res) => {
        state.dataList = res.data.page.list;
        state.dataListTotal = res.data.page.total;
        state.refundTotal = res.data.refundTotal;
        state.sendCardTotal = res.data.total;
      });
    };
    const rowClick = (row) => {
      state.selectRow = row;
    };
    const showInfo = (row) => {
      state.editForm.wstName = row.wstName;
      state.editForm.expiryDate = row.expiryDate;
      state.editForm.wstId = row.wstId;
      state.dialogVisible = true;
    };

    const update = () => {
      state.editForm.expiryDate = timeStr(state.editForm.expiryDate);
      workStationUpdate(state.editForm).then((res) => {
        ElMessage.info(res.message);
        state.dialogVisible = false;
        getList();
      });
    };
    const changeDate = (val) => {
      if (val) {
        state.queryListForm.startTime = timeStr(val[0]);
        state.queryListForm.endTime = timeStr(val[1]);
      }else{
        delete state.queryListForm.startTime
        delete state.queryListForm.endTime
      }

    };
    const exportClick = async () => {
      let res = await cardLogExport(state.queryListForm);
      let url = window.URL.createObjectURL(
        new Blob([res], { type: "application/vnd.ms-excel" })
      );
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "卡片日志表.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link)
    };
    const search = () => {
      getList();
      state.selectRow = "";
    };
    const reset = () => {
      state.queryListForm = {
        currentPage: 1,
        pageSize: 10,
        startTime: requestDate()[0],
        endTime: requestDate()[1],
      };
      state.requestDate = requestDate();
    };
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.CardTypeList = res.data;
      });
    };
    const off = () => {
      state.isShow = false;
    };
    const handlePageChange = (val) => {
      state.queryListForm.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.queryListForm.currentPage = 1;
      state.queryListForm.pageSize = val;
      getList();
    };
    const handleActive = (val) => {
      ElMessageBox.confirm(`确定要激活此工作站?`, {
        type: "warning",
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(async () => {
        try {
          state.activeForm.wstId = val;
          const { message } = await workStationActive(state.activeForm);
          ElMessage.success(message);
          getList();
        } catch (e) {
          throw new Error(e.message);
        }
      });
    };
    onMounted(() => {
      getList();
      queryCardTypeList();
      querySystemUser()
    });
    return {
      state,
      rowClick,
      search,
      reset,
      off,
      showInfo,
      exportClick,
      update,
      changeDate,
      handleActive,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.personnelInformation {
  width: 100%;
  height: 100%;
}

.card-log-out {
  background: rgb(90, 92, 124);
  color: #fff;
}</style>
