<template>
  <div class="home padding-box">
    <div class="summary-box">
      <div class="summary-item" v-for="(item, index) in list" :key="index">
        <div class="left" :style="{ backgroundColor: item.background }">
          <img :src="item.icon" alt="">
        </div>
        <div class="right">
          <div class="value" :style="{ color: item.background }">{{ state.statisticsData[item.value]?state.statisticsData[item.value]:0 }}</div>
          <div class="label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="8" style="margin-bottom:20px">
        <kade-check-work />
      </el-col>
      <el-col :span="16" style="margin-bottom:20px">
        <kade-violations />
      </el-col>
      <el-col :span="24" style="margin-bottom:20px">
        <kade-hygiene />
      </el-col>
<!--       <el-col :span="16">
        <kade-hydropower />
      </el-col>
      <el-col :span="8">
        <kade-abnormal />
      </el-col> -->
    </el-row>
  </div>
</template>
<script>
import { reactive, onMounted } from 'vue'
import { ElCol, ElRow } from "element-plus"

import { getDormStatistics } from "@/applications/eccard-dorm/api"

import buildIcon from "@/assets/dorm_img/build.svg"
import roomIcon from "@/assets/dorm_img/room.svg"
import personIcon from "@/assets/dorm_img/person.svg"
import bedIcon from "@/assets/dorm_img/bed.svg"
import repairIcon from "@/assets/dorm_img/repair.svg"
import checkWork from "@/applications/eccard-dorm/views/home/<USER>"
// import abnormal from "@/applications/eccard-dorm/views/home/<USER>"
import hygiene from "@/applications/eccard-dorm/views/home/<USER>"
// import hydropower from "@/applications/eccard-dorm/views/home/<USER>"
import violations from "@/applications/eccard-dorm/views/home/<USER>"

export default {
  components: {
    ElCol, ElRow,
    "kade-check-work": checkWork,
    // "kade-abnormal": abnormal,
    "kade-hygiene": hygiene,
    // "kade-hydropower": hydropower,
    "kade-violations": violations,
  },
  setup() {
    const list = [
      { label: "楼栋数", value: "buildingCount", icon: buildIcon, background: "#3399ff" },
      { label: "房间数", value: "roomCount", icon: roomIcon, background: "#3c6" },
      { label: "入住人数", value: "stayInCount", icon: personIcon, background: "#935cff" },
      { label: "空余床位数", value: "freeBedCount", icon: bedIcon, background: "#ff8726" },
      { label: "报修单数", value: "repairCount", icon: repairIcon, background: "#e64444" },
    ]


    const state = reactive({
      statisticsData: {}
    })
    const getData = async () => {
      let { data } = await getDormStatistics()
      console.log(data);
      state.statisticsData = data

    }
    onMounted(() => {
      getData()
    })
    return {
      state,
      list,
    }
  }
}
</script>
<style lang="scss" scoped>
.home {
  .summary-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .summary-item {
      box-sizing: border-box;

      width: 19%;
      border-radius: 5px;
      box-shadow: 1px 3px 5px rgb(0 0 0 / 12%);
      overflow: hidden;
      display: flex;

      .left {
        width: 100px;
        // height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 40px;
          height: 40px;
        }
      }

      .right {
        flex: 1;
        margin-left: 20px;
        padding: 20px 0;

        .value {
          font: 36px arial;
          // height: 60px;
        }

        .label {
          font-size: 14px;
          color: #999999;
        }
      }
    }
  }
}

:deep(.el-card__header) {
  padding: 0;

  .header-title {
    display: inline-block;
    padding: 16px;
    border-bottom: 2px solid #3399ff;
    font-size: 16px;
    color: #1890FF;
  }
}
</style>