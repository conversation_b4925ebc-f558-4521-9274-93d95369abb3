<template>
  <el-dialog :model-value="isShow" :title="(rowData.id?'编辑':'新增')+'手机号'" width="500px" :before-close="beforeClose">
    <el-form ref="formRef" size="mini" label-width="120px" :model="state.form" :rules="rules" style="margin-top:20px">
      <el-form-item label="手机号:" prop="tel">
        <el-input v-model="state.form.tel" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose()" size="mini">返&nbsp;&nbsp;回</el-button>
        <el-button @click="submit()" :loading="state.loading" type="primary" size="mini">保&nbsp;&nbsp;存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { nextTick, watch } from "@vue/runtime-core";
import { baseTelLibAdd, baseTelLibEdit } from "@/applications/eccard-basic-data/api";
const rules = {
  tel: [
    {
      required: true,
      message: "请输入手机号",
    },
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号",
    },
  ],
};
export default {
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
  },
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: null,
    },
  },
  setup(props, context) {
    const formRef = ref(null);
    const state = reactive({
      loading:false,
      form: {},
    });
    watch(
      () => props.isShow,
      (val) => {
        state.form = val && props.rowData.id ? { ...props.rowData } : {};
        nextTick(() => {
          formRef.value.clearValidate();
        })
      }
    );
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          state.loading=true
          let fn = props.rowData.id ? baseTelLibEdit : baseTelLibAdd;
          try{
            let { code, message } = await fn(state.form);
          if (code === 0) {
            ElMessage.success(message);
            context.emit("close", true);
          }
          state.loading=false
          }
          catch{
          state.loading=false
          }
        } else {
          return false;
        }
      });
    };
    const beforeClose = () => {
      context.emit("close", false);
    };
    return {
      rules,
      formRef,
      state,
      submit,
      beforeClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 0px 0px 10px;
}
.kade-table-wrap {
  padding-bottom: 0;
}
</style>