<template>
  <el-dialog :model-value="modelValue" :title="title(editType)" width="800px" :before-close="handleClose">
    <el-form inline label-width="150px" size="mini" ref="formRef" :model="state.form" :rules="rules">
      <kade-linkage-select v-if="editType !== 'details'" :isEdit="modelValue ? true : false" :value="state.form"
        :data="linkageData" @change="linkageChange" />
      <el-form-item label="所属区域：" v-if="editType == 'details'">
        <el-input v-if="editType == 'details'" :model-value="state.form.areaName" readonly></el-input>
      </el-form-item>
      <el-form-item label="管理楼栋：" v-if="editType == 'details'">
        <el-input v-if="editType == 'details'" :model-value="state.form.buildName" readonly></el-input>
      </el-form-item>
      <el-form-item label="管理单元：" v-if="editType == 'details'">
        <el-input v-if="editType == 'details'" :model-value="state.form.unitNumName" readonly></el-input>
      </el-form-item>
      <el-form-item label="管理员：" prop="userName">
        <el-input v-if="editType == 'details'" :model-value="state.form.userName" readonly></el-input>
        <el-input v-else placeholder="请选择" v-model="state.form.userName" readonly
          @click="state.isPersoner = true"></el-input>
        <select-person-dialog :isMultiple="false" :isShow="state.isPersoner" @close="closePersonSelect" />
      </el-form-item>
      <el-form-item label="联系电话：" v-if="editType == 'details'">
        <el-input :model-value="state.form.userTel" readonly></el-input>
      </el-form-item>
      <el-form-item label="接收宿管消息：">
        <el-radio-group v-model="state.form.receiveMessage" :disabled="editType == 'details'">
          <el-radio v-for="(item, index) in boolList" :key="index" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer v-if="editType !== 'details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave" size="mini">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElRadio, ElRadioGroup, ElMessage } from "element-plus"
import { reactive, ref, watch, nextTick } from 'vue'
import { useDict } from "@/hooks/useDict";
import { dormManagerAdd, dormManagerEdit } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
import selectPersonDialog from "@/components/table/selectPersonDialog.vue";
const linkageData = {
  area: { label: "所属区域：", valueKey: "areaId", key: "id" },
  building: { label: "管理楼栋：", valueKey: "buildId" },
  unit: { label: "管理单元：", valueKey: "unitNum" },
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
    editType: {
      types: String,
      default: ''
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElRadio,
    ElRadioGroup,
    "kade-linkage-select": linkageSelect,
    "select-person-dialog": selectPersonDialog,
  },
  setup(props, context) {
    const formRef = ref(null)
    const boolList = useDict("SYS_BOOL_STRING");
    const state = reactive({
      isPersoner: false,
      form: {
        receiveMessage: 'TRUE'
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        if (props.editType == 'add') {
          state.form = {
            receiveMessage: 'TRUE'
          }
        } else {
          state.form = { ...props.rowData }
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const title = (val) => {
      if (val == 'add') {
        return '新建宿舍管理员'
      } else if (val == 'edit') {
        return '编辑宿舍管理员'
      } else if (val == 'details') {
        return '宿舍管理员详情'
      }
    }
    const rules = {
      areaId: [
        {
          required: true,
          message: "请选择所属区域",
          trigger: "blur",
        },
      ],
      buildId: [
        {
          required: true,
          message: "请选择管理楼栋",
          trigger: "blur",
        },
      ],
      unitNum: [
        {
          required: true,
          message: "请选择管理单元",
          trigger: "blur",
        },
      ],
      userName: [
        {
          required: true,
          message: "请选择管理员",
          trigger: "blur",
        },
      ],
    }
    const closePersonSelect = (val) => {
      if (val) {
        state.form.userName = val.userName
        state.form.userId = val.id
        state.form.userCode = val.userCode
      }
      console.log(val);
      state.isPersoner = false;
    };
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const handleSave = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          let fn = props.editType == 'add' ? dormManagerAdd : dormManagerEdit
          state.loading = true
          try {
            let { code, message } = await fn(state.form)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", true)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      nextTick(() => {
        formRef.value.clearValidate()
      })
      context.emit("update:modelValue", false)
    };
    return {
      linkageData,
      formRef,
      boolList,
      state,
      title,
      rules,
      closePersonSelect,
      linkageChange,
      handleSave,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

.el-input {
  width: 211px;
}

:deep(.single-image-uploader) {
  width: 100px;
  height: 100px;

  .el-upload {
    width: 100px;
    height: 100px;
  }

  .element-icons {
    font-size: 40px !important;
  }
}
</style>