<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-form-item label="门锁类型">
          <el-select v-model="state.form.roomKey" placeholder="请选择">
            <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="姓名/编号">
          <el-input v-model="state.form.keyWord" placeholder="请输入" clearable></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="权限列表">
      <template #extra>
        <el-button type="primary" icon="el-icon-house" size="mini" @click="state.isOldRoom=true">旧房间管理</el-button>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="add(1)">新房间新增授权</el-button>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="add(2)">旧房间新增授权</el-button>
        <el-button type="danger" icon="el-icon-delete-solid" size="mini" @click="handleBatch()">批量删除</el-button>
      </template>
      <el-table :data="state.dataList" border @selection-change="selectChange" v-loading="state.loading">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column width="140px" label="人员编号" prop="userCode" align="center"></el-table-column>
        <el-table-column width="100px" label="人员姓名" prop="userName" align="center"></el-table-column>
        <el-table-column width="50px" label="性别" prop="userSex" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.userSex) }}
          </template>
        </el-table-column>
        <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column label="卡号" prop="cardNo" align="center"></el-table-column>
        <el-table-column label="房间" prop="roomString" align="center"></el-table-column>
        <el-table-column width="300px" label="权限时间" prop="" align="center">
          <template #default="scope">
            {{scope.row.authBeginTime}}至{{scope.row.authEndTime}}
          </template>
        </el-table-column>
        <el-table-column width="180px" label="授权时间" prop="createTime" align="center"></el-table-column>
        <el-table-column width="100px" label="下发状态" prop="createTime" align="center">
          <template #default="scope">
            {{ scope.row.synStatus==1?'已下发':'未下发' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" class="green" @click="handleDel(scope.row)">删除授权</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentPage" />
      </div>
    </kade-table-wrap>
    <kade-access-add v-model:modelValue="state.isShow" :type="state.type" @update:modelValue="close" />
    <old-room-manage v-model:modelValue="state.isOldRoom" />
  </kade-route-card>
</template>

<script>
import { reactive, onMounted } from 'vue'
import { oldRoomAuthLockAuthPage, oldRoomAuthLockAuthDel } from "@/applications/eccard-dorm/api";
import deptSelectTree from '@/components/tree/deptSelectTree'
import add from "./components/add.vue"
import oldRoomManage from "./components/oldRoomManage.vue"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElTable, ElTableColumn, ElPagination, ElMessageBox, ElMessage } from "element-plus"
const linkageData = {
  area: { label: '区域', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
  floor: { label: '楼层', valueKey: 'floorNum' },
  room: { label: '房间', valueKey: 'roomId' }
}
const typeList = [
  { label: "新门锁", value: 1 },
  { label: "旧门锁", value: 2 },
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect, ElOption,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
    "kade-linkage-select": linkageSelect,
    "kade-access-add": add,
    oldRoomManage
  },
  setup() {
    const state = reactive({
      type: null,
      form: {
        roomKey: 1,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      isShow: false,
      isOldRoom: false,
      selectData: [],
      loading: false
    })
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        roomKey: 1,
        currentPage: 1,
        pageSize: 10
      }
      getList()
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await oldRoomAuthLockAuthPage(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const add = (val) => {
      state.type = val
      state.isShow = true
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isShow = false
    }

    const handleDel = (row) => {
      ElMessageBox.confirm(`确认删除？`, `提示`, {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        let { code, message } = await oldRoomAuthLockAuthDel({ authIds: [row.id] })
        if (code === 0) {
          ElMessage.success(message)
          getList()
        }
      })
    }
    const selectChange = (val) => {
      state.selectData = val
    }
    const handleBatch = () => {
      if (!state.selectData.length) {
        return ElMessage.error('请先选择需要删除的信息!')
      }
      ElMessageBox.confirm(`确认删除已选择信息？`, `提示`, {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        let list = state.selectData.map(item => item.id)
        let { code, message } = await oldRoomAuthLockAuthDel({ authIds: list })
        if (code === 0) {
          ElMessage.success(message)
          getList()
        }
      })
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentPage = (val) => {
      state.form.currentPage = val
      getList()
    }
    onMounted(() => {
      getList()
    })
    return {
      typeList,
      state,
      add,
      handleDel,
      handleBatch,
      close,
      handleSearch,
      handleReset,
      linkageData,
      selectChange,
      linkageChange,
      handleSizeChange,
      handleCurrentPage
    }
  }
}
</script>

<style lang="scss" scoped>
.green {
  text-decoration: underline;
}
:deep(.el-input--mini .el-input__inner) {
  width: 198px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}
:deep(.el-dialog) {
  border-radius: 6px;
  padding-bottom: 15px;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}
:deep(.el-dialog__footer) {
  border-top: none;
  text-align: center;
}
:deep(.el-divider--horizontal) {
  margin: 0 0 20px 0;
}
</style>