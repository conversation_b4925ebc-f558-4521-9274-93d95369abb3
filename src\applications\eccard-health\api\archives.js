import request from '@/service';



//健康档案分页查询
export function healthRecordsPage(params) {
  return request.post('/eccard-basic-data/healthRecords/page', params,);
}

//健康档案新增
export function healthRecordsAdd(params) {
  return request.post('/eccard-basic-data/healthRecords', params,);
}
//健康档案编辑
export function healthRecordsUpdate(params) {
  return request.put('/eccard-basic-data/healthRecords', params,);
}


//病史-手术史列表查询
export function healthMedicalHistory(params) {
  return request.post('/eccard-basic-data/healthMedicalHistory/list', params,);
}

//病史-手术史新增
export function healthMedicalHistoryAdd(params) {
  return request.post('/eccard-basic-data/healthMedicalHistory', params,);
}
//病史-手术史编辑
export function healthMedicalHistoryUpdate(params) {
  return request.put('/eccard-basic-data/healthMedicalHistory', params,);
}
//病史-手术史删除
export function healthMedicalHistoryDel(params) {
  return request.delete(`/eccard-basic-data/healthMedicalHistory/${params}`,);
}