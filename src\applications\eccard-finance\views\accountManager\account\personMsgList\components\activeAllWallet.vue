<template>
  <div class="box-dialog">
    <kade-table-wrap title="选择批量钱包激活人员">
      <el-divider></el-divider>
      <kade-select-table :isShow="isActiveAllWallet" :value="[]" :reqFnc="getPersonList"
        :selectCondition="state.selectCondition" :column="column" :isCurrentSelect="true" :params="params" @change="personChange" />
    </kade-table-wrap>
    <kade-table-wrap title="激活钱包" style="margin-top: 10px">
      <el-divider></el-divider>
      <el-table style="width: 100%" :data="state.form.walletList" v-loading="false" border stripe>
        <el-table-column label="钱包名称" prop="walletName" align="center"></el-table-column>
        <el-table-column label="钱包类型" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.walletType) }}
          </template>
        </el-table-column>
        <el-table-column label="是否启用" prop="walletName" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.walletStatus" active-value="WALLET_NORMAL" inactive-value="WALLET_NOT_ACTIVE"
              inline-prompt />
          </template>
        </el-table-column>
        <el-table-column label="有效期（年）" align="center"><template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="ash">
              未开启
            </div>
            <el-input v-else class="date-num-input" placeholder="请输入" type="number"
              v-model="scope.row.walletValidityDateNum" :min="1" size="mini"></el-input>
          </template></el-table-column>
        <el-table-column label="有效期至" width="300" align="center">
          <template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="ash">
              未开启
            </div>

            <div v-else>
              {{
              scope.row.walletValidityDateNum
              ? dateStr(
              scope.row.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
              new Date().getTime()
              )
              : ""
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">取&nbsp;&nbsp;消</el-button>
      <el-button @click="submitForm()" size="mini" type="primary" :loading="state.loading">确&nbsp;&nbsp;认</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElDivider,
  ElTable,
  ElTableColumn,
  ElButton,
  ElInput,
  ElMessage,
  ElSwitch,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { onMounted, watch } from "@vue/runtime-core";
import { dateStr } from "@/utils/date.js";
import {
  getPersonList,
  batchPersonWalletActive,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";
import selectTable from "@/components/table/selectTable.vue";
const column = [
  { label: "用户编号", prop: "userCode", isDict: false, width: "" },
  { label: "姓名", prop: "userName", isDict: false, width: "" },
  { label: "组织机构", prop: "deptName", isDict: false, width: "" },
  { label: "身份类别", prop: "userRoleName", isDict: false, width: "" },
];
const params = {
  currentPageKey: "currentPage",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
    acctStatus: "NORMAL_ACCOUNT",
  },
  tagNameKey: "userName",
  valueKey: "userId"
};

export default {
  components: {
    ElDivider,
    ElTable,
    ElTableColumn,
    ElButton,
    ElInput,
    ElSwitch,
    "kade-select-table": selectTable,
  },
  props: {
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    roleList: {
      types: Array,
      default: [],
    },
    isActiveAllWallet: {
      types: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    let dataTable = ref(null);
    const state = reactive({
      loading: false,
      selectCondition: [
        {
          label: "关键字",
          valueKey: "keyWord",
          placeholder: "姓名或编号关键字搜索",
          isSelect: false,
        },
        {
          label: "组织机构",
          valueKey: "deptPath",
          dataKey: "deptPath",
          placeholder: "请选择",
          isSelect: false,
          isTree: "dept",
        },
        {
          label: "身份类别",
          valueKey: "userRoleId",
          placeholder: "请选择",
          isSelect: true,
          select: {
            list: props.roleList,
            option: {
              label: "roleName",
              value: "id",
            },
          },
        },
      ],
      form: {
        queryCondition: {
          deptPath: null,
          keyWord: null,
          userRoleId: null,
        },
        selectAll: false,
        userIds: [],
        walletList: [],
      },
      personList: [],
      getPersonLoading: false,
      accountStatusList: [], //账户状态列表
      accountStrategyList: [], //开户策略列表
      cardTypeList: [], //卡片类型列表
    });
    watch(
      () => props.isActiveAllWallet,
      (val) => {
        if (val) {
          queryWalletActiveList();
          state.form = {
            queryCondition: {
              deptPath: null,
              keyWord: null,
              userRoleId: null,
            },
            selectAll: false,
            userIds: [],
            walletList: [],
          };
        } else {
          off();
        }
      }
    );

    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.form.walletList = res.data.map((item) => {
          return {
            walletCode: item.walletCode,
            walletName: item.walletName,
            walletStatus: "WALLET_NOT_ACTIVE",
            walletType: item.walletType,
            walletValidityDate: "",
            walletValidityDateNum: 5,
          };
        });
      });
    };


    const personChange = (val) => {
      state.form.userIds = val.list.map((item) => item.userId);
      let { deptPath, keyWord, userRoleId } = val.params;
      state.form.queryCondition = { deptPath, keyWord, userRoleId };
      state.form.selectAll = val.isSelectAll;
      delete state.form.queryCondition.currentPage;
      delete state.form.queryCondition.pageSize;
    };

    const off = () => {
      context.emit("off", false);
      state.form.userIds = [];
    };
    const submitForm = () => {
      let params = { ...state.form };
      if (state.form.selectAll) {
        params.userIds = [];
      } else {
        if (!state.form.userIds.length) {
          return ElMessage.error("请选择人员！");
        }
      }
      for (let item of params.walletList) {
        if (!item.walletStatus) {
          ElMessage.error("请选择钱包状态！");
          return false;
        }
        if (!item.walletValidityDateNum) {
          ElMessage.error("请输入钱包有效期！");
          return false;
        }
        item.walletValidityDate = dateStr(
          item.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
          new Date().getTime()
        );
      }
      state.loading = true
      batchPersonWalletActive(params).then(res => {
        if (res.code === 0) {
          ElMessage.success(res.message);
          context.emit("success", true);
          state.form = {
            queryCondition: {
              deptPath: null,
              keyWord: null,
              userRoleId: null,
            },
            selectAll: false,
            userIds: [],
            walletList: [],
          };
        }
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    };
    onMounted(() => {
      queryWalletActiveList();
    });
    return {
      getPersonList,
      column,
      params,
      dataTable,
      state,
      dateStr,
      personChange,
      off,
      submitForm,
    };
  },
};
</script>
<style lang="scss" scoped>
.box-dialog {
  padding: 10px 0;

  .el-table__row {
    height: 30px !important;
  }

  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}

.el-divider--horizontal {
  margin: 0 0 10px;
}

.table-box {
  padding: 10px;
}

.submit-btn {
  text-align: center;
  margin: 10px auto;
}

.pagination {
  display: flex;
  justify-content: space-between;
  margin-left: 20px;
}
</style>