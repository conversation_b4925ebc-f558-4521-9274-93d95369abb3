<template>
  <el-dialog :model-value="modelValue" title="设备导入绑定" width="1000px" :before-close="handleClose">
    <div style="padding-bottom:30px" v-loading="state.loading">
      <div class="top-button">
        <el-form inline label-width="120px">
          <el-form-item label="导入设备类型：">
            <el-select size="mini" v-model="state.deviceType">
              <el-option v-for="(item,index) in deviceTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-button type="text" size="small" @click="exportModel" style="text-decoration: underline">导入模板下载</el-button>
          </el-form-item>
        </el-form>
        <div style="display:flex">
          <el-upload :disabled="!state.deviceType" style="margin:0 10px" :data="{deviceType:state.deviceType}" ref="uploadRef" :headers="{ Authorization: `bearer ${state.uploadHeader}` }" class="upload-demo" :show-file-list="false" :action="state.uploadUrl" :before-upload="beforeUpload" :on-success="uploadSuccess" :on-error="uploadError">
            <el-button :disabled="!state.deviceType" type="success" icon="el-icon-daoru" size="small">导入数据</el-button>
          </el-upload>
          <div>
            <el-button @click="save" type="primary" size="small" :disabled="!state.ImportUnitDeviceRes.projectNo">保存</el-button>
          </div>
        </div>
      </div>
      <div class="table-box">
        <el-table :data="state.dataList" border>
          <!-- <el-table-column show-overflow-tooltip prop="" type="selection" align="center"></el-table-column> -->
          <el-table-column show-overflow-tooltip prop="areaName" label="所属区域" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="buildName" label="所属楼栋" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="unitNumName" label="所属单元" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="deviceNo" label="设备机号" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip width="200px" prop="resultMsg" label="校验结果" align="center">
            <template #default="scope">
              <div :style="{color:scope.row.error?'red':'green'}">
                {{scope.row.resultMsg}}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElTable, ElTableColumn, ElPagination, ElUpload, ElMessage } from "element-plus"
import { reactive, watch } from 'vue'
import { getToken, downloadXlsx } from "@/utils";
import { useDict } from "@/hooks/useDict.js";
import { unitDeviceImportTemplate, importUnitDeviceList, saveImportUnitDevice } from "@/applications/eccard-dorm/api";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElUpload,
  },
  setup(props, context) {
    const deviceTypeList = useDict("DORM_UNIT_DEVICE_TYPE");
    const state = reactive({
      loading: false,
      deviceType: "",
      uploadHeader: getToken(),
      uploadUrl: `${CONFIG.BASE_API_PATH}eccard-dorm/UnitDevice/importUnitDevice`,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      ImportUnitDeviceRes: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.dataList = []
        state.total = 0
        state.deviceType = ""
        state.ImportUnitDeviceRes = {}
      }
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { pageInfo: { list, total }, ImportUnitDeviceRes } } = await importUnitDeviceList(state.form)
        state.dataList = list
        state.total = total
        state.ImportUnitDeviceRes = ImportUnitDeviceRes
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const beforeUpload = () => {
      state.loading = true
    }
    const uploadSuccess = ({ code, data, message }) => {
      if (code === 0) {
        state.form.projectNo = data.projectNo
        getList();
      } else {
        ElMessage.error(message);
      }
      state.loading = false
    };
    const uploadError = () => {
      state.loading = false
    }
    const save = async () => {
      if (!state.dataList.length) {
        return ElMessage.error("导入列表为空，请重新导入！")
      }
      if (state.ImportUnitDeviceRes.errorCount !== 0) {
        return ElMessage.error('当前单元设备数据有错误信息，请检查后重新上传！')
      }
      state.loading = true
      try {
        let { code, message } = await saveImportUnitDevice({ projectNo: state.ImportUnitDeviceRes.projectNo })
        if (code === 0) {
          ElMessage.success(message)
          context.emit("update:modelValue", true)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }

    }
    const exportModel = async () => {
      state.loading = true
      let res = await unitDeviceImportTemplate()
      downloadXlsx(res, "单元设备列表.xlsx")
      state.loading = false
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      deviceTypeList,
      state,
      beforeUpload,
      uploadSuccess,
      uploadError,
      exportModel,
      save,
      handleSizeChange,
      handleCurrentChange,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
.top-button {
  display: flex;
  justify-content: space-between;
  align-content: center;
  margin: 20px 0 0;
}
.table-box {
  // border: 1px solid #eeeeee;
  .pagination {
    padding: 10px;
    border: 1px solid #eeeeee;
    border-top: 0;
  }
}
</style>