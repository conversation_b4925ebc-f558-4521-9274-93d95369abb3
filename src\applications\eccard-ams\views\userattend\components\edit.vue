<template>
  <div>
    <div style="with:120px;float:left">
      <el-image style="width: 100px; height: 200px" :src="state.model.userPhoto" :fit="'contain'" />
    </div>
    <div style="margin-left:140px;width:95%">
      <el-form ref="formRef" :label-width="labelWidth" :inline="true" :rules="rules" :model="state.model" size="small">
        <el-form-item label="人员编号" prop="userNum">
          <el-input placeholder="请输入" v-model="state.model.userNum" />
        </el-form-item>
        <el-form-item label="人员姓名" prop="userName">
          <el-input placeholder="请输入" v-model="state.model.userName" />
        </el-form-item>
        <el-form-item label="性别" prop="userSex">
          <el-input placeholder="请输入" :modelValue="state.model.userSex == 'SEX_MALE' ? '男' : '女'" />
        </el-form-item>
        <el-form-item label="民族" prop="userNation">
          <el-input placeholder="请输入" v-model="state.model.userNation" />
        </el-form-item>
        <el-form-item label="籍贯" prop="userNative">
          <el-input placeholder="请输入" v-model="state.model.userNative" />
        </el-form-item>
        <el-form-item label="出生地" prop="userBornArea">
          <el-input placeholder="请输入" v-model="state.model.userBornArea" />
        </el-form-item>
        <el-form-item label="出生日期" prop="userBornDate">
          <el-input placeholder="请输入" v-model="state.model.userBornDate" />
        </el-form-item>
        <el-form-item label="政治面貌" prop="userPolitical">
          <el-input placeholder="请输入" v-model="state.model.userPolitical" />
        </el-form-item>
        <el-form-item label="证件类型" prop="userIdType">
          <el-input placeholder="请输入" v-model="state.model.userIdType" />
        </el-form-item>
        <el-form-item label="证件号码" prop="userIdCode">
          <el-input placeholder="请输入" v-model="state.model.userIdCode" />
        </el-form-item>
        <el-form-item label="手机号码" prop="userTel">
          <el-input placeholder="请输入" v-model="state.model.userTel" />
        </el-form-item>
        <el-form-item label="账户状态" prop="userAccountStatus">
          <el-input placeholder="请输入" v-model="state.model.userAccountStatus" />
        </el-form-item>
        <el-form-item label="户籍地址" prop="userCenPro" style="width:100%">
          <el-row :gutter="15">
            <el-col :sm="4">
              <el-input placeholder="省" v-model="state.model.userCenPro" />
            </el-col>
            <el-col :sm="4">
              <el-input placeholder="市" v-model="state.model.userCenCity" />
            </el-col>
            <el-col :sm="4">
              <el-input placeholder="区" v-model="state.model.userCenCounty" />
            </el-col>
            <el-col :sm="12">
              <el-input placeholder="地址" v-model="state.model.userCenDetail" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="身份类别" prop="userIde">
          <el-input placeholder="请输入" v-model="state.model.userIde" />
        </el-form-item>
        <el-form-item label="岗位" prop="userPost">
          <el-input placeholder="请输入" v-model="state.model.userPost" />
        </el-form-item>
        <el-form-item label="组织机构" prop="orgNameList">
          <el-input placeholder="请输入" v-model="state.model.orgNameList" />
        </el-form-item>
        <el-form-item label="是否住校" prop="userLiveSchool">
          <el-input placeholder="请输入" v-model="state.model.userLiveSchool" />
        </el-form-item>
        <el-form-item label="人员状态" prop="userPostStatus">
          <el-input placeholder="请输入" v-model="state.model.userPostStatus" />
        </el-form-item>
        <el-form-item label="入校时间" prop="userInDate">
          <el-input placeholder="请输入" v-model="state.model.userInDate" />
        </el-form-item>
        <el-form-item label="离校时间" prop="userOutDate">
          <el-input placeholder="请输入" v-model="state.model.userOutDate" />
        </el-form-item>
        <el-form-item label="家庭住址" prop="userAddrPro" style="width:100%">
          <el-row :gutter="15">
            <el-col :sm="4">
              <el-input placeholder="请输入" v-model="state.model.userAddrPro" />
            </el-col>
            <el-col :sm="4">
              <el-input placeholder="请输入" v-model="state.model.userAddrCity" />
            </el-col>
            <el-col :sm="4">
              <el-input placeholder="请输入" v-model="state.model.userAddrCounty" />
            </el-col>
            <el-col :sm="12">
              <el-input placeholder="请输入" v-model="state.model.userAddrDetail" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-row :gutter="15">
          <el-col :sm="24">
            <el-form-item label="备注" prop="userDetail" style="width:100%">
              <el-input type="textarea" style="width:800px" :rows="5" placeholder="请输入" v-model="state.model.userDetail" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
// import Modal from "@/components/modal";
// import {dateStr} from "@/utils/date.js"
import { computed, reactive, ref, watch } from "vue";
import {
  // ElTabPane,
  // ElTabs,
  // ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  // ElMessage,
  // ElSwitch,
  ElImage,
} from "element-plus";
// import { addRole, updateRole } from "@/applications/eccard-basic-data/api";
// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({}),
    },
    data:{
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    // ElButton,
    ElRow,
    ElCol,
    ElForm,
    ElFormItem,
    ElInput,
    // ElMessage,
    ElImage,
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: props.data,
    });
    const rules = {
      roleName: [
        { required: true, message: "请输入类别名称" },
        { max: 20, message: "类别名称不能超过20个字符" },
      ],
    };
    // const getDetails = async () => {
    //   let params = {
    //     userCode: store.state.userInfo.rowData.userCode,
    //     userId: store.state.userInfo.rowData.id,
    //   };
    //   let { data } = await getBaseUserInfo(params);
    //   state.form = data;
    //   state.form.userRole = Number(state.form.userRole);
    //   state.form.userBirthPlace = Number(state.form.userBirthPlace);
    // };

    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    // const submit = () => {
    //   formRef.value?.validate(async (valid) => {
    //     if (valid) {
    //       try {
    //         btnLoading.value = true;
    //         const fn = props.role?.id ? updateRole : addRole;
    //         const { message, code } = await fn(state.model);
    //         if (code === 0) {
    //           ElMessage.success(message);
    //         } else {
    //           ElMessage.error(message);
    //         }
    //         context.emit("update:modelValue", false);
    //         context.emit("change");
    //       } catch (e) {
    //         throw new Error(e.message);
    //       } finally {
    //         btnLoading.value = false;
    //       }
    //     }
    //   });
    // };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    watch(
      // () => props.data.,
      // (n) => {
      //   if (n) {
      //     if (props.role?.id) {
      //       state.model = Object.assign(getDefaultModel(), props.role);
      //     } else {
      //       state.model = getDefaultModel();
      //     }
      //   }
      // }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      // submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>
