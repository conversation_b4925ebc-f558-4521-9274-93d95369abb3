<template>
  <div class="transactionBalance">
    <el-form inline size="small" label-width="100px">
      <el-form-item label="选择日期:">
        <el-col :span="24">
          <el-date-picker
            v-model="state.requestDate"
            type="datetimerange"
            range-separator="~"
            :default-time="state.defaultTime"
            start-placeholder="请选择日期"
            end-placeholder="请选择日期"
            @change="changeDate"
            unlink-panels
          >
          </el-date-picker>
        </el-col>
        <el-col :span="3" class="date" v-for="(item, index) in state.defaultDateList" :key="index" @click="changeDefaultDate(item.value)">{{ item.label }}</el-col>
      </el-form-item>
      <el-form-item label="&nbsp;">
        <el-button @click="search()" size="small" type="primary" icon="el-icon-search">搜索</el-button>
        <el-button icon="el-icon-refresh-right" @click="reset()" size="small">重置</el-button>
      </el-form-item>
      <div>
        <el-form-item label="金额范围:" class="money-range">
          <span> ￥ </span>
          <el-input class="tranBal-input" placeholder="交易金额" v-model="state.form.minAmount"></el-input>
          <span>&nbsp;&nbsp;- &nbsp;&nbsp;</span>
          <el-input class="tranBal-input" placeholder="交易金额" v-model="state.form.maxAmount"></el-input>
        </el-form-item>
        <el-form-item label="精准查询:">
          <el-input clearable style="width: 215px" placeholder="输入交易单号" v-model="state.form.tradeNo"></el-input>
        </el-form-item>
        <el-form-item label="交易钱包:">
          <el-select clearable v-model="state.form.walletCode" placeholder="请选择">
            <el-option v-for="(item, index) in state.walletActiveList" :key="index" :label="item.walletName" :value="item.walletCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收支类型:">
          <el-select clearable v-model="state.form.inoutType" placeholder="请选择">
            <el-option v-for="(item, index) in state.inoutTypeList" :key="index" :label="item.dictValue" :value="item.dictCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型:">
          <el-select clearable v-model="state.form.costType" placeholder="请选择">
            <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName" :value="item.costCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易来源:">
          <el-select clearable v-model="state.form.tradeSource" placeholder="请选择">
            <el-option v-for="(item, index) in state.tradeSourceList" :key="index" :label="item.tradeName" :value="item.tradeCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易方式:">
          <el-select clearable v-model="state.form.tradeMode" placeholder="请选择">
            <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易设备:">
          <el-select clearable v-model="state.form.deviceNo" placeholder="请选择">
            <el-option v-for="(item, index) in state.deviceList" :key="index" :label="item.deviceName" :value="item.deviceNo"></el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <el-table height="40vh" :data="personTradeList" v-loading="false" highlight-current-row border stripe>
      <el-table-column show-overflow-tooltip type="selection" width="55" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip width="150" label="交易单号" prop="tradeNo" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip width="153" prop="tradeDate" label="交易时间" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易钱包" prop="walletName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="收支类型" prop="inoutType" align="center">
        <template #default="scope">
          {{ filterDictionary(scope.row.inoutType, state.inoutTypeList) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="交易类型" prop="costTypeName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易来源" prop="tradeSourceName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易方式" prop="tradeModeName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易前余额(元)" prop="tradeBeforeBalance" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易金额(元)" prop="tradeAmount" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易后余额(元)" prop="tradeAfterBalance" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="交易设备" prop="deviceName" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip label="操作员" prop="operatorName" align="center"></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.currentPage"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="personTradeTotal"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <kade-route-card style="height: auto;margin-top:10px;">
      <span>合计：</span>
      <span style="margin: 0 20px">收入</span>
      <span style="color: #09b706; font-size: 20px">{{
        amountTotal.incomeAmount
      }}</span>
      <span style="color: #999999">元</span>
      <span style="margin: 0 20px">支出</span>
      <span style="color: #ff8726; font-size: 20px">{{
        amountTotal.expendAmount
      }}</span>
      <span style="color: #999999">元</span>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  // ElDivider,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import {
  tradeMode,
  costType,
  getWalletActiveList,
  tradeSource,
  getDeviceList,
} from "@/applications/eccard-finance/api";
import { reactive } from "@vue/reactivity";
import { timeStr } from "@/utils/date.js";
import { requestDefaultTime,requestDate,defaultDateList } from "@/utils/reqDefaultDate.js";
import { useStore } from "vuex";
import { watch, computed, onMounted } from "@vue/runtime-core";
export default {
  components: {
    // "el-divider": ElDivider,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-date-picker": ElDatePicker,
    "el-col": ElCol,

    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      form: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        userId: store.state.data.selectPerson.userId,
        amountType: 1, //区分钱包类型（金额1/次数2）
      },
      inoutTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "WALLET_INOUT_TYPE"), //收支类型
      defaultDateList,
      requestDate:requestDate(),
      defaultTime:requestDefaultTime(),
      tradeModeList: [],
      costTypeList: [],
      walletActiveList: [],
      tradeSourceList: [],
    });
    const timeStrDate = computed(() => {
      return timeStr;
    });
    watch(
      () => state.form,
      (val) => {
        store.commit("data/updateState", {
        key: "watchExportData",
        payload: val,
      });
      }
    );
    const personTradeList = computed(() => {
      return store.state.data.personTradeList;
    });
    const personTradeTotal = computed(() => {
      return store.state.data.personTradeTotal;
    });
    const amountTotal = computed(() => {
      return store.state.data.amountTotal;
    });
    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };
    //获取交易方式
    const getTradeModeList = () => { 
      tradeMode().then((res) => {
        state.tradeModeList = res.data;
      });
    };
    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data;
      });
    };
    //获取交易钱包
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.walletActiveList = res.data;
      });
    };
    //获取交易来源 
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data;
      });
    };
    //获取交易设备
    const queryDeviceList = () => {
      getDeviceList().then((res) => {
        state.deviceList = res.data;
      });
    };
    const search = () => {
      getPersonTradeList();
    };
    const getPersonTradeList = () => {
      store.dispatch("data/queryPersonTradeList");
    };
    const changeDefaultDate = (val) => {
      state.requestDate = val();
      state.form.beginDate = state.requestDate[0];
      state.form.endDate = state.requestDate[1];
    };
    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getPersonTradeList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getPersonTradeList();
    };
    const reset = () => {
      state.form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        userId: store.state.data.selectPerson.userId,
        amountType: 1, //区分钱包类型（金额/次数）
      };
      state.requestDate=requestDate()

    };
    onMounted(() => {
      getPersonTradeList();
      getTradeModeList();
      getCostTypeList();
      queryWalletActiveList();
      getTradeSource();
      queryDeviceList();
      store.commit("data/updateState", {
        key: "watchExportData",
        payload: state.form,
      });
    });
    return {
      state,
      personTradeList,
      personTradeTotal,
      amountTotal,
      timeStrDate,
      changeDefaultDate,
      changeDate,
      filterDictionary,
      handlePageChange,
      handleSizeChange,
      search,
      reset,
    };
  },
};
</script>
<style lang="scss" scoped>
.date {
  margin: 0 10px;
  color: rgba(0, 0, 0, 0.847058823529412);
}
.date:hover {
  color: #06f;
}
.tranBal-input {
  width: 88px !important;
  .el-input__inner {
    width: 88px !important;
  }
}

</style>