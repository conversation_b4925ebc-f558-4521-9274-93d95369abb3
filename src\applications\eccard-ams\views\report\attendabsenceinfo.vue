<template>
  <div style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="异常类型">
          <el-select v-model="state.form.abiType" :clearable="true">
            <el-option v-for="(item, index) in [{ label: '上班漏刷', value: '上班漏刷' }, { label: '下班漏刷', value: '下班漏刷' }]" :key="index"
              :label="item.label" :value="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="请输入姓名或编号搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgId" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.orgId = val.deptPath)" />
        </el-form-item>
        <el-form-item label="所属考勤组">
          <el-select v-model="state.form.atgId" :clearable="true">
            <el-option v-for="(item, index) in state.groupList" :key="index" :label="item.atgName"
              :value="item.atgId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker v-model="state.form.atiStarttime" type="date" placeholder="开始时间搜索" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" :clearable="true" />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker v-model="state.form.atiEndtime" type="date" placeholder="开始时间搜索" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" :clearable="true" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="缺勤记录列表">
      <template #extra>
        <el-button size="mini" type="primary" icon="Download" @click="handleClick(1)">导出</el-button>
        <el-button size="mini" type="danger" icon="Printer" @click="handleClick(2)">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
        border stripe>
        <el-table-column label="异常类型" prop="abiType" align="center"></el-table-column>
        <el-table-column label="所属班次" prop="abiBeclassname" align="center"></el-table-column>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
        <el-table-column label="考勤日期" prop="abiDate" align="center">
          <template #default="scope">
            {{ getDateTimeStr(scope.row.abiDate) }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="考勤班次" prop="abiClass" align="center"></el-table-column> -->
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <attend-edit :modelValue="state.isEdit" :title="state.type == '详情'" :type="state.type" :userinfo="state.rowData"
      :data="state.rowData" @update:modelValue="close" @edit="state.type = 'edit'" />
  </div>
</template>
<script>
import { reactive, onMounted } from "vue";
import { timeStr, dateStr } from "@/utils/date.js";
import { downloadXlsx, print } from "@/utils"
import { useDict } from "@/hooks/useDict";
import deptSelectTree from '@/components/tree/deptSelectTree'
import {
  // ElSwitch,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElButton,
  ElPagination,
  ElDatePicker,
  ElMessage,
  // ElMessageBox
} from 'element-plus';
import { getAttentionAbsencePage, getAttentionGroupInfoPageAll, downLoadFile } from "@/applications/eccard-ams/api";
import attendEdit from "@/applications/eccard-ams/views/userattend/components/attendedit.vue"

export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    attendEdit,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElDatePicker,
    ElButton,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const statusList = useDict("USER_ATTENDINFO_STATUS");
    const state = reactive({
      loading: false,
      isEdit: false,
      groupList: [],
      form: {
        abiType: '',
        userDisType: 2,
        keyWord: '',
        orgId: '',
        atgId: '',
        atiStarttime: new Date(),
        atiEndtime: new Date(),
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      rowData: "",
      type: "",
    });

    //分页
    const getList = async () => {
      state.loading = true
      let objs = state.form;
      objs["atiStarttime"] = objs["atiStarttime"] ? dateStr(objs["atiStarttime"]) + ' 00:00' : '';
      objs["atiEndtime"] = objs["atiEndtime"] ? dateStr(objs["atiEndtime"]) + ' 23:59' : '';
      let { data } = await getAttentionAbsencePage(objs)
      state.dataList = data.list
      state.total = parseInt(data.count)
      state.loading = false
    }
    const edit = (row, type) => {
      state.type = type
      state.rowData = row
      state.isEdit = true
    }
    const reset = () => {
      state.form = {
        userDisType: 2,
        pageNum: 1,
        pageSize: 10,
        atiStarttime: new Date(),
        atiEndtime: new Date(),
      }
    }
    const getDateTimeStr = (val) => {
      if (!val) {
        return "";
      }
      return timeStr(val);
    }
    const search = () => {
      getList()
    }
    const handleClick = async (flag) => {
      if (flag == 1) {
        let res = await downLoadFile("/eccard-ams/api/ams/attention-absence-info/export", state.form);
        downloadXlsx(res, "缺勤记录.xlsx");
      } else {
        let { code, message } = await downLoadFile("/eccard-ams/api/ams/attention-absence-info/export?type=2", state.form, 2);
        if (code == 0) {
          print(message, '缺勤记录')
        } else {
          ElMessage.warning(message);
        }
      }
    }

    const handlePageChange = (val) => {
      state.form.pageNum = val
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.pageSize = val
      getList()
    }
    // const del=async (row)=>{
    //   let {code,message}=await delSysMessage(row.id)
    //   if(code===0){
    //     ElMessage.success(message)
    //     getList()
    //   }
    // }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isEdit = false
    }

    // const groupList = async () => {
    //   let {data}=await getAttentionGroupInfoPageAll();
    //   return data.list;
    // };

    onMounted(async () => {
      getList()
      let { data } = await getAttentionGroupInfoPageAll();
      state.groupList = data;
    })
    return {
      state,
      statusList,
      edit,
      reset,
      search,
      handlePageChange,
      handleSizeChange,
      // del,
      close,
      // groupList
      handleClick,
      getDateTimeStr,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
