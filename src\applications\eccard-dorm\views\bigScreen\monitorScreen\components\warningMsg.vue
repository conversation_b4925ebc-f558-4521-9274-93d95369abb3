<template>
  <div class="repair-msg">
    <div class="repair-title">宿舍预警</div>
    <div class="table-head">
      <div class="table-head-item">房间号</div>
      <div class="table-head-item">水表余额</div>
      <div class="table-head-item">电表余额</div>
      <!-- <div class="table-head-item">门锁电量</div> -->
      <div class="table-head-item">预警消息</div>
    </div>
    <div class="carousel" ref="carousel">
      <el-carousel height="403px" direction="vertical" :autoplay="true" :interval="5000" indicator-position="none"
        v-if="state.dataList.length">
        <el-carousel-item v-for="(item, index) in Math.ceil(state.dataList.length / 13)" :key="index" style="color:#fff">
          <div class="carousel-item">
            <div class="table-body" v-for="(v, i) in state.dataList.slice(index * 13, (index + 1) * 13) " :key="i">
              <div class="table-body-item">{{ v.roomNo }}</div>
              <div class="table-body-item">{{ v.waterBalance.toFixed(2) }}</div>
              <div class="table-body-item">{{ v.powerBalance.toFixed(2) }}</div>
              <!-- <div class="table-body-item">50%</div> -->
              <div class="table-body-item">{{ v.result }}</div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <div class="none-data" v-else>
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { reactive, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from "vue-router"
import { earlyWarning } from "@/applications/eccard-dorm/api.js"
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      timer: null,
      dataList: [],
    })
    const getData = async () => {
      let { data } = await earlyWarning(route.params)
      state.dataList = data
    }

    onMounted(() => {


      getData()
      state.timer = setInterval(() => {
        getData()
      }, 10000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      state
    }
  }
}
</script>
<style lang="scss" scoped>
.repair-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .repair-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .table-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    background: #1e408a;

    .table-head-item {
      text-align: center;
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      width: 80px;

      &:last-child {
        flex: 1;
      }
    }
  }

  .carousel {
    height: 100%;
  }

  .carousel-item {
    height: 100%;
  }

  .table-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    box-sizing: border-box;
    height: 31px;

    .table-body-item {
      text-align: center;
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 80px;

      &:last-child {
        flex: 1;
      }
    }
  }
}

.el-carousel {
  width: 100%;
  height: 100%;
}

.none-data {
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}</style>