<template>
  <div class="padding-box">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="统计日期">
          <el-date-picker v-model="state.requestDate" :default-time="defaultTime" unlink-panels type="daterange" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="按入账时间:">
          <el-checkbox v-model="state.checked" size="large" />
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false" @valueChange="(val) => state.form.areaPath = val.areaPath" />
        </el-form-item>
        <el-form-item label="消费类型">
          <el-select v-model="state.form.consumerType" placeholder="请选择" size="small">
            <el-option v-for="(item, index) in consumerTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="汇总方式">
          <el-cascader v-model="state.summaryModel" :options="summaryModelList" :show-all-levels="true" />
        </el-form-item>
        <el-form-item label="商户">
          <el-select v-model="state.form.merchantId" placeholder="请选择" size="small" clearable>
            <el-option v-for="(item, index) in state.merchantList" :key="index" :label="item.merchantName" :value="item.merchantId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作员">
          <el-select v-model="state.form.operatorList" filterable multiple collapse-tags placeholder="请选择" clearable>
            <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select v-model="state.form.tradeModeList" placeholder="请选择" size="small" multiple collapse-tags clearable>
            <el-option v-for="(item, index) in state.tradeModeList" :key="index" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>

      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="消费交易汇总表" v-loading="state.loading">
      <template #extra>
        <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
        <el-button size="mini" @click="handlePrint" class="btn-purple" icon="el-icon-printer">打印</el-button>
      </template>
      <el-table height="55vh" border :data="state.dataList">
        <el-table-column v-for="item in state.column" :key="item.prop" :prop="item.prop" :label="item.label" align="center" :width="item.width">
          <template #default="scope">
            {{ scope.row[item.prop] === null ? '--' : (typeof scope.row[item.prop] === 'number' ?
                scope.row[item.prop].toFixed(2) :
                scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination v-model:currentPage="state.form.pageNum" v-model:page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" :small="small" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElCascader,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElCheckbox,
  ElMessage
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree";
import areaSelectTree from "@/components/tree/areaSelectTree";
import { dateStr } from "@/utils/date.js";
import { requestDefaultTime, defaultTime } from "@/utils/reqDefaultDate.js";
import { useDict } from "@/hooks/useDict.js";
import { downloadXlsx, print } from "@/utils";
import { getSystemUser, tradeMode, consumerSummary, consumerSummaryExport, consumerSummaryPrint } from "@/applications/eccard-finance/api";
import { getMerchantList, } from "@/applications/eccard-iot/api";
import { onMounted } from "@vue/runtime-core";
const column = [
  { prop: "name", label: "区域/终端/部门/姓名", width: '200px' },
  { prop: "settlementDate", label: "汇总时间" },
  { prop: "breakfastCount", label: "早餐笔数" },
  { prop: "breakfastAmount", label: "早餐金额" },
  { prop: "lunchCount", label: "午餐笔数" },
  { prop: "lunchAmount", label: "午餐金额" },
  { prop: "dinnerCount", label: "晚餐笔数" },
  { prop: "dinnerAmount", label: "晚餐金额" },
  { prop: "supperCount", label: "宵夜笔数" },
  { prop: "supperAmount", label: "宵夜金额" },
  { prop: "otherCount", label: "其他笔数" },
  { prop: "otherAmount", label: "其他金额" },
  { prop: "correctCount", label: "纠错笔数" },
  { prop: "correctAmount", label: "纠错金额" },
  { prop: "subtotalCount", label: "小计笔数" },
  { prop: "subtotalAmount", label: "小计金额" },
]

export default {
  components: {
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElCascader,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElCheckbox,
    "kade-area-select-tree": areaSelectTree,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const consumerTypeList = useDict("CONSUMER_SUMMARY_CONSUMER_TYPE");
    const sumMethodList = useDict("CONSUMER_SUMMARY_SETTLEMENT_METHOD");
    const sumTypeList = useDict("CONSUMER_SUMMARY_MONTHDAY_TYPE");
    const state = reactive({
      loading: false,
      column,
      summaryModel: ["C_SUMMARY_SETTLE_AREA", "MONTH"],
      form: {
        consumerType: "MONEY",
        pageSize: 10,
        pageNum: 1,
      },
      checked: true,
      dataList: [],
      total: 0,
      requestDate: requestDefaultTime(),
      systemUserList: [],
      tradeModeList: [],
      merchantList: []
    });
    const getTradeModeList = () => {
      tradeMode({ costType: 201 }).then((res) => {
        state.tradeModeList = res.data
      });
    };
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        console.log(res)
        state.systemUserList = res.data
      })
    }
    //获取商户列表
    const queryMerchantList = async () => {
      let { data } = await getMerchantList();
      state.merchantList = data;
    };
    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.settlementStartDate = dateStr(state.requestDate[0]);
        params.settlementEndDate = dateStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择统计日期")
        return false
      }
      if (state.summaryModel && state.summaryModel.length) {
        params.settlementMethod = state.summaryModel[0]
        params.monthDay = state.summaryModel[1]
      } else {
        delete params.settlementMethod
        delete params.monthDay
      }
      if (params.operatorList && params.operatorList.length) {
        params.operatorList = params.operatorList.join(",")
      } else {
        delete params.operatorList
      }
      if (params.tradeModeList && params.tradeModeList.length) {
        params.tradeModeList = params.tradeModeList.join(",")
      } else {
        delete params.tradeModeList
      }
      params.timeType = state.checked ? "BY_CREATE_DATE" : "BY_TRADE_DATE";
      return params
    }

    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let { data: { generate, pageInfo: { list, total } } } = await consumerSummary(params)
        state.dataList = list
        state.total = total
        if (list.length) {
          state.dataList.push(generate)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }

    };
    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        consumerType: "MONEY",
        pageNum: 1,
        pageSize: 10
      }
      state.requestDate = requestDefaultTime()
      state.summaryModel = ["C_SUMMARY_SETTLE_AREA", "MONTH"]
    };
    const changeDate = () => { };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1,
        state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val,
        getList()
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await consumerSummaryExport(params)
        downloadXlsx(res, '消费交易汇总表.xlsx')
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handlePrint = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let { data, code } = await consumerSummaryPrint(params)
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    onMounted(() => {
      // getList();
      querySystemUser();
      getTradeModeList()
      queryMerchantList()
    });
    return {
      defaultTime,
      consumerTypeList,
      summaryModelList: sumMethodList.map(item => {
        return {
          ...item, children: sumTypeList
        }
      }),
      column,
      state,
      search,
      reset,
      changeDate,
      handleSizeChange,
      handleCurrentChange,
      exportClick,
      handlePrint
    };
  },
};
</script>