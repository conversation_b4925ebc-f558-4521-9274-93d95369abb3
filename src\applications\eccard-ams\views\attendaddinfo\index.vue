<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="用户编号">
          <el-input v-model="state.form.userNum" placeholder="用户编号搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="用户名称">
          <el-input v-model="state.form.userName" placeholder="用户名称搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
        <el-form-item label="人员状态">
          <el-select v-model="state.form.userPostStatus" :clearable="true">
            <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>

    <kade-table-wrap title="出勤补录列表">
      <template #extra>
        <el-button size="mini" type="success" :icon="Plus" @click="edit('','add')">新增补录</el-button>
        <el-button size="mini" type="primary" :icon="Download" @click="handleClick()">导出</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="审核状态" prop="exmResult" align="center">
          <template #default="scope">
            <span style="color:#02D200" v-if="scope.row.exmResult == '0'">待审核</span>
            <span style="color:#3399FF" v-if="scope.row.exmResult == '1'">已通过</span>
            <span style="color:#FF3F3F" v-if="scope.row.exmResult == '2'">未通过</span>
          </template>
        </el-table-column>
        <el-table-column label="审核方式" prop="ataExamType" align="center"></el-table-column>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center"></el-table-column>
        <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
        <el-table-column label="开始时间" prop="startDate" align="center"></el-table-column>
        <el-table-column label="结束时间" prop="endDate" align="center"></el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="edit(scope.row , 'edit')" type="text" size="mini" v-if="scope.row.exmResult != '1'">编辑</el-button>
            <el-button @click="edit(scope.row , 'info')" type="text" size="mini" v-if="scope.row.exmResult == '1'">详情</el-button>
            <el-button @click="del(scope.row , 'edit')" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <attend-group-edit :modelValue="state.isEdit" :title="state.type == 'add' ? '新增出勤补录' : (state.type == 'edit' ? '编辑出勤补录' : '出勤补录详情')"
                       :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
  </kade-route-card>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { downloadXlsx } from "@/utils"
  import { getAttentionAddInfoPage , delAttentionAddInfo , downLoadFile } from "@/applications/eccard-ams/api";
  import attendGroupEdit from "./components/edit.vue"
  import deptSelectTree from '@/components/tree/deptSelectTree'
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessage,
    ElMessageBox
  } from 'element-plus';

  export default {
    components: {
      attendGroupEdit,
      ElForm,
      ElFormItem,
      ElInput,
      ElSelect,
      ElOption,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      "kade-dept-select-tree":deptSelectTree,
    },
    setup() {
      const statusList = useDict("BASE_USER_STATE");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          userNum : '',
          userName: '',
          orgId: '',
          userPostStatus : '',
          pageNum:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionAddInfoPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }

      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          pageNum:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const handleClick=async ()=>{
        let res = await downLoadFile("/eccard-ams/api/ams/attention-addinfo/export" , state.form);
        downloadXlsx(res, "出勤补录列表.xlsx");
      }
      const del=async (row)=>{
        ElMessageBox.confirm(`确定删除当前考勤补录信息？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code,message}=await delAttentionAddInfo(row.ataId)
          if(code===0){
            ElMessage.success(message)
            getList()
          }
        });
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        statusList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        del,
        close,
        handleClick
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
