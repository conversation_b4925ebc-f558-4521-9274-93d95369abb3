<template>
  <div class="padding-box">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline label-width="100px" size="mini">
        <el-form-item label="交易时间">
          <el-date-picker unlink-panels v-model="state.requestDate" type="datetimerange" :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期" :clearable="false" />
        </el-form-item>
        <el-form-item label="操作员">
          <el-select v-model="state.form.operatorList" filterable multiple collapse-tags placeholder="请选择" clearable>
            <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户编号">
          <el-input v-model="state.form.userCode" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input v-model="state.form.userName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptId" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>

        <el-form-item label="交易钱包">
          <el-select v-model="state.form.walletCodeList" multiple collapse-tags placeholder="请选择" size="small" clearable>
            <el-option v-for="(item, index) in state.walletActiveList" :key="index" :label="item.walletName" :value="item.walletCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select v-model="state.form.costTypeList" multiple collapse-tags placeholder="请选择" size="small" clearable>
            <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costTypeName" :value="item.costType">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="出纳明细报表" v-loading="state.loading">
      <template #extra>
        <el-button class="btn-blue" icon="el-icon-daochu" size="mini" @click="exportClick()">导出</el-button>
      </template>
      <el-table :data="state.dataList" border height="55vh">
        <el-table-column v-for="(item) in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination :currentPage="state.form.pageNum" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" :small="small" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
  </div>
</template>

<script>
import { reactive } from "@vue/reactivity";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import deptSelectTree from "@/components/tree/deptSelectTree";
import {
  // getWalletActiveList,
  cashierDetailCostTypeList,
  cashierDetailWalletList,
  getSystemUser,
  cashierDetail,
  cashierDetailExport,
} from "@/applications/eccard-finance/api";
import { downloadXlsx } from "@/utils";
import { onMounted } from "@vue/runtime-core";
import { timeStr } from "@/utils/date.js";
import { requestDefaultTime, defaultTime } from "@/utils/reqDefaultDate";

const column = [
  { label: "操作员", prop: "operatorName", width: "" },
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "交易时间", prop: "tradeDate", width: "" },
  { label: "交易钱包", prop: "walletName", width: "" },
  { label: "交易类型", prop: "costTypeName", width: "" },
  { label: "交易金额", prop: "tradeAmount", width: "", render: (val) => Number(val).toFixed(2) },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
]


export default {
  components: {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElDatePicker,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      requestDate: requestDefaultTime(),
      walletActiveList: [],
      form: {
        pageNum: 1,
        pageSize: 10,
        operatorList: [],
        costTypeList: [],
        walletCodeList: []
      },
      dataList: [],
      total: 0,
      costTypeList: [],
      systemUserList: [],
    });
    const getParams = () => {
      let params = { ...state.form };
      if (state.requestDate && state.requestDate.length) {
        params.startDateTime = timeStr(state.requestDate[0]);
        params.endDateTime = timeStr(state.requestDate[1]);
      } else {
        delete params.startDateTime;
        delete params.endDateTime;
      }
      if (params.operatorList && params.operatorList.length) {
        params.operatorList = params.operatorList.join(",");
      } else {
        delete params.operatorList;
      }
      if (params.costTypeList && params.costTypeList.length) {
        params.costTypeList = params.costTypeList.join(",");
      } else {
        delete params.costTypeList;
      }
      if (params.walletCodeList && params.walletCodeList.length) {
        params.walletCodeList = params.walletCodeList.join(",");
      } else {
        delete params.walletCodeList;
      }
      return params
    }
    const getList = () => {
      let params = getParams()
      state.loading = true
      cashierDetail(params).then((res) => {
        let {
          data: {
            generate,
            pageInfo: { list, total },
          },
        } = res;
        state.dataList = list;
        state.total = total;
        if (list.length > 0) {
          state.dataList.push({ ...generate });
        }
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    };

    const search = () => {
      getList();
    };
    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 10,
      };
      state.requestDate = requestDefaultTime()
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleCurrentChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const queryWalletActiveList = () => {
      cashierDetailWalletList().then((res) => {
        state.walletActiveList = res.data;
      });
    };
    const querycostTypeList = () => {
      cashierDetailCostTypeList().then((res) => {
        console.log(res);
        state.costTypeList = res.data;
      });
    };
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };
    const exportClick = async () => {
      let params = getParams()
      state.loading = true
      try {
        let res = await cashierDetailExport(params);
        downloadXlsx(res, "出纳明细报表.xlsx");
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    onMounted(() => {
      // getList();
      queryWalletActiveList();
      querycostTypeList();
      querySystemUser();
    });
    return {
      column,
      defaultTime,
      state,
      search,
      exportClick,
      reset,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>