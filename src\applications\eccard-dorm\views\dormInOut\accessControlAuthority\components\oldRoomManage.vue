<template>
  <el-dialog :model-value="modelValue" title="旧门锁管理" width="1200px" :before-close="handleClose">

    <div class="padding-box">
      <el-form inline label-width="80px" size="mini">
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
        <el-button type="primary" size="mini" @click="handleSearch">搜索</el-button>
      </el-form>
      <el-table :data="state.dataList" border v-loading="state.loading">
        <el-table-column label="房间" prop="roomName" align="center">
          <template #default="scope">
            {{ `${scope.row.buildName}>${scope.row.unitNum}单元>${scope.row.floorNum}楼>${scope.row.roomName}` }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template #default="scope">
            <el-button type="text" class="green" @click="handleOp(scope.row,1)">清除权限</el-button>
            <el-button type="text" class="green" @click="handleOp(scope.row,2)">校正时间</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentPage" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, watch } from 'vue'
import { ElDialog, ElButton, ElForm, ElTable, ElTableColumn, ElPagination, ElMessage, ElMessageBox } from "element-plus"
import { getRoomList, roomTimeSync, roomClearAuth } from "@/applications/eccard-dorm/api";
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
const linkageData = {
  area: { label: '区域', valueKey: 'areaId', key: 'id' },
  building: { label: '楼栋', valueKey: 'buildId' },
  unit: { label: '单元', valueKey: 'unitNum' },
}
export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    }
  },
  components: {
    ElDialog,
    ElButton, ElForm, ElTable, ElTableColumn, ElPagination,
    "kade-linkage-select": linkageSelect
  },
  setup(props, context) {
    console.log("props", props);

    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 10,
        roomKey: 2,
      },
      datalist: [],
      total: 0
    })
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
    }
    const getList = async () => {
      state.loading = true
      try {
        let { data: { list, total } } = await getRoomList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleOp = async (row, type) => {


      ElMessageBox.confirm(`确认对${row.roomName}进行${type == 1 ? '权限清除' : '校正时间'}？`, `提示`, {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        let fn = type == 1 ? roomClearAuth : roomTimeSync
        let { code, message } = await fn(row.id)
        if (code === 0) {
          ElMessage.success(message)
        }
      })
    }
    const handleSearch = () => {
      getList()
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentPage = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    }
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = {
          currentPage: 1,
          pageSize: 10,
          roomKey: 2,
        }
        getList()
      }
    })
    return {
      linkageData,
      getRoomList,
      state,
      linkageChange,
      handleOp,
      handleSearch,
      handleClose,
      handleSizeChange,
      handleCurrentPage
    }
  }
}
</script>
