<template>
  <div class="person-msg">
    <div class="statistics">
      <kade-label-box v-for="(item, index) in fieldList" :key="index" class="label-box">
        <div class="label">{{ item.label }}</div>
        <div class="value">{{ state.totalData[item.field] }}</div>
      </kade-label-box>
    </div>
    <div class="monitor">
      <div class="monitor-title">监控预警</div>
      <div class="monitor-box1">
        <kade-border-box v-for="(item, index) in state.recordData1" :key="index" class="monitor-box1-item">
          <img :src="item.snapPhotoUrl" alt="">
          <div class="msg-box">
            <div class="name">{{ item.userName }}</div>
            <div class="msg">房间：{{ item.roomNo }}</div>
            <div class="msg">班级：{{ item.deptName }}</div>
            <div class="msg">方向：{{ item.passDirection }}</div>
            <div class="msg">时间：{{ timeStr(item.passTime) }}</div>
            <!-- <div class="msg">体温：38℃</div> -->
          </div>
        </kade-border-box>
      </div>
      <div class="monitor-box2">
        <kade-label-box v-for="(item, index) in state.recordData2" :key="index" class="label-box">
          <img :src="item.snapPhotoUrl" alt="" />
          <div class="msg-box">
            <div class="msg">{{ item.userName }}</div>
            <div class="msg">方向：{{ item.passDirection }}</div>
            <div class="msg">时间：{{ timeStr(item.passTime) }}</div>
            <!-- <div class="msg">状态：陌生人</div> -->
          </div>
        </kade-label-box>
      </div>
      <div class="none-data" v-if="!(state.recordData1.length + state.recordData2.length)">
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { reactive, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from "vue-router"
import { timeStr } from "@/utils/date.js"
import { screenTotal, screenRecord } from "@/applications/eccard-dorm/api.js"

import labelBox from "../../components/labelBox.vue"
import borderBox from "../../components/borderBox.vue"

const fieldList = [
  { label: "房间数量", field: "roomCount" },
  { label: "空余房间", field: "roomNullCount" },
  { label: "床位数量", field: "bedCount" },
  { label: "空余床位", field: "bedNullCount" },
  { label: "入住人数", field: "stayCount" },
  { label: "请假人数", field: "leaveCount" },
  { label: "在寝人数", field: "inCount" },
  { label: "离寝人数", field: "outCount" },
]
export default {
  components: {
    "kade-label-box": labelBox,
    "kade-border-box": borderBox,
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      timer: null,
      totalData: {
        bedCount: 0,
        bedNullCount: 0,
        inCount: 0,
        leaveCount: 0,
        outCount: 0,
        roomCount: 0,
        roomNullCount: 0,
        stayCount: 0,
      },
      recordData1: [],
      recordData2: [],
    })
    const getTotal = async () => {
      let { data } = await screenTotal(route.params)
      state.totalData = data
    }
    const getRecord = async () => {
      let { data } = await screenRecord(route.params)
      state.recordData1 = data.slice(0, 4)
      state.recordData2 = data.slice(4, 11)
    }
    onMounted(() => {
      getTotal()
      getRecord()
      state.timer = setInterval(() => {
        getTotal()
        getRecord()
      }, 10000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      timeStr,
      fieldList,
      state
    }
  }
}
</script>
<style scoped lang="scss">
.person-msg {
  padding: 10px 0;
  height: 100%;
  display: flex;
  flex-direction: column;

  .statistics {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    box-sizing: border-box;

    .label-box {
      box-sizing: border-box;
      width: 23%;
      margin-bottom: 10px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .label {
        padding-right: 50px;
        font-weight: 400;
        font-size: 14px;
        color: #0ee4f9;
        margin-bottom: 10px;
      }

      .value {
        font-weight: 700;
        font-size: 30px;
        color: #fe7007;
      }
    }
  }

  .monitor {
    min-height: 500px;
    padding: 0px 10px;

    .monitor-title {
      font-weight: 700;
      font-size: 18px;
      color: #0ee4f9;
      margin: 10px 0 10px;
    }

    .monitor-box1 {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .monitor-box1-item {
        width: 48%;
        margin-bottom: 20px;
        display: flex;
        padding: 10px;

        img {
          width: 137px;
          height: 164px;
          margin-right: 20px;
        }

        .msg-box {
          font-size: 16px;
          color: #ffffff;
          line-height: 28px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          .name {
            margin-bottom: 5px;
            font-weight: 700;
            font-size: 28px;
            color: #50ffff;
          }
        }
      }
    }
    .monitor-box2 {
      margin-bottom: 10px;
      display: flex;
      // justify-content: space-between;

      .label-box {
        text-align: center;
        padding: 10px 5px;
        width: 130px;
        margin-right: 10px;

        &:last-child {
          margin-right: 0;
        }

        img {
          width: 107px;
          height: 120px;
        }

        .msg-box {
          font-size: 12px;
          color: #ffffff;
          line-height: 18px;
          text-align: left;
          padding: 0 10px;
        }
      }
    }
  }
}
.none-data {
  margin-top: 200px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>