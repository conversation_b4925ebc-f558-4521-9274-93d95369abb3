<template>
  <div class="not-returned-msg">
    <div class="not-returned-title">未归人员</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="10000" indicator-position="none"
      v-if="state.dataList.length">
      <el-carousel-item v-for="(item, index) in Math.ceil(state.dataList.length / 12)" :key="index" style="color:#fff">
        <div class="person-box">
          <div class="inline" v-for="(v, i) in state.dataList.slice(index * 12, (index + 1) * 12) " :key="i">
            <div class="person-item">
              <img :src="v.userPhoto" alt="">
              <div class="msg">
                <div class="name">{{ v.userName }}</div>
                <div class="group">{{ v.deptName }}</div>
                <div class="room">{{ v.roomName }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="none-data" v-else>
      <div>暂无数据</div>
    </div>
  </div>
</template>
<script>
import { onMounted, onBeforeUnmount, reactive } from "vue"
import { useRoute } from "vue-router"
import { requestDefaultTime } from "@/utils/reqDefaultDate.js"
import { timeStr } from "@/utils/date.js"
import { ElCarousel, ElCarouselItem } from "element-plus"
import { notReturnList } from "@/applications/eccard-dorm/api"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      dataList: [],
      timer: null
    })
    const getData = async () => {
      let params = {
        attendancePeriodCode: 4,
        ...route.params,
        beginDate: timeStr(requestDefaultTime()[0]),
        endDate: timeStr(requestDefaultTime()[1]),
      }
      let { data } = await notReturnList(params)
      state.dataList = data
    }
    onMounted(() => {
      getData()
      state.timer = setInterval(() => {
        getData()
      }, 30000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      state
    }
  }
}
</script>
<style lang="scss" scoped>
.not-returned-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .not-returned-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }

  .person-box {
    background: #001034;

    width: 100%;
    height: 100%;
    overflow-y: auto;

    /*     display: flex;
    flex-wrap: wrap; */
    // justify-content: space-between;
    .inline {
      display: inline-block;
      width: 23.5%;
      height: 33%;
      margin-bottom: 1%;
      margin-right: 2%;
      &:nth-child(4n) {
        margin-right: 0;
      }
      &:nth-child(n+8) {
        margin-bottom: 0;
      }
      .person-item {
        height: 100%;
        box-sizing: border-box;
        padding: 5px;
        background: #08266f;
        display: flex;
        align-items: center;
        img {
          width: 82px;
          height: 98px;
        }
        .msg {
          flex: 1;
          text-align: center;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;

          .name {
            font-weight: 700;
            font-size: 20px;
            color: #fff;
          }

          .group {
            font-size: 13px;
            margin: 10px 0;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          .room {
            font-weight: 600;
            font-size: 18px;
            color: #fff;

          }
        }
      }
    }

  }

}

.el-carousel {
  width: 100%;
  height: 100%;
}

.none-data {
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>