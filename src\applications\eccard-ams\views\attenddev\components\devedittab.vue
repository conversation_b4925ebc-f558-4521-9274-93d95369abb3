<template>
  <el-dialog append-to-body="true" width="1200px" :model-value="modelValue" :title="title"  :before-close="update" destroy-on-close="true" :close-on-click-modal="false" :modal="true">
    <el-tabs :model-value="state.selectedIndex">
      <el-tab-pane label="基本信息" name="info">
        <dev-edit :data="state.model"  />
      </el-tab-pane>
      <el-tab-pane label="考勤记录" name="record">
        <dev-attend-info :data="state.model"/>
      </el-tab-pane>
    </el-tabs>

  </el-dialog>
</template>
<script>
import { computed, reactive, ref, watch } from "vue";
import devAttendInfo from "@/applications/eccard-ams/views/attenddev/devattendinfo.vue"
import devEdit from "./devedit.vue"
// import attendEdit from "@/applications/eccard-ams/views/userattend/components/attendedit.vue"

import {
  ElTabPane,
  ElTabs,
  ElDialog
//   ElButton,
//   ElForm,
//   ElFormItem,
//   ElInput,
//   ElMessage,
//   ElSwitch,
//   el-image,
} from "element-plus";
// import { addRole, updateRole } from "@/applications/eccard-basic-data/api";
// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    selectedIndex:{
      type: String,
      default: "info",
    },
    role: {
      type: Object,
      default: () => ({}),
    },
    data:{
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElTabPane,
    ElTabs,
    ElDialog,
    devAttendInfo,
    devEdit,
    // "el-button": ElButton,
    // "el-form": ElForm,
    // "el-form-item": ElFormItem,
    // "el-input": ElInput,
    // "el-switch": ElSwitch,
    // 'el-tabs': ElTabs,
    // 'el-tab-pane': ElTabPane,
    // 'el-image' :
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: props.data,
      selectedIndex : props.selectedIndex,
    });
    const rules = {
      roleName: [
        { required: true, message: "请输入类别名称" },
        { max: 20, message: "类别名称不能超过20个字符" },
      ],
    };
    // const getDetails = async () => {
    //   let params = {
    //     userCode: store.state.userInfo.rowData.userCode,
    //     userId: store.state.userInfo.rowData.id,
    //   };
    //   let { data } = await getBaseUserInfo(params);
    //   state.form = data;
    //   state.form.userRole = Number(state.form.userRole);
    //   state.form.userBirthPlace = Number(state.form.userBirthPlace);
    // };

    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    // const submit = () => {
    //   formRef.value?.validate(async (valid) => {
    //     if (valid) {
    //       try {
    //         btnLoading.value = true;
    //         const fn = props.role?.id ? updateRole : addRole;
    //         const { message, code } = await fn(state.model);
    //         if (code === 0) {
    //           ElMessage.success(message);
    //         } else {
    //           ElMessage.error(message);
    //         }
    //         context.emit("update:modelValue", false);
    //         context.emit("change");
    //       } catch (e) {
    //         throw new Error(e.message);
    //       } finally {
    //         btnLoading.value = false;
    //       }
    //     }
    //   });
    // };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    watch(
      () => props.data,
      (n) => {
        if (n) {
          state.model = props.data;
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      // submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>
