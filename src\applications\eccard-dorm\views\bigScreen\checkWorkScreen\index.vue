<template>
  <div class="box" ref="ScaleBox">
    <kade-header-box :title="route.params.buildName + route.params.unitNum+'单元晚归寝考勤监控大屏'" />
    <div class="main">
      <el-row :gutter="15" style="height: 100%;">
        <el-col :span="10" class="person-msg">
          <kade-border-box style="flex:1;margin-bottom: 20px;">
            <kade-person-msg />
          </kade-border-box>
          <kade-border-box style="height: 50%;">
            <kade-not-returned />
          </kade-border-box>
        </el-col>
        <el-col :span="14">
          <kade-border-box style="height: 100%;">
            <kade-room-msg />
          </kade-border-box>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { reactive, onMounted, onBeforeUnmount } from "vue"
import { useRouter, useRoute } from "vue-router"
import { setScreenCompatibility } from "@/utils"
import { hourStr, time_to_sec } from "@/utils/date.js"
import { ElCol, ElRow, } from "element-plus"
import headerBox from "../components/headerBox.vue"
import borderBox from "../components/borderBox.vue"
import personMsg from "./components/personMsg.vue"
import notReturned from "./components/notReturned.vue"
import roomMsg from "./components/roomMsg.vue"

export default {
  name: "checkWorkScreen",
  components: {
    ElCol, ElRow,
    "kade-header-box": headerBox,
    "kade-border-box": borderBox,
    "kade-person-msg": personMsg,
    "kade-not-returned": notReturned,
    "kade-room-msg": roomMsg,

  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const state = reactive({
      timer: null,
      timerOut: null,
    })
    onMounted(() => {
      state.timerOut = setTimeout(() => {
        state.timer = setInterval(() => {

          let nowTimeSec = time_to_sec(hourStr(new Date))
          let startTimeSec = time_to_sec(CONFIG.SCREEN_CKECK_WORK_TIME[0])
          let endTimeSec = time_to_sec(CONFIG.SCREEN_CKECK_WORK_TIME[1])
          console.log(nowTimeSec, startTimeSec, endTimeSec);
          if (nowTimeSec < startTimeSec || nowTimeSec > endTimeSec) {
            router.push({
              name: 'MonitorScreen',
              params: {
                ...route.params
              }
            })
          }
        }, 1000);
      }, CONFIG.SCREEN_TIMEOUT);
      setScreenCompatibility();
      window.addEventListener('resize', () => {
        setScreenCompatibility();
      });
    })
    onBeforeUnmount(() => {
      if (state.timerOut) {
        clearInterval(state.timerOut)
      }
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      route,
      state,
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  background: #001034;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .main {
    flex: 1;
    padding: 0 20px 20px;

    .person-msg {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>