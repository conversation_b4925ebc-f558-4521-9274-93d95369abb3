<template>
  <div class="divmain">
    <el-row :gutter="15">
      <el-col :sm="24">
        <h5 style="font-size:18px;margin: 10px 10px 25px 0px;">考勤一张图</h5>
      </el-col>
    </el-row>

    <el-row :gutter="15" class="box" style="padding-top:10px">
      <el-col :sm="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>今日出勤统计</span>
            </div>
          </template>
          <el-row :gutter="2" class="box1">
            <el-col :sm="4" class="box1-1">
              <el-row :gutter="2">
                <el-col :sm="24" style="text-align:center">
                  <span class="box-second-title">考勤总人数</span>
                </el-col>
              </el-row>
              <el-row :gutter="2" class="box-content">
                <el-col :sm="16">
                  <div class="box-left-icon">
                    <UserFilled style="width: 1em; height: 1em; margin-right: 8px" />
                  </div>
                </el-col>
                <el-col :sm="8" class="box1-2">
                  <span class="boxkeyword">{{state.form.atrNum ? state.form.atrNum : 0}}</span>
                </el-col>
              </el-row>
            </el-col>

            <el-col :sm="4" class="box1-1">
              <el-row :gutter="2">
                <el-col :sm="24" style="text-align:center">
                  <span class="box-second-title">实际出勤人数</span>
                </el-col>
              </el-row>
              <el-row :gutter="2" class="box-content">
                <el-col :sm="16">
                  <div class="box-left-icon" style="color:#02d200">
                    <Avatar style="width: 1em; height: 1em; margin-right: 8px" />
                  </div>
                </el-col>
                <el-col :sm="8" class="box1-2">
                  <span class="boxkeyword">{{state.form.atrAttendNum ? state.form.atrAttendNum : 0 }}</span>
                </el-col>
              </el-row>
            </el-col>

            <el-col :sm="4" class="box1-1">
              <el-row :gutter="2">
                <el-col :sm="24" style="text-align:center">
                  <span class="box-second-title">今日请假人数</span>
                </el-col>
              </el-row>
              <el-row :gutter="2" class="box-content">
                <el-col :sm="16">
                  <div class="box-left-icon" style="color:#ff3f3f">
                    <User style="width: 1em; height: 1em; margin-right: 8px" />
                  </div>
                </el-col>
                <el-col :sm="8" class="box1-2">
                  <span class="boxkeyword">{{state.form.atrAbseNum ? state.form.atrAbseNum : 0}}</span>
                </el-col>
              </el-row>
            </el-col>

            <el-col :sm="4" class="box1-1">
              <el-row :gutter="2">
                <el-col :sm="24" style="text-align:center">
                  <span class="box-second-title">今日迟到人数</span>
                </el-col>
              </el-row>
              <el-row :gutter="2" class="box-content">
                <el-col :sm="16">
                  <div class="box-left-icon" style="color:#ff8726">
                    <AlarmClock style="width: 1em; height: 1em; margin-right: 8px" />
                  </div>
                </el-col>
                <el-col :sm="8" class="box1-2">
                  <span class="boxkeyword">{{state.form.atrLateNum ? state.form.atrLateNum : 0}}</span>
                </el-col>
              </el-row>
            </el-col>

            <el-col :sm="4" class="box1-1">
              <el-row :gutter="2">
                <el-col :sm="24" style="text-align:center">
                  <span class="box-second-title">今日缺勤人数</span>
                </el-col>
              </el-row>
              <el-row :gutter="2" class="box-content">
                <el-col :sm="16">
                  <div class="box-left-icon" style="color:#405aff">
                    <Stopwatch style="width: 1em; height: 1em; margin-right: 8px" />
                  </div>
                </el-col>
                <el-col :sm="8" class="box1-2">
                  <span class="boxkeyword">{{state.form.atrLeaveNum ? state.form.atrLeaveNum : 0}}</span>
                </el-col>
              </el-row>
            </el-col>

            <el-col :sm="4" class="box1-1">
              <el-row :gutter="2">
                <el-col :sm="24" style="text-align:center">
                  <span class="box-second-title">今日加班人数</span>
                </el-col>
              </el-row>
              <el-row :gutter="2">
                <el-col :sm="16">
                  <div class="box-left-icon" style="color:#ff6f43">
                    <Monitor style="width: 1em; height: 1em; margin-right: 8px" />
                  </div>
                </el-col>
                <el-col :sm="8" class="box1-2">
                  <span class="boxkeyword">{{state.form.atrOverNum ? state.form.atrOverNum : 0}}</span>
                </el-col>
              </el-row>
            </el-col>

          </el-row>
        </el-card>

      </el-col>
    </el-row>

    <el-row :gutter="15" class="box">
      <el-col :sm="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>近7日出勤趋势</span>
              <span style="float:right">单位：人</span>
            </div>
          </template>
          <div id="div_attendanceChart" class="box-card-content">
          </div>
        </el-card>
      </el-col>

      <el-col :sm="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>近7日迟到统计</span>
              <span style="float:right">单位：人</span>
            </div>
          </template>
          <div id="div_lateChart" class="box-card-content">
          </div>
        </el-card>
      </el-col>

      <el-col :sm="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>近7日缺勤统计</span>
              <span style="float:right">单位：人</span>
            </div>
          </template>
          <div id="div_absenceChart" class="box-card-content">
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="15" class="box">
      <el-col :sm="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>近7日请假趋势</span>
              <span style="float:right">单位：人</span>
            </div>
          </template>
          <div id="div_leaveChart" class="box-card-content">
          </div>
        </el-card>
      </el-col>

      <el-col :sm="8">

        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>近7日加班时长统计</span>
              <span style="float:right">单位：分钟</span>
            </div>
          </template>
          <div id="div_overTimeChart" class="box-card-content">
          </div>
        </el-card>

      </el-col>

      <el-col :sm="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>考勤设备状态统计</span>
              <span style="float:right">单位：台</span>
            </div>
          </template>
          <div id="div_devStatusChart" class="box-card-content">
          </div>
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>
<style lang="scss" scoped>
  .box-second-title{
    font-size:14px;
    color:#333333;
  }
  .box-left-icon{
    font-size: 40px;
    color: #3399ff;
    margin-top: 20px;
    text-align: center;
  }
  .box-content{
    height:70px;
    border-right: 1px ridge #ccc;
  }
  .boxkeyword{
    position: absolute;
    right: 22px;
    bottom: -6px;
    font-size: 36px;
    font-weight: 500;
  }
  .box{
    padding-top:25px;
  }
  .box-card-content{
    width:100%;
    height:300px;
  }

  .divmain {
    padding:15px 20px 20px 20px;
  }
</style>
<style>
  #app{
    overflow:auto;
  }
</style>
<script>
  import {
    ElRow,
    ElCol,
    // ElButton,
    ElCard,
    // ElForm,
    // ElFormItem,
    // ElInput,
    // ElSelect,
    // ElOption,
    ElMessage,
    // ElDatePicker,
  } from "element-plus";
import { reactive, onMounted ,nextTick} from "vue";
import { getCurrentDayAttentReport, getCurrentSevenDayReport } from "@/applications/eccard-ams/api";
import * as echarts from "echarts";

export default {
  components: {
    "el-row": ElRow,
    "el-col": ElCol,
    // "el-button": ElButton,
    "el-card": ElCard,
    // "el-form": ElForm,
    // "el-form-item": ElFormItem,
    // "el-input": ElInput,
    // "el-select": ElSelect,
    // "el-option": ElOption,
    // "el-date-picker": ElDatePicker,
    // "kade-single-image-upload": SingleImageUpload,
    // "kade-avatar": Avatar,
    // "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const chartOptions = reactive({
      attendanceChartOption:null,
      lateChartOption:null,
      absenceChartOption:null,
      leaveChartOption:null,
      overTimeChartOption:null,
      devStatusChartOption:null,
    });
    const state = reactive({
      form:{
        atrNum:0,
        atrAttendNum:0,
        atrAbseNum:0,
        atrLateNum:0,
        atrLeaveNum:0,
        atrOverNum:0,
        atrDevOnum:0,
        atrDevDnum:0,
      }
    });

    const getList = async () => {
      let tempObj = await getCurrentDayAttentReport();
      if(tempObj.code == 0){
        //成功
        state.form = tempObj.data;

        state.form["atrDevOnum"] = tempObj.data.devOnLineNum;
        state.form["atrDevDnum"] = tempObj.data.devOffLineNum;
      }else{
        //失败
        // ElMessage.warning("查询数据失败，请刷新页面重试！");
      }

      let { code, data } = await getCurrentSevenDayReport();
      // let { code, data } = {code:0, data:{list:[
      //     {atrDate:'20220811' , atrAttendNum:23 , atrLateNum:43,atrCompassAbseNum:345,atrSickAbseNum:54,atrAnnualAbseNum:565,atrOverTimes:45},
      //     {atrDate:'20220812' , atrAttendNum:43, atrLateNum:34,atrCompassAbseNum:34,atrSickAbseNum:68,atrAnnualAbseNum:454,atrOverTimes:33},
      //     {atrDate:'20220813' , atrAttendNum:34, atrLateNum:23,atrCompassAbseNum:55,atrSickAbseNum:54,atrAnnualAbseNum:574,atrOverTimes:56},
      //     {atrDate:'20220814' , atrAttendNum:64, atrLateNum:56,atrCompassAbseNum:22,atrSickAbseNum:67,atrAnnualAbseNum:367,atrOverTimes:43},
      //     {atrDate:'20220815' , atrAttendNum:22, atrLateNum:11,atrCompassAbseNum:56,atrSickAbseNum:89,atrAnnualAbseNum:54,atrOverTimes:67},
      //     {atrDate:'20220816' , atrAttendNum:55, atrLateNum:5,atrCompassAbseNum:89,atrSickAbseNum:22,atrAnnualAbseNum:77,atrOverTimes:34},
      //     {atrDate:'20220817' , atrAttendNum:67, atrLateNum:3,atrCompassAbseNum:24,atrSickAbseNum:52,atrAnnualAbseNum:33,atrOverTimes:66},
      //     {atrDate:'20220818' , atrAttendNum:88, atrLateNum:67,atrCompassAbseNum:56,atrSickAbseNum:90,atrAnnualAbseNum:908,atrOverTimes:78},
      //   ]}};
      if(code == 0){
        //成功
        //读取近7日出勤趋势
        chartOptions.attendanceChartOption = {
          color: '#5b8ff9',
          tooltip: {
            trigger: 'axis',
            formatter:
              '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0} </br> 实际出勤 {c0}人'
          },
          grid: {
            show:true,
            top: 10,
            bottom: 20,
            left: 30,
            right: 0
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.map((d) => d.atrDate.toString().substring(5 , 7) + "-" + d.atrDate.toString().substring(8 , 10))
            }
          ],
          yAxis: [
            {
              type: 'value',
            }
          ],
          series: [
            {
              type: 'line',
              smooth: true,
              data: data.map((d) => d.atrAttendNum),
              label: {
                normal: {
                  show: true,
                }
              }
            }
          ]
        };

        //读取近7日迟到统计
        chartOptions.lateChartOption = {
          color: '#74a0fa',
          tooltip: {
            trigger: 'axis',
            formatter:
              '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0} </br> 迟到 {c0}人'
          },
          grid: {
            show:true,
            top: 10,
            bottom: 20,
            left: 30,
            right: 0
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.map((d) => d.atrDate.toString().substring(5 , 7) + "-" + d.atrDate.toString().substring(8 , 10))
            }
          ],
          yAxis: [
            {
              show: true,
              type: 'value',
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'bar',
              smooth: true,
              data: data.map((d) => d.atrLateNum),
              label: {
                normal: {
                  show: true,
                  position: 'top',
                }
              }
            }
          ]
        };

        //读取近7日缺勤统计
        chartOptions.absenceChartOption = {
          color: '#349eff',
          tooltip: {
            trigger: 'axis',
            formatter:
              '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0} </br> 缺勤 {c0}人'
          },
          grid: {
            show:true,
            top: 10,
            bottom: 20,
            left: 30,
            right: 0
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.map((d) => d.atrDate.toString().substring(5 , 7) + "-" + d.atrDate.toString().substring(8 , 10))
            }
          ],
          yAxis: [
            {
              show: true,
              type: 'value',
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'line',
              //stack: 'Total',
              areaStyle: {
                opacity:0.5,
              },
              // emphasis: {
              //   focus: 'series'
              // },
              // smooth: true,
              data: data.map((d) => d.atrLeaveNum),
              label: {
                normal: {
                  show: true,
                  position: 'top',
                }
              }
            }
          ]
        };

        //读取近7天请假统计
        chartOptions.leaveChartOption = {
          color:['#5b8ff9','#02d200','#ff8726'],
          tooltip: {
            trigger: 'axis',
            formatter:
              '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0} </br> 请假 {c0}人'
          },
          legend: {
            data: ['事假', '病假', '年假']
          },
          grid: {
            show:true,
            top: 10,
            bottom: 20,
            left: 30,
            right: 0
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.map((d) => d.atrDate.toString().substring(5 , 7) + "-" + d.atrDate.toString().substring(8 , 10))
            }
          ],
          yAxis: [
            {
              show: true,
              type: 'value',
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '事假',
              type: 'line',
              stack: 'Total',
              data: data.map((d) => d.atrCompassAbseNum)
            },
            {
              name: '病假',
              type: 'line',
              stack: 'Total',
              data: data.map((d) => d.atrSickAbseNum)
            },
            {
              name: '年假',
              type: 'line',
              stack: 'Total',
              data: data.map((d) => d.atrAnnualAbseNum)
            }
          ]
        };


        //读取近7日加班时长统计
        chartOptions.overTimeChartOption = {
          color: '#409eff',
          tooltip: {
            trigger: 'axis',
            formatter:
              '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0} </br> 加班时长 {c0}分钟'
          },
          grid: {
            show:true,
            top: 10,
            bottom: 20,
            left: 45,
            right: 0
          },
          xAxis: [
            {
              type: 'value',
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.map((d) => d.atrDate.toString().substring(5 , 7) + "-" + d.atrDate.toString().substring(8 , 10))
            }
          ],
          series: [
            {
              type: 'bar',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              smooth: true,
              data: data.map((d) => d.atrOverTimes),
              label: {
                normal: {
                  show: true,
                  position: 'insideBottomLeft',
                }
              }
            }
          ]
        };

        //读取考勤设备状态统计
        chartOptions.devStatusChartOption = {
          tooltip: {
            trigger: 'item',
          },
          series: [
            {
              name: '设备状态',
              type: 'pie',
              radius: ['40%', '70%'],
              labelLine:{
                normal:{
                  length:15,   	// 指示线长度
                  lineStyle: {
                    color: "#595959"  // 指示线颜色
                  }
                },
              },
              label: {
                normal: {
                  textStyle: {
                    color: '#595959',	// 提示文字颜色
                    fontSize: 14		// 提示文字大小
                  },
                  formatter: '{b}：{d}%'
                }
              },
              data: [
                { value: state.form.atrDevOnum , name: '在线数' },
                { value: state.form.atrDevDnum, name: '离线数' },
              ]
            },
          ]
        };

        var chartAttendanceChart = document.getElementById("div_attendanceChart");
        var myChartAttendanceChart = echarts.init(chartAttendanceChart);

        var chartLateChart = document.getElementById("div_lateChart");
        var myChartLateChart = echarts.init(chartLateChart);

        var chartAbsenceChart = document.getElementById("div_absenceChart");
        var myChartAbsenceChart = echarts.init(chartAbsenceChart);

        var chartLeaveChart = document.getElementById("div_leaveChart");
        var myChartLeaveChart = echarts.init(chartLeaveChart);

        var chartOverTimeChart = document.getElementById("div_overTimeChart");
        var myChartOverTimeChart = echarts.init(chartOverTimeChart);

        var chartDevStatusChart = document.getElementById("div_devStatusChart");
        var myChartDevStatusChart = echarts.init(chartDevStatusChart);

        nextTick(() => {
          myChartAttendanceChart.resize();
          myChartAttendanceChart.setOption(chartOptions.attendanceChartOption, true);

          myChartLateChart.resize();
          myChartLateChart.setOption(chartOptions.lateChartOption, true);

          myChartAbsenceChart.resize();
          myChartAbsenceChart.setOption(chartOptions.absenceChartOption, true);

          myChartLeaveChart.resize();
          myChartLeaveChart.setOption(chartOptions.leaveChartOption, true);

          myChartOverTimeChart.resize();
          myChartOverTimeChart.setOption(chartOptions.overTimeChartOption, true);

          myChartDevStatusChart.resize();
          myChartDevStatusChart.setOption(chartOptions.devStatusChartOption, true);
        });

      }else{
        //失败
        ElMessage.warning("查询数据失败，请刷新页面重试！");
      }
    };

    onMounted(() => {
      getList();
    });
    return {
      state,
      chartOptions,
    };
  },
};
</script>
<style lang="scss" scoped>
.role {
  width: 100%;
  height: 100%;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
