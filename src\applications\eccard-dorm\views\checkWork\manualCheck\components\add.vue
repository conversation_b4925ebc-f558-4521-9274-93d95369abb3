<template>
  <el-dialog :model-value="dialogAdd" title="考勤补卡" width="1400px" :before-close="handleClose">
    <el-form inline size="mini" label-width="80px">
      <kade-linkage-select :data="linkageData" :value="state.form1" @change="linkageChange" />
      <el-form-item label="人员编号">
        <el-input clearable v-model="state.form1.userCode"></el-input>
      </el-form-item>
      <el-form-item label="人员姓名">
        <el-input clearable v-model="state.form1.userName"></el-input>
      </el-form-item>
      <el-form-item label="考勤日期">
        <el-date-picker v-model="state.requestDate" @change="dateChange" type="daterange" range-separator="~" start-placeholder="请选择开始日期"
          end-placeholder="请选择结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="考勤时段">
        <el-select clearable v-model="state.form1.attendancePeriodName" placeholder="全部">
          <el-option v-for="(item, index) in state.checkPeriodIdList" :key="index" :label="item.name" :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-button @click="search()" size="mini" icon="el-icon-search" type="primary">搜索</el-button>
    </el-form>
    <div class="table-box">
      <el-table border highlight-current-row :data="state.data" @row-click="rowClick">
        <el-table-column show-overflow-tooltip label="人员编号" prop="userCode" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="人员姓名" prop="userName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="考勤日期" prop="attendanceDate" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="考勤时段" prop="attendancePeriodName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="考勤结果" prop="attendanceResult" align="center">
          <template #default="scope">
            {{ dictionaryFilter(scope.row.attendanceResult) }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="区域" prop="areaName" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="房间" prop="roomString" align="center"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.currentPage" v-model:page-size="state.pageSize"
          :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
    <div class="form-box">
      <div class="top-box">复核信息</div>
      <el-form inline label-width="80px" size="mini" :rules="rules" :model="state.form" ref="formRef">
        <el-form-item label="人员编号">
          <el-input v-model="state.form.userCode" disabled></el-input>
        </el-form-item>
        <el-form-item label="人员姓名">
          <el-input v-model="state.form.userName" disabled></el-input>
        </el-form-item>
        <!-- <el-form-item label="复核日期">
          <el-input v-model="state.form.userName" disabled></el-input>
        </el-form-item> -->
        <el-form-item label="组织机构">
          <el-input v-model="state.form.deptName" disabled></el-input>
        </el-form-item>
        <el-form-item label="复核时段">
          <el-input v-model="state.form.attendancePeriodName" disabled></el-input>
        </el-form-item>
        <el-form-item label="复核结果" prop="checkResult">
          <el-select clearable v-model="state.form.checkResult" placeholder="全部">
            <el-option v-for="(item, index) in resultList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="复核人员" prop="checkPerson">
          <el-input clearable v-model="state.form.checkPerson" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="text">备注：复核之后需在考勤分析中手动重新分析，产生新的考勤结果</div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue";
import { nextTick, watch } from 'vue';
import { dateStr } from "@/utils/date.js";
import { useDict } from "@/hooks/useDict"
import { attendanceResultList, attendancePeriodList, manualCheckAdd } from '@/applications/eccard-dorm/api.js'
const linkageData = {
  area: { label: "区域", valueKey: "areaPath", key: "areaPath" },
  building: { label: "楼栋", valueKey: "buildId" },
  unit: { label: "单元", valueKey: "unitNum" },
  floor: { label: "楼层", valueKey: "floorNum" },
};
const rules = {
  checkPerson: [
    {
      required: true,
      message: '请输入人员',
      trigger: 'blur',
    },
  ],
  checkResult: [
    {
      required: true,
      message: '请选择结果',
      trigger: 'change',
    },
  ]
}
export default {
  props: {
    dialogAdd: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    'kade-linkage-select': linkageSelect,
  },
  setup(props, context) {
    const resultList = useDict('DORM_ATTENDANCE_RESULT')
    const formRef = ref(null)
    const state = reactive({
      form1: {
        pageSize: 10,
        currentPage: 1
      },
      form: {},
      data: [],
      total: 0,
      requestDate: [],
      checkPeriodIdList: ''
    });
    const linkageChange=(val)=>{
      state.form1={...state.form1,...val}
    }
    const getCheckList = async () => {
      let { data } = await attendancePeriodList()
      state.checkPeriodIdList = data
    }
    const dateChange=(val)=>{
      if(val){
        state.form1.beginDate=dateStr(val[0])
        state.form1.endDate=dateStr(val[1])
      }else{
        delete state.form1.beginDate
        delete state.form1.endDate
      }
    }
    const getAttendanceList = async () => {
      let { data: { list, total } } = await attendanceResultList(state.form1)
      state.data = list
      state.total = total
    };
    const handleClose = () => {
      context.emit("close", false);
      formRef.value.clearValidate()
    };
    const search = () => {
      getAttendanceList()
    };
    const rowClick = (row) => {
      let { userCode, userName, deptName, attendancePeriodName,id} = { ...row }
      state.form = { userCode, userName, deptName, attendancePeriodName,analysisId:id }
    }
    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let {analysisId,checkPerson,checkResult}={...state.form}
          if(!analysisId){
            return ElMessage.error('请选择人员！')
          }
          let params={analysisId,checkPerson,checkResult}
          let { code, message } = await manualCheckAdd(params)
          if (code === 0) {
            ElMessage.success(message)
            context.emit('close', true)
          }
        } else {
          return false
        }
      })
    }
    const handleCurrentChange=(val)=>{
      state.form1.currentPage=val
      getAttendanceList()
    }
    const handleSizeChange=(val)=>{
      state.form1.currentPage=1
      state.form1.pageSize=val
      getAttendanceList()
    }
    watch(() => props.dialogAdd, val => {
      if (val) {
        state.form1 = {
          pageSize: 10,
          currentPage: 1
        }
        state.form={}
        getAttendanceList()
        getCheckList()
      }
      nextTick(() => {
        formRef.value.clearValidate()
      })
    })
    return {
      rules,
      formRef,
      rowClick,
      resultList,
      state,
      dateChange,
      linkageChange,
      search,
      submit,
      linkageData,
      handleClose,
      handleCurrentChange,
      handleSizeChange
    };
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}

.table-box {
  border: 1px solid #eeeeee;
  margin-top: 20px;

  .pagination {
    margin: 10px;
  }
}

.form-box {
  border: 1px solid #eeeeee;
  margin-top: 15px;

  .top-box {
    border-bottom: 1px solid #eeeeee;
    padding: 15px;
  }
}

.text {
  color: rgb(199, 8, 8);
  margin-top: 15px;
  font-size: 12px;
}

:deep(.el-input--mini .el-input__inner) {
  width: 180px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 180px !important;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px !important;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px !important;
}

.el-table {
  border-right: none;
  border-top: none;
}
</style>