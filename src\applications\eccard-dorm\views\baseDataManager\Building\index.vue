<template>
  <!-- 楼栋管理 -->
  <div class="padding-box">
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline size="small" label-width="80px">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="楼栋号或楼栋名称关键字搜索" clearable></el-input>
        </el-form-item>
        <el-form-item label="楼栋类型">
          <el-select v-model="state.form.buildType" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in buildTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域">
          <kade-area-select-tree style="width: 100%" :value="state.form.areaPath" valueKey="areaPath" :multiple="false" @valueChange="(val) => (state.form.areaPath = val.areaPath)" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <div class="table-box" v-loading="state.loading">
      <kade-table-wrap title="楼栋列表">
        <template #extra>
          <el-button type="success" icon="el-icon-edit" size="small" @click="edit('add', {})">新建楼栋</el-button>
          <el-button type="primary" icon="el-icon-delete-solid" size="small" @click="batchDel()">批量删除</el-button>
          <el-button class="btn-blue" icon="el-icon-daoru" type="" size="small" @click="handleImport('import')">导入</el-button>
          <el-button class="btn-purple" icon="el-icon-daochu" type="" size="small" @click="handleExport('export')">导出</el-button>
        </template>
        <el-table border :data="state.dataList" height="55vh" @selection-change="handleSelectChange">
          <el-table-column prop="" show-overflow-tooltip type="selection" align="center"></el-table-column>
          <el-table-column prop="buildNo" show-overflow-tooltip label="楼栋号" align="center"></el-table-column>
          <el-table-column prop="buildName" show-overflow-tooltip label="楼栋名称" align="center"></el-table-column>
          <el-table-column prop="buildTypeName" show-overflow-tooltip label="楼栋类型" align="center"></el-table-column>
          <el-table-column prop="areaName" show-overflow-tooltip label="所属区域" align="center"></el-table-column>
          <el-table-column prop="unitCount" show-overflow-tooltip label="总单元数" align="center"></el-table-column>
          <el-table-column prop="floorCount" show-overflow-tooltip label="总楼层数" align="center"></el-table-column>
          <el-table-column prop="" show-overflow-tooltip label="经纬度" align="center">
            <template #default="scope">
              {{ `经度：${scope.row.lngPoint ? scope.row.lngPoint : '无'}，纬度：${scope.row.latPoint ? scope.row.latPoint : '无'}`
              }}
            </template>
          </el-table-column>
          <el-table-column prop="address" show-overflow-tooltip label="详细地址" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="220px">
            <template #default="scope">
              <el-button type="text" size="mini" @click="edit('edit', scope.row)">编辑</el-button>
              <el-button type="text" size="mini" @click="del(scope.row)">删除</el-button>
              <el-button type="text" size="mini" @click="edit('detail', scope.row)" style="margin-right: 10px">查看详情</el-button>
             <!--  <el-dropdown trigger="click" @command="(e) => handleCommand(e, scope.row)">
                <el-button type="text" size="mini">楼栋大屏</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="(item, index) in bigScreenList" :key="index" :command="item.route">{{
                      item.name }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :currentPage="state.form.currentPage" :pageSize="state.form.pageSize" :page-sizes="[5, 10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </div>
    <kade-build-edit :dialogVisible="state.dialogVisible" :type="state.type" :rowData="state.rowData" @close="close"></kade-build-edit>
    <kade-build-import :dialogTable="state.dialogTable" @close="close"></kade-build-import>
  </div>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
/*   ElDropdown,
  ElDropdownItem, */
  ElPagination,
  ElMessage,
  ElMessageBox
} from "element-plus";
import { onMounted, reactive } from "vue";
import { useRouter } from "vue-router"
import { useDict } from "@/hooks/useDict.js";
import { downloadXlsx } from "@/utils"
import { getBuildingList, buildingDelete, buildingBatchDelete, exportBuilding } from "@/applications/eccard-dorm/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import edit from "./components/edit.vue";
import buildingImport from "./components/buildingImport.vue"
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
   /*  ElDropdown,
    ElDropdownItem, */
    ElPagination,
    "kade-area-select-tree": areaSelectTree,
    "kade-build-edit": edit,
    "kade-build-import": buildingImport,
  },
  setup() {
    const router = useRouter()
    const bigScreenList = [
      { name: "监控大屏", route: "MonitorScreen" },
      { name: "考勤大屏", route: "CheckWorkScreen" },
      // { name: "AI大屏", route: "AIScreen" },
    ]
    const buildTypeList = useDict("DORM_BUILDING_TYPE");
    const state = reactive({
      loading: false,
      dialogVisible: false,
      dialogTable: false,
      type: "",
      rowData: {},
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      selectRowList: []
    });

    const getList = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true
      try {
        let { data: { list, total } } = await getBuildingList(state.form)
        state.dataList = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }

    const edit = (type, row) => {
      console.log(type, row);
      state.type = type;
      state.rowData = row
      state.dialogVisible = true;
    };
    const handleImport = (val) => {
      console.log(val)
      state.dialogTable = true
    };
    const handleExport = async () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key]
        }
      }
      state.loading = true
      let res = await exportBuilding(state.form)
      downloadXlsx(res, "楼栋列表.xlsx")
      state.loading = false
    }
    const del = (row) => {
      ElMessageBox.confirm("确认删除?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await buildingDelete(row.buildId);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    };
    const handleSelectChange = (val) => {
      state.selectRowList = val
    }
    const batchDel = () => {
      if (!state.selectRowList.length) {
        return ElMessage.error("请先选择需要删除的楼栋！")
      }
      ElMessageBox.confirm("确认删除已选择楼栋?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let param = state.selectRowList.map(item => item.buildId).join(",")
        let { code, message } = await buildingBatchDelete(param);
        if (code === 0) {
          ElMessage.success(message);
          getList();
        }
      });
    }
    const handleCommand = (e, row) => {
      console.log(JSON.parse(sessionStorage.getItem(`kade_cache_userinfo`)));
      router.push({
        name: e,
        params: {
          tenantId: JSON.parse(sessionStorage.getItem(`kade_cache_userinfo`)).tenantId,
          buildName: row.buildName,
          buildId: row.buildId,
          unitNum: 1
        }
      })
    }
    const handleSearch = () => {
      getList()
    }
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.dialogVisible = false;
      state.dialogTable = false
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()

    };
    onMounted(() => {
      getList()
    })
    return {
      bigScreenList,
      buildTypeList,
      state,
      edit,
      handleImport,
      handleExport,
      del,
      handleSelectChange,
      batchDel,
      handleCommand,
      handleSearch,
      handleReset,
      close,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-dialog__footer) {
  text-align: center;
  border: 0;
}

:deep(.el-table--border) {
  border: 1px solid #eeeeee;
}
</style>