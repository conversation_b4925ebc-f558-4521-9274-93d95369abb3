<template>
  <div class="building" v-if="dataList.length">
    <div class="building-item" v-for="(item, index) in dataList" :key="index"
      :style="{ backgroundColor: item.fullRoomCount == item.roomCount ? '#aaaaaa' : (item.fullRoomCount !== 0 ? '#1296db' : '#70b603') }">
      <div class="building-top">
        <div><i class="el-icon-office-building"></i>{{ item.buildName }}</div>
        <div>{{ item.fullRoomCount }}/{{ item.roomCount }}</div>
      </div>
      <div class="building-bottom">
        <div>房间总数：{{ item.roomCount }}</div>
        <div>已住满数：{{ item.fullRoomCount }}</div>
        <div>未住满数：{{ item.notFullRoomCount }}</div>
        <div>空房间数：{{ item.emptyRoomCount }}</div>
      </div>
    </div>
  </div>
  <el-empty v-else description="暂无数据"></el-empty>
</template>
<script>
import { ElEmpty } from "element-plus"
export default {
  components: {
    ElEmpty
  },
  props: {
    dataList: {
      type: Array,
      default: null
    }
  }
};
</script>

<style lang="scss" scoped>
.building {
  box-sizing: border-box;
  // width: 100%;
  flex-wrap: wrap;
  height: 55vh;
  margin: 20px;
  overflow-y: auto;

  .building-item {
    width: 200px;
    display: inline-block;
    align-items: center;
    color: #fff;
    font-size: 12px;
    padding: 10px 15px 20px 15px;
    border-radius: 5px;
    margin: 0 15px 15px 0;

    .building-top {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .building-bottom {
      display: flex;
      align-items: center;
      border-top: 1px solid #fff;
      justify-content: space-between;
      flex-wrap: wrap;
      padding-top: 10px;
      margin-top: 5px;

      & :first-child {
        margin: 0 15px 10px 0;
      }

      & :nth-child(2) {
        margin-bottom: 10px;
      }
    }
  }
}

:deep(.el-icon-office-building:before) {
  color: #fff;
  font-size: 30px;
  margin-right: 5px;
}
</style>