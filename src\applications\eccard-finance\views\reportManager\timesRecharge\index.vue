<template>
  <!-- 次数充值明细 -->
  <div class="cashRecharge">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="统计时间:">
          <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="按入账时间:">
          <el-checkbox v-model="state.timeType"></el-checkbox>
        </el-form-item>
        <el-form-item label="用户编号:">
          <el-input placeholder="编号关键字搜索" v-model="state.form.userCode"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名:">
          <el-input placeholder="姓名关键字搜索" v-model="state.form.userName"></el-input>
        </el-form-item>
        <el-form-item label="组织机构:">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false" @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item>
        <el-form-item label="交易类型:">
          <el-select clearable multiple collapse-tags v-model="state.form.costType" placeholder="请选择">
            <el-option v-for="(item, index) in state.costTypeList" :key="index" :label="item.costName" :value="item.costCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易来源:">
          <el-select clearable multiple collapse-tags v-model="state.form.tradeSource" placeholder="请选择">
            <el-option v-for="(item, index) in state.tradeSourceList" :key="index" :label="item.tradeName" :value="item.tradeCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作员:">
          <el-select clearable filterable multiple collapse-tags v-model="state.form.operatorId" placeholder="请选择">
            <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.userName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="次数充值明细表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElCheckbox,
  ElMessage
} from "element-plus";
import { downloadXlsx } from "@/utils";
import { timeStr } from "@/utils/date.js";
import { requestDate, defaultTime } from "@/utils/reqDefaultDate";
import { reactive } from "@vue/reactivity";
import {
  getFrequencyList,
  costType,
  tradeSource,
  tradeMode,
  getSystemUser,
  frequencyExport,
} from "@/applications/eccard-finance/api";
import { onMounted, } from "@vue/runtime-core";
import deptSelectTree from "@/components/tree/deptSelectTree.vue";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "充值钱包", prop: "walletName", width: "" },
  { label: "充值次数", prop: "rechargeAmount", width: "" },
  { label: "充值时间", prop: "rechargeTime", width: "170", render: (val) => val && timeStr(val) },
  { label: "入账时间", prop: "tradeDate", width: "170", render: (val) => val && timeStr(val) },
  { label: "交易类型", prop: "costTypeName", width: "" },
  { label: "交易来源", prop: "tradeSourceName", width: "" },
  { label: "交易方式", prop: "tradeModeName", width: "" },
  { label: "操作员", prop: "operatorName", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElDatePicker,
    ElCheckbox,
    "kade-dept-select-tree": deptSelectTree,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      timeType: true,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      requestDate: requestDate(),
      costTypeList: [],
      allWalletList: [],
      departCheckList: [],
      tradeSourceList: [],
      tradeModeList: [],
      systemUserList: [],
    });
    //获取交易类型
    const getCostTypeList = () => {
      costType().then((res) => {
        state.costTypeList = res.data.filter(
          (item) => item.costCode == 101 || item.costCode == 102
        );
      });
    };
    /* 
    //获取钱包列表
    const queryWalletActiveList = () => {
      getWalletActiveList().then((res) => {
        state.allWalletList = res.data;
      });
    }; */
    //获取交易来源
    const getTradeSource = () => {
      tradeSource().then((res) => {
        state.tradeSourceList = res.data.filter(
          (item) => item.tradeCode == 1 || item.tradeCode == 2
        );
      });
    };
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode({ costType: 101 }).then((res) => {
        state.tradeModeList = res.data;
      });
    };
    //获取操作员
    const querySystemUser = () => {
      getSystemUser().then((res) => {
        state.systemUserList = res.data;
      });
    };

    const getParams = () => {
      let params = { ...state.form }
      params.timeType = state.timeType ? "BY_CREATE_DATE" : "BY_TRADE_DATE";

      if (state.requestDate && state.requestDate.length) {
        params.startDate = timeStr(state.requestDate[0]);
        params.endDate = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择统计时间")
        return false
      }
      return params
    }

    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data } = await getFrequencyList(params);
        if (code === 0) {
          let {
            pageInfo: { total, list },
            totalAmount,
          } = data;
          state.detailList = list;
          if (state.detailList.length) {
            state.detailList.push({
              rechargeAmount: totalAmount,
              userCode: "合计",
            });
          }
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    }
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await frequencyExport(params);
        downloadXlsx(res, "次数充值明细.xlsx")
        state.loading = false;
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
      };
      state.requestDate = requestDate()
    };
    onMounted(() => {
      // getList();
      getCostTypeList();
      getTradeSource();
      getTradeModeList();
      querySystemUser();
    });
    return {
      defaultTime,
      column,
      state,
      timeStr,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>