<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form  inline size="mini" label-width="100px">
        <el-form-item label="资产编号">
          <el-input v-model="state.form.code" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="资产名称">
          <el-input v-model="state.form.name" placeholder="请输入"></el-input>
        </el-form-item>
        <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange" />
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="资产列表">
      <el-icon>
        <Delete />
      </el-icon>
      <template #extra>
        <el-button type="success" size="mini" icon="el-icon-plus" @click="edit('add')">新增</el-button>
        <el-button type="primary" size="mini" icon="el-icon-delete-solid" @click="batchDel">批量删除</el-button>
        <el-button class="btn-purple" size="mini" icon="el-icon-daochu" @click="handleExport">导出</el-button>
      </template>
      <el-table :data="state.data" border @selection-change="SelectChange" v-loading="state.loading">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :label="item.label" :prop="item.prop" :width="item.width" align="center"></el-table-column>
        <el-table-column label="操作" width="200px" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" class="green" @click="edit('details',scope.row)">详情</el-button>
            <el-button type="text" size="mini" class="green" @click="edit('edit',scope.row)">编辑</el-button>
            <el-button type="text" size="mini" class="green" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:currentPage="state.currentPage" v-model:page-size="state.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </kade-table-wrap>
    <kade-breach-princeple-edit v-model:modelValue="state.isShow" :type="state.type" :rowData="state.rowData" @update:modelValue="close" />
  </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity"
import edit from "./components/edit.vue"
// import { downloadXlsx } from "@/utils"
import { getAssetsInfo,delAssetsInfo,batchAssetsInfo,exportAssetsInfo } from "@/applications/eccard-dorm/api.js"
import linkageSelect from '@/applications/eccard-dorm/components/linkageSelect'
import { ElForm, ElFormItem, ElInput, ElButton, ElTable, ElTableColumn,ElPagination,ElMessageBox,ElMessage } from "element-plus"
import { onMounted } from 'vue'
const linkageData = {
  area: { label: '所属区域', valueKey: 'areaPath', key: 'areaPath' },
  building: { label: '所属楼栋', valueKey: 'buildId' }
}
const column = [
  { label: '所属区域', prop: 'areaName' },
  { label: '所属楼栋', prop: 'buildName' },
  { label: '所属单元', prop: 'unitNum' },
  { label: '资产编号', prop: 'code' },
  { label: '资产名称', prop: 'name' },
  { label: '规格型号', prop: 'specs' },
  { label: '数量', prop: 'num' },
  { label: '资产位置', prop: 'position',width:'300px' }
]
export default {
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-linkage-select": linkageSelect,
    "kade-breach-princeple-edit":edit
  },
  setup() {
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 10
      },
      data: [],
      total: 0,
      loading:false,
      isShow:false,
      type:'',
      rowData:'',
      selectData:''
    });
    const handleSearch=()=>{
      getList()
    }
    const handleReset=()=>{
      state.form={
        pageSize:10,
        currentPage:1
      }
      getList()
    }
    const linkageChange = (val) => {
        state.form = { ...state.form, ...val }
    }
    const edit = (type, row) => {
      console.log(type, row)
      state.isShow=true
      state.type=type
      state.rowData=row

    }
    const handleDel = (row) => { 
      ElMessageBox.confirm(`确认删除?`, `提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let { code,message } = await delAssetsInfo(row.id)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
    }

    const SelectChange =(val)=>{
        state.selectData=val
    }
    const batchDel = () => {
      if(!state.selectData.length){
        return ElMessage.error("请先选择需要删除的信息！")
      }
      ElMessageBox.confirm(`确认删除已选择信息？`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let param = state.selectData.map((item)=>item.id).join(',')
        console.log(param)
        let {code,message} = await batchAssetsInfo(param)
        if(code===0){
          ElMessage.success(message)
          getList()
        }
      })
     }
    const handleExport = async() => {
      let res = await exportAssetsInfo(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res],{type:'application/vnd.ms-excel'})
      )
      let link = document.createElement('a')
      link.href=url
      link.style.display='none'
      link.setAttribute('download','公共资产列表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      //  for (let key in state.form) {
      //   if (!state.form[key]) {
      //     delete state.form[key]
      //   }
      // }
      // state.loading = true
      // let res = await exportAssetsInfo(state.form)
      // console.log(res)
      // downloadXlsx(res, "公共资产列表.xlsx")
      // state.loading = false
    }
    const handleCurrentChange=(val)=>{
      state.form.currentPage=val
      getList()
    }
    const handleSizeChange=(val)=>{
      state.form.currentPage=1
      state.form.pageSize=val
      getList()
    }
    const getList=async()=>{
      state.loading=true
      try{
        let { data:{list,total} } = await getAssetsInfo(state.form)
        state.data=list
        state.total=total
        state.loading=false
        console.log(list)
      }
      catch{
        state.loading=false
      }
    }
    const close = (val)=>{
      if(val){
        getList()
      }
        state.isShow=false
    }
    onMounted(()=>{
      getList()
    })
    return {
      state,
      column,
      edit,
      close,
      linkageData,
      linkageChange,
      handleDel,
      batchDel,
      handleExport,
      handleReset,
      handleSearch,
      handleCurrentChange,
      handleSizeChange,
      SelectChange
    }
  }
}
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}
:deep(.el-dialog){
  border-radius: 6px;
}
:deep(.el-dialog__header){
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 15px;
}
:deep(.el-dialog__footer){
  border-top: none;
  text-align: center;
}
:deep(.el-input--mini .el-input__inner){
  width: 198px;
}
:deep(.el-input-number--mini){
  width: 198px;
}
:deep(.el-pagination .el-select .el-input .el-input__inner){
  width: 100px;
}
:deep(.el-pagination__editor.el-input .el-input__inner){
  width: 46px;
}
:deep(.single-image-uploader .el-upload){
  width: 100px;
  height: 100px;
}

</style>