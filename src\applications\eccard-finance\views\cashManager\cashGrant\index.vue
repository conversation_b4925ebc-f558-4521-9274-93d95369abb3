<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="现金发放列表" style="height: 100%">
      <template #extra>
        <el-button icon="el-icon-daoru" size="small" class="btn-green" @click="cashClick()">现金发放</el-button>
        <el-button icon="el-icon-daoru" size="small" class="btn-yellow" @click="importRighting()">导入冲正</el-button>
        <el-button icon="el-icon-daoru" size="small" class="btn-purple" @click="exportList()">下载查询明细</el-button>
        <el-button icon="el-icon-daochu" size="small" class="btn-blue" @click="printClick()">打印</el-button>
      </template>
      <kade-tab-wrap :tabs="tabs" v-model="state.tab">
        <template #qb>
          <kade-all-cash-grant />
        </template>
        <template #dsh>
          <kade-examine-cash-grant />
        </template>
        <template #ytg>
          <kade-to-pass-cash-grant />
        </template>
        <template #wtg>
          <kade-out-pass-cash-grant />
        </template>
      </kade-tab-wrap>
      <kade-add-cash-grant />
      <kade-edit-cash-grant />
      <kade-import-reversal />
      <kade-dialog-list />
      <kade-cash-detail />
    </kade-table-wrap>
  </kade-route-card>
</template>
<script>
import { ElButton, ElMessage } from "element-plus";
import { onMounted, reactive } from "vue";
import { useStore } from "vuex";
import {
  getRolelist,
  getCardTypeList,
  getCashGrantListByExport,
  getCashGrantListByPrint
} from "@/applications/eccard-finance/api";
import { print } from "@/utils";
import allCashGrant from "./allCashGrant";
import examineCashGrant from "./examineCashGrant";
import outPassCashGrant from "./outPassCashGrant";
import toPassCashGrant from "./toPassCashGrant";
import addCashGrant from "./components/addCashGrant.vue";
import editCashGrant from "./components/editCashGrant.vue";
import importReversal from "./components/importReversal.vue";
import DialogList from "./components/DialogList.vue";
import cashDetail from "./components/cashDetail.vue";
const tabs = [
  {
    name: "qb",
    label: "全部",
  },
  {
    name: "dsh",
    label: "待审核",
    status: "SYS_NO_AUDIT",
  },
  {
    name: "ytg",
    label: "已通过",
    status: "SYS_AUDIT_PASSED",

  },
  {
    name: "wtg",
    label: "未通过",
    status: "SYS_AUDIT_FAIL",

  },
];
export default {
  components: {
    ElButton,
    "kade-all-cash-grant": allCashGrant,
    "kade-examine-cash-grant": examineCashGrant,
    "kade-to-pass-cash-grant": toPassCashGrant,
    "kade-out-pass-cash-grant": outPassCashGrant,
    "kade-add-cash-grant": addCashGrant,
    "kade-edit-cash-grant": editCashGrant,
    "kade-import-reversal": importReversal,
    "kade-dialog-list": DialogList,
    "kade-cash-detail": cashDetail,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      tab: "qb",
      dialogVisibleAdd: false,
      isImportReversal: false,
      departCheckList: [],
    });

    //获取身份类别
    const queryRolelist = () => {
      getRolelist(state.form).then((res) => {
        store.commit("cashData/updateState", {
          key: "roleList",
          payload: res.data,
        });
      });
    };

    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        store.commit("cashData/updateState", {
          key: "cardTypeList",
          payload: res.data,
        });
      });
    };

    const cashClick = () => {
      store.commit("cashData/updateState", {
        key: "isAdd",
        payload: true,
      });
    };
    const active = () => {
      store.commit("cashData/updateState", {
        key: "isEdit",
        payload: false,
      });
    };
    const importRighting = () => {
      /*
       ** 项目未审核或未通过就不能冲正，可以修改
       ** 冲正只能选择已通过的项目
       */
      if (store.state.cashData.selectRow) {
        if (store.state.cashData.selectRow.costType === 102) {
          return ElMessage.error("当前选中项目为冲正项目！");
        } else {
          if (store.state.cashData.selectRow.auditStatus === "SYS_AUDIT_PASSED") {
            store.commit("cashData/updateState", {
              key: "isImportReversal",
              payload: true,
            });
          } else {
            return ElMessage.error("当前选中项目未审核或未通过！");
          }
        }
      } else {
        ElMessage.error("请选择发放项目！");
      }
    };

    const exportList = () => {

      let data = { ...store.state.cashData.exportParam, auditStatus: tabs.find(item => item.name === state.tab)?.status };
      getCashGrantListByExport(data).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "现金发放列表.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    };

    const printClick = async () => {
      let data = store.state.cashData.exportParam;
      let res = await getCashGrantListByPrint(data);
      if (res.code === 0) {
        print(res.data, '现金发放明细')
      }
    }

    onMounted(() => {
      queryRolelist();
      queryCardTypeList();
    });
    return {
      state,
      tabs,
      printClick,
      cashClick,
      exportList,
      importRighting,
      active,
    };
  },
};
</script>