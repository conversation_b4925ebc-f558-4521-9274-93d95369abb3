<template>
  <el-dialog append-to-body="true" :model-value="modelValue" title="查看详情" width="1200px" :before-close="update" :close-on-click-modal="false" :modal="true">
        <div style="with:120px;float:left">
          <el-image style="width: 100px; height: 200px" :src="state.model.userinfo.userPhoto" :fit="'contain'" />
        </div>
        <div style="margin-left:140px;width:90%">
          <el-form ref="formRef" :label-width="labelWidth" :inline="true" :rules="rules" :model="state.model" size="small">
            <el-form-item label="用户编号" prop="userNum">
              <el-input placeholder="请输入" v-model="state.model.userNum" />
            </el-form-item>
            <el-form-item label="用户名称" prop="userName">
              <el-input placeholder="请输入" v-model="state.model.userinfo.userName" />
            </el-form-item>
            <el-form-item label="组织机构" prop="orgNameList">
              <el-input placeholder="请输入" v-model="state.model.userinfo.orgNameList" />
            </el-form-item>
            <el-form-item label="所属考勤组" prop="atgName">
              <el-input placeholder="请输入" v-model="state.model.atgName" />
            </el-form-item>
            <el-form-item label="考勤时间" prop="userNative">
              <el-input placeholder="请输入" v-model="state.model.atiDate" />
            </el-form-item>
            <el-form-item label="打卡时间" prop="atiCheckDate">
              <el-input placeholder="请输入" v-model="state.model.atiCheckDate" />
            </el-form-item>
            <el-form-item label="打卡图片" prop="atiCheckImg" style="width:100%">
              <el-image style="width: 300px; height: 280px" :src="state.model.atiCheckImg" :fit="'contain'" />
            </el-form-item>
          </el-form>
        </div>
  </el-dialog>
</template>
<script>
  import { reactive , ref , computed , watch} from "vue";
  // import {timeStr} from "@/utils/date"
// import attendInfoComponent from "./attendinfo.vue"
// import { getAttentionUserAttendDetail } from "@/applications/eccard-ams/api";
  import {
    // ElTabPane,
    // ElTabs,
    // ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    // ElMessage,
    // ElSwitch,
    ElImage,
    ElDialog
  } from "element-plus";

// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({}),
    },
    data:{
      type: Object,
      default: () => ({}),
    },
    userinfo:{
      type: Object,
      default: () => ({}),
    }
  },
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    // ElMessage,
    ElImage,
    ElDialog
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: props.data,
    });
    const rules = {
      roleName: [
        { required: true, message: "请输入类别名称" },
        { max: 20, message: "类别名称不能超过20个字符" },
      ],
    };

    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("update:modelValue", v);
    };
    watch(
      () => props.data,
      async (n) => {
        if (n) {
          if (props.data) {
            state.model = props.data;
            state.model["userinfo"] = props.userinfo;
          } else {
            state.model = {};
            state.model["userinfo"] = props.userinfo;
          }
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
    };
  },
};
</script>
