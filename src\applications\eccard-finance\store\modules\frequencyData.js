import {

} from "@/applications/eccard-finance/api";
import { requestDate } from "@/utils/reqDefaultDate.js";
const state = {
  isEdit: false,
  selectRow: '',
  frequencyTypeList: [],
  roleList: [],
  departCheckList: [],
  addSuccess: false,
  exportParam: {
    beginDate: requestDate()[0],
    endDate: requestDate()[1],
  },
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  },
};
const actions = {


};
export default {
  namespaced: true,
  state,
  mutations,
  actions
}