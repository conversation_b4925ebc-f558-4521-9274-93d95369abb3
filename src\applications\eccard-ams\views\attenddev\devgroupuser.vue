<template>
  <div style="height: 100%">
    <p style="margin:10px auto 10px 10px;">
      <el-form inline size="mini">
        <el-form-item label="所属考勤组">
          <el-select v-model="state.form.atgId" :clearable="true">
            <el-option v-for="(item,index) in state.atgIdList" :key="index" :label="item.atgName" :value="item.atgId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="用户编号或名称搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </p>
    <p style="margin:0px auto 10px 5px;">
      <el-button size="mini" type="success" icon="el-icon-plus" @click="add('','add')">授权</el-button>
      <!-- <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleClick(scope.row)">编辑</el-button>
      <el-button size="mini" type="danger" icon="el-icon-circle-close" @click="handleClick(scope.row)">删除</el-button> -->
    </p>
    <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
      <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
      <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
      <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
      <el-table-column label="性别" prop="userSex" align="center">
        <template #default="scope">
          {{ scope.row.userSex == 'SEX_MALE' ? '男' : '女' }}
        </template>
      </el-table-column>
      <el-table-column label="手机号码" prop="userTel" align="center" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.userTel ?  scope.row.userTel.toString().substring(0 , 3) + '****' + scope.row.userTel.toString().substring(7) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="人员状态" prop="userPostStatus" align="center" show-overflow-tooltip>
        <template #default="scope">
          <el-tag size="small" :type=" scope.row.userPostStatus === 'STATE_IN' ? 'primary' : 'warning' ">{{
            dictionaryFilter(scope.row.userPostStatus) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="" align="center">
        <template #default="scope">
          <el-button @click="edit(scope.row , 'edit')" type="text" size="mini">详情</el-button>
          <el-button @click="unBind(scope.row , 'edit')" type="text" size="mini">删除授权</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>

    <el-dialog append-to-body="true" v-model="state.isEdit" width="1200px" :title="'人员详情信息'" :close-on-click-modal="false" :modal="true">
      <user-edit :data="state.rowData" :type="1" />
    </el-dialog>

    <bind-user-info :modelValue="state.isEdit1" :type="state.type" :data="state.rowData"  @close="close" @update:modelValue="close" @edit="state.type='edit'" />

  </div>
</template>
<script>
  import { reactive, watch , onMounted} from "vue";
  import { useStore } from "vuex";
  import { getAttentionUserAttendPage , removeBindDevGroup ,getAttentionGroupInfoPageAll } from "@/applications/eccard-ams/api";
  import deptSelectTree from "@/components/tree/deptSelectTree.vue";
  //import userEdit from "@/applications/eccard-ams/views/userattend/components/edit.vue"
  import userEdit from "@/applications/eccard-basic-data/views/personInfo/components/details/info.vue"
  import bindUserInfo from "@/applications/eccard-ams/views/attenddev/binddevgroupuser.vue"
  import {
    ElSelect,
    ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessage,
    ElMessageBox,
    ElDialog,
  } from 'element-plus';

  export default {
    components: {
      "kade-dept-select-tree": deptSelectTree,
      ElSelect,
      ElOption,
      ElInput,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      userEdit,
      ElDialog,
      bindUserInfo,
    },
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      type:{
        type: String,
        default: "",
      },
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    setup(props) {
      const store = useStore();
      const state = reactive({
        loading: false,
        isEdit:false,
        isEdit1:false,
        atgIdList:[],
        form:{
          atgId:'',
          degId:'',
          userDisType:1,
          orgNameList:'',
          keyWord:'',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        if(!props.data.degId){
          return;
        }
        state.loading=true
        let {data}=await getAttentionUserAttendPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }
      const add=(row,type)=>{
        if(!props.data.degId){
          ElMessage.warning("请选择左边设备分组！");return;
        }
        state.type=type
        state.rowData = props.data;
        state.isEdit1=true
      }
      const edit=(row,type)=>{
        row["id"] = row.userId;
        store.commit("userInfo/updateState", {
          key: "rowData",
          payload: row,
        });
        store.commit("userInfo/updateState", {
          key: "isDetails",
          payload: true,
        });

        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form = {
          degId : props.data.degId,
          userDisType:1,
          orgNameList:'',
          keyWord:'',
          currentPage:1,
          pageSize:10
        };
        getList();
      }
      const search=()=>{
        if(!props.data.degId){
          ElMessage.warning("请选择左边设备分组！");
        }
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const unBind=async (row)=>{
        ElMessageBox.confirm(`确定删除授权？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code}=await removeBindDevGroup(row)
          if(code===0){
            //ElMessage.success(message)
            getList()
          }
        });
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
        state.isEdit1=false
      }

      onMounted(async ()=>{
        let {data} = await getAttentionGroupInfoPageAll('');
        state.atgIdList = data;

        state.form.degId = props.data ?  props.data.degId : '';
        getList();
      });

      watch(
        () => props.data,
        (n) => {
          if (n) {
            state.form.degId = props.data.degId;
            getList();
          }
        }
      );

      return {
        state,
        add,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        // del,
        close,
        unBind,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
