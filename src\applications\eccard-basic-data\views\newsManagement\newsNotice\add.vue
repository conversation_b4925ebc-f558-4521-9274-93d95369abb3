<template>
  <el-dialog :model-value="isShow" title="发布新闻公告" width="1200px" :before-close="beforeClose" :close-on-click-modal="false">
    <div style="padding-top: 20px">
      <el-form size="mini" label-width="120px" ref="formDom" :rules="rules" :model="state.form">
        <el-row>
          <el-col :span="6">
            <el-form-item label="新闻公告类型:" prop="newsType">
              <el-select v-model="state.form.newsType" placeholder="请选择">
                <el-option v-for="item in newsType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收端:" prop="newsReceiver">
              <el-select v-model="state.form.newsReceiver" multiple collapse-tags placeholder="请选择">
                <el-option v-for="item in newsReceiver" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label=" 发布人:" prop="newsPublisher">
              <el-input placeholder="请输入" v-model="state.form.newsPublisher"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发布时间:" prop="newsPublishTime">
              <el-date-picker v-model="state.form.newsPublishTime" type="datetime" placeholder="请选择时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="新闻公告标题:" prop="newsTitle">
          <el-input placeholder="请输入不超过30字" v-model="state.form.newsTitle" :maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="封面图片:" prop="newsImgUrl">
          <el-upload class="avatar-uploader" :show-file-list="false" :auto-upload="false" :on-change="uploadChange">
            <img v-if="state.form.newsImgUrl" :src="state.form.newsImgUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon el-icon-plus"></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="新闻正文:" prop="newsContent">
          <kade-wangeditor :htmlData="state.form.newsContent" @change="(val) => (state.form.newsContent = val)" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="beforeClose" size="mini">取消</el-button>
        <el-button type="primary" @click="submit()" size="mini">提交</el-button>
      </span>
    </template>
  </el-dialog>
  <kade-tailor :isShow="state.isTailor" :file="state.imgFile" @close="close" />
</template>
<script>
import { computed, reactive, ref } from "vue";
import { useStore } from "vuex";
import { useDict } from "@/hooks/useDict";
import { timeStr } from "@/utils/date.js";
// import { uploadApplyLogo } from "@/applications/unified_portal/api";
import { addNews } from "@/applications/eccard-basic-data/api";
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElUpload,
  ElIcon,
  ElMessage,
} from "element-plus";
import tailor from "./components/Tailor.vue";
import wangeditor from "@/components/wangeditor.vue";

const rules = {
  newsType: [
    {
      required: true,
      message: "请选择新闻公告类型",
      trigger: "change",
    },
  ],
  newsReceiver: [
    {
      type: "array",
      required: true,
      message: "请选择接收端",
      trigger: "change",
    },
  ],
  newsPublisher: [
    {
      required: true,
      message: "请输入发布人",
      trigger: "blur",
    },
  ],
  newsPublishTime: [
    {
      required: true,
      message: "请输入发布时间",
      trigger: "blur",
    },
  ],
  newsTitle: [
    {
      required: true,
      message: "请输入新闻公告标题",
      trigger: "blur",
    },
  ],
  newsImgUrl: [
    {
      required: true,
      message: "请上传封面图片",
      trigger: "blur",
    },
  ],
  newsContent: [
    {
      required: true,
      message: "请输入新闻正文",
      trigger: "blur",
    },
  ],
};

export default {
  components: {
    ElDialog,
    ElRow,
    ElCol,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElUpload,
    ElIcon,
    "kade-tailor": tailor,
    "kade-wangeditor": wangeditor,
  },
  setup() {
    const store = useStore();
    const formDom = ref(null);
    const newsType = useDict("SYS_NEWS_TYPE"); //新闻公告类型
    const newsReceiver = useDict("SYS_NEWS_RECEIVER"); //新闻接收
    const state = reactive({
      isTailor: false,
      imgFile: "",
      form: {
        newsReceiver: []
      },
    });
    const isShow = computed(() => {
      return store.state.news.isAdd;
    });

    //上传标题图片
    const uploadChange = async (file) => {
      console.log(file);
      var reader = new FileReader();
      reader.readAsDataURL(file.raw);
      reader.onload = function () {
        this.result; // 这个就是base64编码了
        state.imgFile = this.result;
      };
      /* const formData = new FormData();
      formData.append("file", file.raw);
      const { data } = await uploadApplyLogo(formData);
      console.log(data);
      state.imgFile = data; */
      state.isTailor = true;
    };

    const submit = () => {
      formDom.value.validate(async (valid) => {
        if (valid) {
          let params = { ...state.form };
          params.newsReceiver = params.newsReceiver.join(",");
          params.newsPublishTime = timeStr(params.newsPublishTime);
          let { code, message } = await addNews(params);
          if (code === 0) {
            ElMessage.success(message)
            beforeClose()
          }
        } else {
          return false;
        }
      });
    };

    const beforeClose = () => {
      formDom.value.clearValidate()
      store.commit("news/updateState", {
        key: "isAdd",
        payload: false,
      });
      state.form = {
        newsReceiver: []
      }
    };
    const close = (val) => {
      if (val) {
        state.form.newsImgUrl = val;
      }
      state.isTailor = false;
      state.imgFile = "";
    };

    return {
      rules,
      formDom,
      newsType,
      newsReceiver,
      state,
      isShow,
      uploadChange,
      submit,
      beforeClose,
      close,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.avatar-uploader .el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: #409eff;
}

:deep(.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 240px;
  line-height: 160px;
  text-align: center;
}

.avatar {
  width: 240px;
  height: 160px;
  display: block;
}

:deep(.el-form-item__content) {
  .el-select {
    width: 100%;
  }

  .el-textarea {
    width: 100%;

    .el-textarea__inner {
      width: 100%;
    }
  }

  .el-date-editor.el-input {
    width: 100%;
  }
}
</style>