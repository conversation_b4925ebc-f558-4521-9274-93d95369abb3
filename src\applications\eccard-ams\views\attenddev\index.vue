<template>
  <kade-route-card style="height: auto">
    <el-row :gutter="15">
      <el-col :sm="5" class="box-card">
        <el-row :gutter="0">
          <el-col :sm="24" style="margin: 10px 10px 10px 5px;">
            <i class="el-icon-form"></i>
            <span style="margin-left:5px;">设备组列表</span>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :sm="24">
            <el-input
              v-model="state.form.degName"
              placeholder="请输入关键字搜索"
              class="input-with-select"
              size="small"
              :clearable="true"
            >
              <template #append>
                <el-button @click="search" icon="el-icon-search">查询</el-button>
              </template>
            </el-input>
          </el-col>
        </el-row>
        <el-row :gutter="0" class="maindiv">
          <el-col :sm="24">
            <el-table :data="state.dataList" style="width: 100%"  v-loading="state.loading" :size="small" :show-header="false" :highlight-current-row="true" @current-change="rowSelectedChanged">
              <el-table-column prop="degName" label="Date" />
            </el-table>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :sm="24" style="width: 100%;text-align:center">
            <el-button type="success" size="small" :icon="Plus" @click="add(1)">添加</el-button>
            <el-button type="primary" size="small" :icon="Edit" @click="edit(2)">修改</el-button>
            <el-button type="danger" size="small" @click="del(state.rowData)">删除</el-button>
          </el-col>
        </el-row>
      </el-col>
      <el-col :sm="18" class="box-card">
        <el-tabs :model-value="'info'" class="tabs1">
          <el-tab-pane label="考勤设备" name="info">
            <dev-info :data="state.rowData"  />
          </el-tab-pane>
          <el-tab-pane label="授权人员列表" name="record">
            <dev-group-user :data="state.rowData"/>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <attend-dev-edit :modelValue="state.isEdit" :type="state.type" :data="state.type == '1' ? null  : state.rowData"  @close="close" @update:modelValue="close" @edit="state.type='edit'" />
  </kade-route-card>
</template>
<script>
  import { reactive } from "@vue/reactivity";
  // import {timeStr} from "@/utils/date"
  import { useDict } from "@/hooks/useDict";
  import devInfo from "@/applications/eccard-ams/views/attenddev/devinfo.vue";
  import devGroupUser from "@/applications/eccard-ams/views/attenddev/devgroupuser.vue";
  import {ElInput,ElTable,ElTableColumn,ElButton,ElMessage,ElMessageBox,ElRow,
    ElCol,
    ElTabPane,
    ElTabs,} from "element-plus"
  import { getAttentionAttendDevGroupAll , delAttentionAttendDevGroupAll } from "@/applications/eccard-ams/api";
  // import deptSelectTree from "@/components/tree/deptSelectTree.vue";
  import { onMounted } from '@vue/runtime-core';
  import attendDevEdit from "./components/edit.vue"

  export default {
    components: {
      ElRow,
      ElCol,
      ElTabPane,
      ElTabs,
      ElTable,ElTableColumn,ElButton,ElInput,
      attendDevEdit,
      // "kade-dept-select-tree": deptSelectTree,
      devInfo,
      devGroupUser,
    },
    setup() {
      const statusList = useDict("BASE_USER_STATE");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          degName : '',
          // currentPage:1,
          // pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionAttendDevGroupAll(state.form)
        state.dataList=data
        state.total=data.length;
        state.loading=false
      }
      const add=(type)=>{
        state.type=type
        state.isEdit=true
      }
      const edit=(type)=>{
        if(!state.rowData){
          ElMessage.error('请选择要修改的分组!');return;
        }
        state.type=type
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          degName : '',
          // currentPage:1,
          // pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const rowSelectedChanged=(val)=>{
        state.rowData = val;
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const del= (row)=>{
        console.log(JSON.stringify(row))
        if(!row){
          ElMessage.error('请选择要删除的分组!');return;
        }
        ElMessageBox.confirm("确认删除?", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          let { code, message } = await delAttentionAttendDevGroupAll(row.degId);
          if (code === 0) {
            ElMessage.success(message);
            getList();
          }
        });
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })

      return {
        state,
        statusList,
        add,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        rowSelectedChanged,
        del,
        close
      };
    },
  };
</script>
<style lang="scss" scoped>
  .maindiv{
    height:calc(100vh - 280px);
  }
  .tabs1{
    /*height:100vh;*/
  }
  .box-card{
    padding-bottom: 10px;
    box-sizing: border-box;
    border-radius: 5px;
    border: 1px solid #efefef;
    padding:10px;
    margin-left:10px;
  }
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }

  .el-col {

  }
</style>
