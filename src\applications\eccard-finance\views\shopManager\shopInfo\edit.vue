<template>
  <kade-route-card className="shopedit">
    <template #header>
      <span>{{ query.id ? "编辑" : "新建" }}商户信息</span>
      <!-- <el-button @click="$router.go(-1)" size="small" icon="el-icon-back">返回</el-button> -->
    </template>
    <el-form ref="formRef" :model="models" :rules="rules" label-width="120px" size="small"
      :class="{ info: state.isEdit }" v-loading="state.loading">
      <el-card header="商家基本信息1414">
        <el-row :gutter="20">
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="商家名称:" :prop="state.isEdit ? 'info.merchantName' : null">
              <span v-if="!state.isEdit">
                {{ info.merchantName }}
              </span>
              <template v-else>
                <el-input v-model="info.merchantName" placeholder="请输入"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12" v-if="info.id">
            <el-form-item label="商家编号:">
              <span v-if="!state.isEdit">
                {{ info.merchantNo }}
              </span>
              <template v-else>
                <el-input disabled :model-value="info.merchantNo" placeholder="请输入"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="商家类型:">
              <span v-if="!state.isEdit">
                {{ dictionaryFilter(info.merchantType) }}
              </span>
              <template v-else>
                <el-select clearable style="width: 100%" placeholder="请选择" v-model="info.merchantType">
                  <el-option v-for="item in merchantTypes" :label="item.label" :value="item.value" :key="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="所属区域:">
              <span v-if="!state.isEdit">
                {{ info.areaName }}
              </span>
              <template v-else>
                <kade-area-select-tree style="width: 100%" :value="info.areaId" valueKey="id" :multiple="false"
                  @valueChange="(val) => (info.areaId = val.id)" />
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="联系人:">
              <span v-if="!state.isEdit">
                {{ info.merchantContacts }}
              </span>
              <template v-else>
                <el-input v-model="info.merchantContacts" placeholder="请输入"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="联系电话:">
              <span v-if="!state.isEdit">
                {{ info.merchantTel }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="info.merchantTel"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="邮箱地址:">
              <span v-if="!state.isEdit">
                {{ info.merchantEmail }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="info.merchantEmail"></el-input>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card header="商家银行卡信息" style="margin-top: 20px">
        <el-row :gutter="20">
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="银行卡号:">
              <span v-if="!state.isEdit">
                {{ bank.bankNo }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="bank.bankNo"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="开户姓名:">
              <span v-if="!state.isEdit">
                {{ bank.accountName }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="bank.accountName"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="开户银行:">
              <span v-if="!state.isEdit">
                {{ dictionaryFilter(bank.accountBankOutlets) }}
              </span>
              <template v-else>
                <el-select clearable style="width: 100%" placeholder="请选择" v-model="bank.accountBankOutlets">
                  <el-option v-for="item in state.bankList" :label="item.bankName" :value="item.bankId"
                    :key="item.bankId"></el-option>
                </el-select>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="开户网点:">
              <span v-if="!state.isEdit">
                {{ bank.accountBank }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="bank.accountBank"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="网点地址:">
              <span v-if="!state.isEdit">
                {{ bank.bankOutletsAddress }}
              </span>
              <template v-else>
                <el-input show-word-limit maxlength="200" type="textarea" v-model="bank.bankOutletsAddress" rows="3"
                  placeholder="请输入"></el-input>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card header="商家营业信息" style="margin-top: 20px">
        <el-row :gutter="20">
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="统一信用代码:">
              <span v-if="!state.isEdit">
                {{ license.creditCode }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="license.creditCode"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="营业执照名称:">
              <span v-if="!state.isEdit">
                {{ license.businessLicenseName }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="license.businessLicenseName"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="营业类型:">
              <span v-if="!state.isEdit">
                {{ dictionaryFilter(license.businessType) }}
              </span>
              <template v-else>
                <el-select clearable style="width: 100%" placeholder="请选择" v-model="license.businessType">
                  <el-option v-for="item in merchantBusinessTypes" :label="item.label" :value="item.value"
                    :key="item.value"></el-option>
                </el-select>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="法定代表人:">
              <span v-if="!state.isEdit">
                {{ license.legalPerson }}
              </span>
              <template v-else>
                <el-input placeholder="请输入" v-model="license.legalPerson"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :lg="8" :md="12">
            <el-form-item label="成立日期:">
              <span v-if="!state.isEdit">
                {{ license.establishDate }}
              </span>
              <template v-else>
                <el-date-picker v-model="license.establishDate" style="width: 100%" type="date" placeholder="选择日期">
                </el-date-picker>
              </template>
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="16" :md="24">
            <el-form-item label="营业期限:">
              <span v-if="!state.isEdit">
                {{ (license.businessBeginDate && license.businessEndDate) ? (license.businessBeginDate + '至' +
                    license.businessEndDate) : ""
                }}
              </span>
              <template v-else>
                <el-date-picker v-model="state.dateLimit" type="daterange" style="width: 100%" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" @change="handleDateChange">
                </el-date-picker>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="营业范围:">
              <span v-if="!state.isEdit">
                {{ license.businessScope }}
              </span>
              <template v-else>
                <el-input show-word-limit maxlength="200" type="textarea" v-model="license.businessScope" rows="3"
                  placeholder="请输入"></el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="营业执照:">
              <template v-if="!state.isEdit">
                <kade-avatar :size="appImageSize" icon="iconyingyong" :is-app-image="true"
                  :src="license.businessLicenseImg" />
              </template>
              <template v-else>
                <kade-single-image-upload v-model="license.businessLicenseImg" :action="upload" icon="el-icon-plus" />
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <div class="shopedit-footer">
        <el-button @click="handleCancel" icon="el-icon-circle-close" size="small">关闭</el-button>
        <el-button :loading="state.btnLoading" @click="handleSubmit" icon="el-icon-circle-check" size="small"
          type="primary">{{ state.isEdit ? "保存" : "编辑" }}</el-button>
      </div>
    </el-form>
  </kade-route-card>
</template>
<script>
import {
  ElRow,
  ElCol,
  ElButton,
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElMessage,
  ElDatePicker,
} from "element-plus";
import { useStore } from "vuex";
import { reactive, onMounted, ref } from "vue";
import { useEvent } from "@/hooks/index.js"
import moment from "moment";
import { useDict } from "@/hooks/useDict";
import SingleImageUpload from "@/components/singleImageUpload";
import { uploadApplyLogo } from "@/applications/unified_portal/api";
import Avatar from "@/components/avatar";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";

import {
  getBankList,
  getMerchantInfoById,
  updateMerchant,
  addMerchant,
} from "@/applications/eccard-finance/api";
import { getDefaultFields, rules } from "./form";

export default {
  components: {
    "el-row": ElRow,
    "el-col": ElCol,
    "el-button": ElButton,
    "el-card": ElCard,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-input": ElInput,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-date-picker": ElDatePicker,
    "kade-single-image-upload": SingleImageUpload,
    "kade-avatar": Avatar,
    "kade-area-select-tree": areaSelectTree,

  },
  setup() {
    const merchantTypes = useDict("MERCHANT_TYPE");
    const merchantBusinessTypes = useDict("MERCHANT_BUSINESS_TYPE");
    const types = useDict("SYS_DATA_SOURCE");
    const formRef = ref(null);
    const store = useStore();
    const { id, type } = store.getters["app/query"];
    const state = reactive({
      isEdit: type !== "info",
      loading: false,
      btnLoading: false,
      model: getDefaultFields(),
      dateLimit: [],
      bankList: [],
    });
    const bank = ref(state.model.merchantBanksEntity);
    const info = ref(state.model.merchantInfoEntity);
    const license = ref(state.model.merchantLicenseEntity);
    const models = ref({ bank, info, license });
    const loadInfo = async () => {
      try {
        state.loading = true;
        const {
          data: { merchantBanks, merchantInfo, merchantLicense },
        } = await getMerchantInfoById({ merchantId: id });
        Object.assign(info.value, merchantInfo);
        Object.assign(bank.value, merchantBanks);

        if (merchantLicense) {
          Object.assign(license.value, {
            ...merchantLicense,
            /* businessBeginDate: moment(merchantLicense.businessBeginDate).toDate(),
        businessEndDate: moment(merchantLicense.businessEndDate).toDate(),
        establishDate: moment(merchantLicense.establishDate).toDate(), */
            businessBeginDate: merchantLicense.businessBeginDate,
            businessEndDate: merchantLicense.businessEndDate,
            establishDate: merchantLicense.establishDate,
          })
          state.dateLimit = merchantLicense.businessBeginDate && merchantLicense.businessEndDate ? [merchantLicense.businessBeginDate, merchantLicense.businessEndDate] : null
        } else {
          Object.assign(license.value, {});
        }

        console.log("license.value", license.value);
      } catch (e) {
        throw new Error(e.message);
      } finally {
        state.loading = false;
      }
    };

    const getBank = async () => {
      let { data } = await getBankList();
      state.bankList = data;
    };

    const handleSubmit = () => {
      if (state.isEdit) {
        formRef.value?.validate(async (valid) => {
          if (valid) {
            try {
              state.btnLoading = true;
              const fn = id ? updateMerchant : addMerchant;
              const { message } = await fn({
                merchantBanksEntity: {
                  ...bank.value,
                },
                merchantInfoEntity: {
                  ...info.value,
                },
                merchantLicenseEntity: {
                  ...license.value,
                  businessBeginDate: license.value.businessBeginDate ? moment(
                    license.value.businessBeginDate
                  ).format("YYYY-MM-DD") : '',
                  businessEndDate: license.value.businessEndDate ? moment(license.value.businessEndDate).format(
                    "YYYY-MM-DD"
                  ) : "",
                  establishDate: license.value.establishDate ? moment(license.value.establishDate).format(
                    "YYYY-MM-DD"
                  ) : "",
                },
              });
              ElMessage.success(message);
              let { $emit } = useEvent()
              $emit('success')
              store.dispatch("app/closeCurrent");
            } catch (e) {
              throw new Error(e.message);
            } finally {
              state.btnLoading = false;
            }
          }
        });
      } else {
        state.isEdit = true;
      }
    };
    const handleCancel = () => {
      store.dispatch("app/closeCurrent");
    };

    const handleDateChange = (v) => {
      license.value.businessBeginDate = v[0];
      license.value.businessEndDate = v[1];
    };

    const handleAreaChange = ({ areaId, areaName }) => {
      info.value.areaId = areaId;
      info.value.areaName = areaName;
    };

    onMounted(() => {
      getBank();
      if (id) {
        loadInfo();
      }
    });
    return {
      upload: uploadApplyLogo,
      merchantTypes,
      merchantBusinessTypes,
      state,
      formRef,
      bank,
      info,
      license,
      rules,
      types,
      models,
      appImageSize: THEMEVARS.appImageSize,
      handleSubmit,
      handleCancel,
      handleDateChange,
      handleAreaChange,
      query: store.getters["app/query"],
    };
  },
};
</script>
<style lang="scss">
.shopedit {
  .el-card__header {
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
  }

  &>.el-card__body {
    height: calc(100% - 50px);
  }

  .shopedit-footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>