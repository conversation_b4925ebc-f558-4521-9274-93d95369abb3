﻿import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';
import View from '@/components/view';

const routes = [
  {
    path: '/',
    redirect: '/main',
    meta: {
      noauth: true,
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/components/404'),
    meta: {
      noauth: true,
    }
  },
  {
    path: '/main',
    component: View,
    name: 'Main',
    redirect: { name: 'Panel' },
    children: [{
      path: 'panel',
      name: 'Panel',
      component: () => import(/* webpackChunkName: "main" */ '@/components/panel'),
    }]
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: '/404'
  }
]
const fn = CONFIG.ROUTE_TYPE === 'hash' ? createWebHashHistory: createWebHistory;
const router = createRouter({
  history: fn(CONFIG.IS_ROUTE_BASE_REWRITE ? CONFIG.ROUTE_BASE : '/eccard-ams'),
  scrollBehavior: () => ({ y: 0 }),
  routes
})

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router;
