<template>
  <!-- 电费缴费明细 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="缴费类型:">
          <el-select v-model="state.form.paymentType" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人员编号:">
          <el-input placeholder="人员编号" v-model="state.form.userCode" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="缴费时间:">
          <el-date-picker v-model="state.requestDate" type="datetimerange" :default-time="defaultTime" unlink-panels range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="电费缴费明细" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInput,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElPagination,
  ElMessage
} from "element-plus";
import { downloadXlsx, } from "@/utils/index.js"
import { requestDate, defaultTime } from "@/utils/reqDefaultDate";
import { timeStr, hasDateTimeRange } from "@/utils/date.js";
import {
  queryElectricControlPaymentDetailsPage,
  queryElectricControlPaymentDetailsExport,
} from "@/applications/eccard-finance/api";
const column = [
  { label: "用户编号", prop: "userCode", width: "" },
  { label: "用户姓名", prop: "userName", width: "" },
  { label: "缴费时间", prop: "paymentTime", width: "" },
  { label: "缴费金额", prop: "paymentAmount", width: "" },
  { label: "缴费类型", prop: "paymentType", width: "" },
]

export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    ElDatePicker,
    ElSelect,
    ElOption,
    ElInput,
    ElPagination
  },
  setup() {
    const typeList = [
      { label: "充值", value: "101" },
      { label: "转账", value: "302" },
    ]
    const state = reactive({
      loading: false,
      column,
      form: {
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      requestDate: requestDate(),
    });


    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.startDate = timeStr(state.requestDate[0]);
        params.endDate = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择缴费时间")
        return false
      }
      if (hasDateTimeRange([params.startDate, params.endDate], 6)) {
        ElMessage.error("查询日期范围不能操作6个月")
        return false
      }
      return params
    }


    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data: { page: { list, total }, paymentCount, } } = await queryElectricControlPaymentDetailsPage(params);
        if (code === 0) {
          state.dataList = list;
          state.total = total

          if (total) {
            state.detailList.push({
              paymentAmount: paymentCount,
              userCode: "合计",
            });
          }

        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await queryElectricControlPaymentDetailsExport(params);
        downloadXlsx(res, '电费缴费明细.xlsx')
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };
    const reset = () => {
      state.requestDate = requestDate()
      state.form = {
        pageNum: 1,
        pageSize: 10
      }
    };
    return {
      column,
      typeList,
      defaultTime,
      state,
      exportClick,
      getList,
      reset,
      handleSizeChange,
      handlePageChange,
    };
  },
};
</script>