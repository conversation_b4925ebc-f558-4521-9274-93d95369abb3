<template>
  <div class="box-dialog">
    <kade-table-wrap title="开户人员">
      <el-table style="width: 100%" :data="[selectPerson]" border stripe>
        <el-table-column width="150" label="用户编号" prop="userCode" align="center"></el-table-column>
        <el-table-column prop="userName" label="姓名" align="center">
        </el-table-column>
        <el-table-column label="组织机构" prop="deptName" align="center"></el-table-column>
        <el-table-column label="身份类别" prop="userRoleName" align="center"></el-table-column>
        <el-table-column label="账户状态" prop="acctStatus" align="center">
          <template #default="scope">
            {{
            filterDictionary(scope.row.acctStatus, state.accountStatusList)
            }}
          </template>
        </el-table-column>
        <el-table-column label="卡片类别" prop="ctName" align="center"></el-table-column>
        <el-table-column label="卡片状态" align="center"><template #default="scope">
            {{ filterDictionary(scope.row.cardStatus, state.cardStatusList) }}
          </template></el-table-column>
        <el-table-column width="153" label="联系方式" prop="userTel" align="center"></el-table-column>
      </el-table>
    </kade-table-wrap>
    <kade-table-wrap title="钱包设置" style="margin-top: 10px">
      <el-divider></el-divider>
      <el-form inline size="small" label-width="100px">
        <el-form-item label="卡片类型:">
          <el-input size="mini" :model-value="selectPerson.ctName" disabled></el-input>
        </el-form-item>
      </el-form>
      <el-table style="width: 100%" :data="state.form.walletList" v-loading="false" border stripe>
        <el-table-column label="钱包名称" prop="walletName" align="center"></el-table-column>

        <el-table-column label="钱包类型" prop="walletType" align="center">
          <template #default="scope">
            {{ filterDictionary(scope.row.walletType, state.walletTypeList) }}
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center">
          <template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="blue">
              未激活
            </div>
            <div v-else-if="scope.row.walletStatus == 'WALLET_FROZEN'" class="red">
              冻结
            </div>
            <div v-else-if="scope.row.walletStatus == 'WALLET_NORMAL'" class="green">
              正常
            </div>
          </template>
        </el-table-column>
        <el-table-column label="有效期（年）" align="center" width="120"><template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'">
              未激活
            </div>
            <el-input class="date-num-input" size="mini" type="number" :min="1" placeholder="请输入"
              v-model="scope.row.walletValidityDateNum" v-else></el-input>
          </template></el-table-column>
        <el-table-column label="有效期至" width="200" align="center">
          <template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'">
              未激活
            </div>
            <div v-else>
              {{
              scope.row.walletValidityDateNum
              ? timeStrDate(
              scope.row.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
              new Date().getTime()
              )
              : scope.row.walletValidityDate
              ? timeStrDate(scope.row.walletValidityDate)
              : ""
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center"><template #default="scope">
            <div v-if="scope.row.walletStatus == 'WALLET_NOT_ACTIVE'" class="green"
              @click="scope.row.walletStatus = 'WALLET_NORMAL'">
              激活
            </div>
            <div v-else-if="scope.row.walletStatus == 'WALLET_FROZEN'" class="green"
              @click="scope.row.walletStatus = 'WALLET_NORMAL'">
              启用
            </div>
            <div v-else-if="scope.row.walletStatus == 'WALLET_NORMAL'" class="green"
              @click="scope.row.walletStatus = 'WALLET_FROZEN'">
              冻结
            </div>
          </template></el-table-column>
      </el-table>
    </kade-table-wrap>
    <div class="submit-btn">
      <el-button @click="off()" size="mini">取&nbsp;&nbsp;消</el-button>
      <el-button @click="submitForm()" size="mini" type="primary" :loading="state.loading">确&nbsp;&nbsp;认</el-button>
    </div>
  </div>
</template>
<script>
import {
  ElDivider,
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElButton,
  ElInput,
  ElMessage,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import { dateStr } from "@/utils/date.js";
import { personWalletActive } from "@/applications/eccard-finance/api";
import { computed, watch } from "@vue/runtime-core";
export default {
  components: {
    ElDivider,
    ElTable,
    ElTableColumn,
    ElForm,
    ElFormItem,
    ElButton,
    ElInput,
  },
  props: {
    selectPerson: {
      types: Object,
      default: {},
    },
    accountStrategyList: {
      types: Array,
      default: [],
    },
    cardTypeList: {
      types: Array,
      default: [],
    },
    walletList: {
      types: Array,
      default: [],
    },
    allWalletList: {
      types: Array,
      default: [],
    },
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      form: {
        /* acctType: null, */
        userId: props.selectPerson.userId,
        walletList: props.walletList,
      },
      allWalletList: props.allWalletList,
      accountStrategy: "",
      accountStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_ACCOUNT_STATUS"), //账户状态
      walletTypeList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_TYPE"), //钱包类型
      walletStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_WALLET_STATUS"), //钱包状态
      cardStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "PERSON_CARD_STATUS"), //卡片状态
    });
    const timeStrDate = computed(() => {
      return dateStr;
    });

    watch(
      () => props.walletList,
      (val) => {
        console.log(state.allWalletList);
        state.form.walletList = val;
      }
    );
    watch(
      () => props.selectPerson,
      (val) => {
        console.log(val);
        state.form = {
          acctType: null,
          userId: null,
          walletList: null,
        };
        state.form.userId = val.userId;
      }
    );

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    const off = () => {
      context.emit("off", false);
    };
    const submitForm = () => {
      for (let item of state.form.walletList) {
        /*  if (!item.walletValidityDateNum) {
          ElMessage.error("请输入激活钱包有效期！");
          return false;
        } */
        if (item.walletValidityDateNum) {
          item.walletValidityDate = dateStr(
            item.walletValidityDateNum * 3600 * 1000 * 24 * 365 +
            new Date().getTime()
          );
        }

        item.walletValidityDate = dateStr(item.walletValidityDate);
      }
      state.form.walletList = [...state.form.walletList].map((item) => {
        return {
          walletCode: item.walletCode,
          walletName: item.walletName,
          walletStatus: item.walletStatus,
          walletType: item.walletType,
          walletValidityDate: item.walletValidityDate,
        };
      });

      console.log(state.form);
      state.loading = true
      personWalletActive(state.form)
        .then((res) => {
          if (res.code === 0) {
            ElMessage.success(res.message);
            context.emit("success", true);
            state.form = {
              acctType: null,
              userId: null,
              walletList: null,
            };
          } else {
            ElMessage.error(res.message);
          }
          state.loading = false
        })
        .catch(() => {
          state.loading = false
          // context.emit("success", false);
          // state.form = {
          //   acctType: null,
          //   userId: null,
          //   walletList: null,
          // };
        });
    };
    return {
      state,
      timeStrDate,
      filterDictionary,
      off,
      submitForm,
    };
  },
};
</script>
<style lang="scss" >
.box-dialog {
  padding: 10px 0;

  .el-table__row {
    height: 30px !important;
  }

  .account-tips {
    text-align: center;
    margin-bottom: 10px;
    color: #f00;
  }
}

.table-box {
  padding: 10px;
}

.submit-btn {
  text-align: center;
  margin: 10px auto;
}

.el-divider--horizontal {
  margin: 0 0 10px;
}
</style>