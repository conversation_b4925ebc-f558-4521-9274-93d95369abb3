<template>
  <div>
    <el-dialog :model-value="isShow" title="人员清单" width="60%" :before-close="beforeClose" :close-on-click-modal="false">
      <div>
        <div class="padding-form-box">
          <!-- <el-form inline size="small" label-width="100px">
            <el-form-item label="关键字">
              <el-input
                placeholder="请输入"
                v-model="state.form.keyWord"
              ></el-input>
            </el-form-item>
            <el-form-item label="组织机构:">
              <el-cascader
                :options="state.departCheckList"
                v-model="state.form.deptPath"
                :props="{
                  checkStrictly: true,
                  value: 'deptPath',
                  label: 'deptName',
                }"
                clearable
                @change="cascaderChange"
              />
            </el-form-item>
            <el-form-item label="身份类别:">
              <el-select
                clearable
                v-model="state.form.userRoleId"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in state.roleList"
                  :key="index"
                  :label="item.roleName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-button
              @click="getDataList()"
              size="small"
              type="primary"
              icon="el-icon-search"
              >搜索</el-button
            >
          </el-form> -->
          <el-table style="width: 100%" :data="state.personList" v-loading="false" highlight-current-row border stripe>
            <el-table-column show-overflow-tooltip label="用户编号" prop="userCode" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="姓名" prop="userName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="组织结构" prop="deptName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="身份类别" prop="roleName" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="补助金额" prop="subsidyAmount" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="生效时间" prop="takeEffectTime" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="过期时间" prop="invalidTime" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="到账状态" prop="entryStatus" align="center"></el-table-column>

          </el-table>
          <div class="pagination">
            <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
              layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
              @current-change="handlePageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="off()" size="mini">关&nbsp;&nbsp;闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { onMounted, reactive, watch } from "vue";
import {
  getSubsidyListByPage,
} from "@/applications/eccard-finance/api";
import {
  ElDialog,
  // ElCascader,
  ElButton,
  /*   ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption, */
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
export default {
  components: {
    "el-dialog": ElDialog,
    "el-button": ElButton,
    /*     "el-input": ElInput,
        "el-form": ElForm,
        "el-form-item": ElFormItem,
        "el-select": ElSelect,
        "el-option": ElOption, */
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    // ElCascader,
  },
  props: {
    isShow: {
      types: Boolean,
      default: false,
    },
    personListData: {
      types: Object,
      default: {},
    },
    projectId: {
      types: String,
      default: "",
    },
  },
  setup(props, context) {
    const state = reactive({
      form: {
        currentPage: 1,
        pageSize: 6,
        projectId: props.projectId,
      },
      personList: props.personListData.list,
      total: props.personListData.total,
    });
    watch(
      () => props.projectId,
      (val) => {
        console.log(1);
        state.form.projectId = val;
      }
    );

    watch(
      () => props.personListData,
      (val) => {
        state.personList = val.list;
        state.total = val.total;
      }
    );

    watch(
      () => props.isShow,
      (val) => {
        if (!val) {
          state.form = {
            currentPage: 1,
            pageSize: 6,
            projectId: props.projectId,
          };
        }
      }
    );
    const getDataList = () => {
      getSubsidyListByPage(state.form).then((res) => {
        state.personList = res.data.pageInfo.list;
        state.total = res.data.pageInfo.total;
      });
    };
    const cascaderChange = (val) => {
      if (val) {
        state.form.deptPath = val.length && val[val.length - 1];
      } else {
        state.form.deptPath = "";
      }
    };
    const off = () => {
      context.emit("off", false);
    };
    const beforeClose = () => {
      context.emit("off", false);
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getDataList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getDataList();
    };
    onMounted(() => {
    });

    return {
      state,
      getDataList,
      cascaderChange,
      off,
      beforeClose,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

.el-overlay {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
}

.el-dialog__footer {
  text-align: center;
}
</style>