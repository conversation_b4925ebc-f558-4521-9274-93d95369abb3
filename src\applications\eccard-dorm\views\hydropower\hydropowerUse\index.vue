<template>
    <kade-route-card>
        <kade-table-filter @search="handleSearch" @reset="handleReset">
            <el-form label-width="90px" inline size="mini">
                <kade-linkage-select :data="linkageData" :value="state.form" @change="linkageChange">
                </kade-linkage-select>
                <el-form-item label="汇总年月">
                    <el-date-picker v-model="state.form.totalYears" type="month" placeholder="请选择" />
                </el-form-item>
            </el-form>
        </kade-table-filter>
        <div class="total-box">
            <div class="total-item">
                <span>用水总量：{{ state.totalCount.waterTotal }}</span>
                <div></div>
            </div>
            <!--             <div>
                <span>用电总量：{{ state.totalCount.electricTotal }}</span>
                <div></div>
            </div> -->
        </div>
        <kade-table-wrap title="记录列表">
            <el-table border :data="state.dataList">
                <el-table-column show-overflow-tooltip v-for="(item, index) in column" :key="index" :label="item.label"
                    :prop="item.prop" :width="item.width" align="center"></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination v-model:currentPage="state.form.currentPage" v-model:page-size="state.form.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100, 500]" :small="small" background
                    layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </kade-table-wrap>
    </kade-route-card>
</template>

<script>
import { reactive } from "@vue/reactivity";
import { monthStr } from "@/utils/date.js";
import {
    ElForm,
    ElFormItem,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
} from "element-plus";
import { onMounted } from '@vue/runtime-core';
import { hydropowerMetering } from '@/applications/eccard-dorm/api.js'
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect";
const linkageData = {
    area: { label: '区域', valueKey: 'areaId', key: 'id' },
    buildingType: { label: '楼栋类型', valueKey: 'buildType' },
    building: { label: '楼栋', valueKey: 'buildId' },
    unit: { label: '单元', valueKey: 'unitNum' },
    floor: { label: '楼层', valueKey: 'floorNum' },
    room: { label: '房间', valueKey: 'roomId' }
}
const column = [
    { label: '区域', prop: 'areaName' },
    { label: '楼栋类型', prop: 'buildTypeName' },
    { label: '房间', prop: 'roomName', width: '300px' },
    { label: '入住人数', prop: 'stayCount' },
    { label: '汇总年月', prop: 'totalYears' },
    { label: '用水量', prop: 'waterValue' },
    // { label: '用电量', prop: 'electricValue' },
    { label: '历史总量', prop: 'originalUseValue' },
    { label: '历史总用量值', prop: 'historyTotal' },
]

export default {
    components: {
        ElForm,
        ElFormItem,
        ElDatePicker,
        ElTable,
        ElTableColumn,
        ElPagination,
        "kade-linkage-select": linkageSelect
    },
    setup() {
        const state = reactive({
            form: {
                totalYears: new Date(),
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            totalCount: {
                electricTotal: 0,
                waterTotal: 0
            },
            dataList: [],
        });

        const getList = async () => {
            let params = { ...state.form }
            if (params.totalYears) {
                params.totalYears = monthStr(params.totalYears)
            }
            state.loading = true
            try {
                let { data: { pageInfo: { total, list }, electricTotal, waterTotal } } = await hydropowerMetering(params)
                state.dataList = list
                state.total = total
                state.totalCount = { electricTotal, waterTotal }
                state.loading = false
            }
            catch {
                state.loading = false
            }
        };


        const linkageChange = (val) => {
            state.form = { ...state.form, ...val }
        }
        const handleSearch = () => {
            getList()
        };
        const handleReset = () => {
            state.form = {
                totalYears: new Date(),
                pageNum: 1,
                pageSize: 10
            }
        };
        const handleCurrentChange = (val) => {
            state.form.pageNum = val
            getList()
        };
        const handlePageChange = (val) => {
            state.form.pageNum = 1
                state.form.pageSize = val
            getList()
        };
        onMounted(() => {
            getList();
        })
        return {
            linkageData,
            state,
            column,
            linkageChange,
            handleSearch,
            handleReset,
            handleCurrentChange,
            handlePageChange,
        };
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-input--mini .el-input__inner) {
    width: 182px;
}

.total-box {
    display: flex;
    align-items: center;
    padding: 0 0 15px 10px;

    .total-item {
        margin-right: 60px;
    }
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
    width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
    width: 46px;
}
</style>