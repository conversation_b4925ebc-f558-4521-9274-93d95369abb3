<template>
  <el-dropdown trigger="click" ref="dropdownRef" @visible-change="visibleChange">
    <el-button size="mini"> 自定义显示字段 </el-button>
    <template #dropdown>
      <div class="padding-box">
        <el-table :data="state.columnList" ref="tableRef" border stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="字段" prop="label" width="200" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <el-divider></el-divider>
      <div class="footer">
        <el-button size="mini" @click="handleCancel">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dropdown>
</template>
<script>
import { ElDropdown, ElButton, ElTable, ElTableColumn, ElDivider, ElMessage } from "element-plus"
import { nextTick, onMounted, reactive, ref, } from 'vue'
export default {
  components: {
    ElDropdown, ElButton, ElTable, ElTableColumn, ElDivider
  },
  props: {
    column: Array
  },
  setup(props, context) {
    const dropdownRef = ref(null)
    const tableRef = ref(null)
    const state = reactive({
      columnList: [],
      saveColumnList: [],
      selectList: []
    })
    const handleSelectionChange = (val) => {
      state.selectList = val
    }
    const visibleChange = val => {
      if (val) {
        nextTick(() => {
          state.saveColumnList.forEach(item => {
            tableRef.value.toggleRowSelection(item, true)
          })
        })
      }
    }
    const handleConfirm = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请选择需要显示的字段")
      }
      state.selectList.sort((a, b) => a.sort - b.sort);
      context.emit("change", state.selectList)
      state.saveColumnList = state.selectList
      dropdownRef.value.visible = false
    }
    const handleCancel = () => {
      dropdownRef.value.visible = false

    }

    onMounted(() => {
      let list = props.column.map((item, index) => {
        return {
          ...item,
          sort: index + 1
        }
      })
      state.columnList = list
      state.saveColumnList = list
      visibleChange(true)
    })
    return {
      state,
      dropdownRef,
      tableRef,
      visibleChange,
      handleSelectionChange,
      handleConfirm,
      handleCancel
    }
  }
}
</script>
<style lang="scss" scoped>
.footer {
  padding: 0 20px 10px;
  display: flex;
  justify-content: flex-end;
}
.padding-box {
  max-height: 300px;
  overflow-y: auto;
}
</style>