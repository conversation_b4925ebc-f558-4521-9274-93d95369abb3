<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true">
    <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
      <el-form-item label="规则名称" prop="ovrName">
        <el-input placeholder="请输入（不超过30字）" v-model="state.model.ovrName" :readonly="disType=='info'" />
      </el-form-item>
      <el-form-item label="考勤组" prop="atgUsers" v-if="disType!='info'">
        <div class="divSelectUser">
          <el-tag class="ml-2" type="success" v-for="(item,index) in state.model.attendGroupList" :key="index + '111'">{{item.atgName}}</el-tag>
        </div>
        <el-button type="primary" link @click="state.model.isShowSelectedGroup = true;">选择</el-button>
      </el-form-item>

      <el-form-item label="规则设置" prop="atgUsers">
        <el-tabs type="border-card" :model-value="'work'">
          <el-tab-pane label="工作日" name="work">
            <el-row :gutter="15">
              <el-col :sm="24">
                是否计算加班：
                <el-switch v-model="state.model.ovrWOvertime" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                                  inactive-value="0" inactive-text="关" active-text="开"></el-switch>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :sm="5">
                计算方式：
              </el-col>
              <el-col :sm="19">
                <el-row :gutter="15">
                  <el-col :sm="24">
                    <el-radio-group v-model="state.model.ovrWTimestype" class="ml-4">
                      <el-radio label="无需审批，按打卡时长计算" size="large">无需审批，按打卡时长计算</el-radio>
                      <el-radio label="需审批，按打卡时长计算" size="large">需审批，按打卡时长计算</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row :gutter="15">
                  <el-col :sm="24">
                    下班
                    <el-input-number
                      v-model="state.model.ovrWAfterTimes"
                      :min="0"
                      :max="60"
                      controls-position="right"
                    />分钟后计算加班时间
                  </el-col>
                </el-row>
                <el-row :gutter="15">
                  <el-col :sm="24">
                    少于
                    <el-input-number
                      v-model="state.model.ovrWLessTimes"
                      :min="0"
                      :max="60"
                      controls-position="right"
                    />分钟，不计入加班
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :sm="24">
                扣除休息时间：
                <el-switch v-model="state.model.ovrWDeduct" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                           inactive-value="0" inactive-text="关" active-text="开"></el-switch><span style="color:red">休息时间为班次的休息时间</span>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="休息日" name="rest">

            <el-row :gutter="15">
              <el-col :sm="24">
                是否计算加班：
                <el-switch v-model="state.model.ovrROvertime" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                           inactive-value="0" inactive-text="关" active-text="开"></el-switch>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :sm="5">
                计算方式：
              </el-col>
              <el-col :sm="19">
                <el-row :gutter="15">
                  <el-col :sm="24">
                    <el-radio-group v-model="state.model.ovrRTimestype" class="ml-4">
                      <el-radio label="无需审批，按打卡时长计算" size="large">无需审批，按打卡时长计算</el-radio>
                      <el-radio label="需审批，按打卡时长计算" size="large">需审批，按打卡时长计算</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row :gutter="15">
                  <el-col :sm="24">
                    下班
                    <el-input-number
                      v-model="state.model.ovrRAfterTimes"
                      :min="0"
                      :max="60"
                      controls-position="right"
                    />分钟后计算加班时间
                  </el-col>
                </el-row>
                <el-row :gutter="15">
                  <el-col :sm="24">
                    少于
                    <el-input-number
                      v-model="state.model.ovrRLessTimes"
                      :min="0"
                      :max="60"
                      controls-position="right"
                    />分钟，不计入加班
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :sm="24">
                扣除休息时间：
                <el-switch v-model="state.model.ovrRDeduct" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                           inactive-value="0" inactive-text="关" active-text="开"></el-switch><span style="color:red">休息时间为班次的休息时间</span>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="节假日" name="holiday">

            <el-row :gutter="15">
              <el-col :sm="24">
                是否计算加班：
                <el-switch v-model="state.model.ovrHOvertime" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                           inactive-value="0" inactive-text="关" active-text="开"></el-switch>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :sm="5">
                计算方式：
              </el-col>
              <el-col :sm="19">
                <el-row :gutter="15">
                  <el-col :sm="24">
                    <el-radio-group v-model="state.model.ovrHTimestype" class="ml-4">
                      <el-radio label="无需审批，按打卡时长计算" size="large">无需审批，按打卡时长计算</el-radio>
                      <el-radio label="需审批，按打卡时长计算" size="large">需审批，按打卡时长计算</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row :gutter="15">
                  <el-col :sm="24">
                    下班
                    <el-input-number
                      v-model="state.model.ovrHAfterTimes"
                      :min="0"
                      :max="60"
                      controls-position="right"
                    />分钟后计算加班时间
                  </el-col>
                </el-row>
                <el-row :gutter="15">
                  <el-col :sm="24">
                    少于
                    <el-input-number
                      v-model="state.model.ovrHLessTimes"
                      :min="0"
                      :max="60"
                      controls-position="right"
                    />分钟，不计入加班
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :sm="24">
                扣除休息时间：
                <el-switch v-model="state.model.ovrHDeduct" :active-color="themes.primaryColor" :inactive-color="themes.dangerColor" active-value="1"
                           inactive-value="0" inactive-text="关" active-text="开"></el-switch><span style="color:red">休息时间为班次的休息时间</span>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form-item>

    </el-form>
    <template #footer>
      <p style="text-align: center">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>

    <selected-group :modelValue="state.model.isShowSelectedGroup" :data="state.model.attendGroupList" :title="'选择考勤组'" @update:modelValue="selectedUsersClose" />
  </el-dialog>
</template>
<style scoped>
  .ml-4 .radio{
    content: "";
    background: #f4f4f4;
    border-radius: 100%;
    border: 1px solid #b4b4b4;
    display: inline-block;
    width: 1.4em;
    height: 1.4em;
    position: relative;
    top: -0.2em;
    margin-right: 1em;
    vertical-align: top;
    cursor: pointer;
    text-align: center;
    transition: all 250ms ease;
  }
</style>
<script>
  import { reactive, watch , ref , onMounted, computed} from "vue";
  import { updateAttentionOverTimeRule , addAttentionOverTimeRule , getAttentionOverTimeRuleDetail } from "@/applications/eccard-ams/api";
  import selectedGroup from "@/applications/eccard-ams/views/attendovertime/components/selectedgroup.vue"

  import {
    ElTabs,
    ElTabPane,
    ElRow,
    ElCol,
    ElSwitch,
    // ElTimeSelect,
    ElInputNumber,
    ElRadioGroup,
    ElTag,
    ElRadio,
    // ElSelect,
    // ElOption,
    ElInput,
    ElForm,
    ElFormItem,
    ElButton,
    // ElPagination,
    ElMessage,
    // ElMessageBox,
    ElDialog,
  } from 'element-plus';

  const getDefaultModel = () => ({
    attendGroupList:[],
    isShowSelectedGroup:false,
    atgType:'',
    selectedData:null,
    ovrWAfterTimes:0,
    ovrWLessTimes:0,
    ovrRAfterTimes:0,
    ovrRLessTimes:0,
    ovrHAfterTimes:0,
    ovrHLessTimes:0,
    ovrWTimestype:'',
    ovrRTimestype:'',
    ovrHTimestype:'',
  });
  export default {
    emits: ["close"],
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      data: {
        type: Object,
        default: () => ({}),
      },
      type :{
        type: String,
        default: "",
      },
    },
    components: {
      selectedGroup,
      ElTabs,
      ElTabPane,
      ElSwitch,
      ElRow,
      ElCol,
      ElTag,
      ElRadioGroup,
      ElRadio,
      // ElTimeSelect,
      ElInputNumber,
      // ElSelect,
      // ElOption,
      ElInput,
      ElForm,
      ElFormItem,
      ElButton,
      ElDialog,
    },
    setup(props, context) {
      const formRef = ref(null);
      const btnLoading = ref(false);
      const disType = ref(props.type);
      const state = reactive({
        model: getDefaultModel(),
      });
      const rules = {
        ovrName: [
          { required: true, message: "请输入规则名称" },
          { max: 30, message: "规则名称不能超过30个字符" },
        ],
      };
      const selectedManagerUserClose = (val)=>{
        if(val){
          state.model.manageUser.userId = val.userId;
          state.model.manageUser.userNum = val.userAccount;
          state.model.manageUser.userName = val.userName;
        }
        state.model.isShowSelectedManagerUser = false;
      };
      const selectedUsersClose = (vals)=>{
        if(vals){
          state.model.attendGroupList = [];
          vals.forEach((item) =>{
            state.model.attendGroupList.push({
              atgId:item.atgId ,
              atgName:item.atgName ,
            });
          });
        }
        state.model.isShowSelectedGroup = false;
      };
      const cancel = () => {
        formRef.value?.resetFields();
        context.emit("update:modelValue", false);
      };
      const submit = () => {
        formRef.value?.validate(async (valid) => {
          if (valid) {
            if(state.model.attendGroupList.length == 0){
              ElMessage.error('考勤组信息不能为空！');return;
            }
            if((state.model.ovrWAfterTimes == '' && state.model.ovrWAfterTimes != 0) || (state.model.ovrWLessTimes == '' && state.model.ovrWLessTimes != 0)){
              ElMessage.error('工作日不计入加班时间信息不能为空！');return;
            }
            if((state.model.ovrRAfterTimes == '' && state.model.ovrRAfterTimes != 0) || (state.model.ovrRLessTimes == '' && state.model.ovrRLessTimes != 0)){
              ElMessage.error('休息日不计入加班时间信息不能为空！');return;
            }
            if((state.model.ovrHAfterTimes == '' && state.model.ovrHAfterTimes != 0) || (state.model.ovrHLessTimes == '' && state.model.ovrHLessTimes != 0)){
              ElMessage.error('节假日不计入加班时间信息不能为空！');return;
            }
            if(!state.model.ovrWTimestype){
              ElMessage.error('工作日加班计算方式信息不能为空！');return;
            }
            if(!state.model.ovrRTimestype){
              ElMessage.error('休息日加班计算方式信息不能为空！');return;
            }
            if(!state.model.ovrHTimestype){
              ElMessage.error('节假日加班计算方式信息不能为空！');return;
            }
            try {
              btnLoading.value = true;
              const fn = props.data?.ovrId ? updateAttentionOverTimeRule : addAttentionOverTimeRule;
              /*
               atgName
               */
              state.model["attendGroupIdList"] = state.model.attendGroupList.map(item=>{return item.atgId}); //分组ID
              const { message, code } = await fn(state.model);
              if (code === 0) {
                ElMessage.success(message);
              } else {
                ElMessage.error(message);
              }
              context.emit("update:modelValue", true);
            } catch (e) {
              throw new Error(e.message);
            } finally {
              btnLoading.value = false;
            }
          }
        });
      };
      const attrs = computed(() => {
        // eslint-disable-next-line no-unused-vars
        const { modelValue, role, ...attrs } = props;
        return attrs;
      });

      onMounted(() => {
      });

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            if (props.data?.ovrId) {
              const { code, message , data } = await getAttentionOverTimeRuleDetail(props.data.ovrId);
              if(code != 0){
                ElMessage.error(message);return;
              }
              data["attendGroupList"] = data.attendGroups.map(item=>{return {atgId:item.atgId , atgName:item.atgName}});
              let objbase = getDefaultModel();
              state.model = Object.assign(objbase, data);
              state.model.ovrWOvertime = state.model.ovrWOvertime ? state.model.ovrWOvertime.toString() : '0';
              state.model.ovrWDeduct = state.model.ovrWDeduct ? state.model.ovrWDeduct.toString() : '0';
              state.model.ovrROvertime = state.model.ovrROvertime ? state.model.ovrROvertime.toString() : '0';
              state.model.ovrRDeduct = state.model.ovrRDeduct ? state.model.ovrRDeduct.toString() : '0';
              state.model.ovrHOvertime = state.model.ovrHOvertime ? state.model.ovrHOvertime.toString() : '0';
              state.model.ovrHDeduct = state.model.ovrHDeduct ? state.model.ovrHDeduct.toString() : '0';
            } else {
              state.model = getDefaultModel();
            }
          }
        }
      );
      return {
        attrs,
        // update,
        formRef,
        cancel,
        submit,
        rules,
        labelWidth: THEMEVARS.formLabelWidth,
        state,
        disType,
        btnLoading,
        themes: THEMEVARS,
        selectedUsersClose,
        selectedManagerUserClose,
      };
    },
  };
</script>
