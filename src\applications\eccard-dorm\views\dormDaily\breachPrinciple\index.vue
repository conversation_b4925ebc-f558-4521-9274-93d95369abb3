<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="80px" size="mini">
        <el-form-item label="关键字">
          <el-input v-model="state.form.keyWord" placeholder="输入人员编号或人员姓名搜索" size="mini"></el-input>
        </el-form-item>
        <kade-linkage-select :value="state.form" :data="linkageData" @change="linkageChange" />
        <el-form-item label="处理状态">
          <el-select clearable v-model="state.form.processingStatus" placeholder="全部">
            <el-option v-for="(item, index) in processingList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否通报">
          <el-select clearable v-model="state.form.whetherNotify" placeholder="全部">
            <el-option v-for="(item, index) in whetherNotifyList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查日期">
          <el-date-picker style="width: 200px" v-model="state.loginTime" type="daterange" range-separator="~" @change="timeChange" unlink-panels>
          </el-date-picker>
        </el-form-item>
        <el-form-item label="违规类型">
          <el-select clearable v-model="state.form.breachType" placeholder="全部">
            <el-option v-for="(item, index) in breachTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="违规违纪列表" v-loading="state.loading">
      <template #extra>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="edit('add',{})">新增</el-button>
        <el-button type="primary" icon="el-icon-delete-solid" size="mini" @click="batchDel()">批量删除</el-button>
      </template>
      <el-table :data="state.dataList" border style="width: 100%" height="55vh" v-loading="state.loading" @selection-change="handleSelectChange">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item,index) in column" :key="index" :prop="item.prop" :label="item.label" align="center">
          <template #default="scope">
            {{dictionaryFilter(scope.row[item.prop])}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" size="mini" @click="edit('edit',scope.row)" class="green">编辑</el-button>
            <el-button type="text" size="mini" @click="handleDel(scope.row)" class="green">删除</el-button>
            <el-button type="text" size="mini" @click="edit('details',scope.row)" class="green">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage" :page-size="10" layout="total, prev, pager, next, jumper" :total="state.total">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-edit v-model:modelValue="state.isShow" :type="state.type" :selectRow="state.selectRow" @update:modelValue="close"></kade-edit>
  </kade-route-card>

</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
  ElMessageBox,
  ElSelect,
  ElOption,
  ElDatePicker,

} from "element-plus";
import { reactive } from "@vue/reactivity";
import edit from "./components/edit.vue"
import { onMounted } from "vue";
import { useDict } from "@/hooks/useDict"
import { timeStr } from "@/utils/date.js"
import {
  getBreachPrincipleList, deleteBreachPrincipleInfo,batchBreachPrinciple } from "@/applications/eccard-dorm/api";
import linkageSelect from "@/applications/eccard-dorm/components/linkageSelect.vue"
const linkageData = {
  area: { label: '所属区域', valueKey: "areaPath", key: "areaPath" },
  building: { label: '楼栋', valueKey: "buildId" },
  unit: { label: '单元', valueKey: "unitNum" },
  floor: { label: '楼层', valueKey: "floorNum" },
  room: { label: '房间', valueKey: "roomId" },
}
const column = [
  { label: '人员编号', prop: 'userCode' },
  { label: '人员姓名', prop: 'userName' },
  { label: '所属区域', prop: 'areaName' },
  { label: '房间', prop: 'roomString' },
  { label: '违规时间', prop: 'breachTime' },
  { label: '违规类型', prop: 'breachType', isDict: true },
  { label: '处理状态', prop: 'processingStatus', isDict: true },
  { label: '处理时间', prop: 'processingTime' },
  { label: '是否通报', prop: 'whetherNotify', isDict: true },
  { label: '检查人', prop: 'checkPerson' }
]
export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElSelect,
    ElOption,
    ElDatePicker,
    "kade-edit": edit,
    "kade-linkage-select": linkageSelect,
  },
  setup() {
    const processingList = useDict('DORM_PROCESSING_STATUS') //处理状态
    const whetherNotifyList = useDict('SYS_BOOL_STRING') //是否通报
    const breachTypeList = useDict('DORM_ILLEGAL_TYPE') //违规类型
    const state = reactive({
      isShow: false,
      selectRow: "",
      type: '',
      form: {
        currentPage: 1,
        pageSize: 10
      },
      loginTime: [],
      dataList: [],
      loading: false,
      total: 0,
      selectList: []
    });

    const edit = (type, row) => {
      state.type = type
      state.selectRow = row
      state.isShow = true
    };
    const handleSearch = () => {
      getList();
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.loginTime = []
    }
    const linkageChange = (val) => {
      state.form = { ...state.form, ...val }
      console.log(state.form);
    }
    const getList = async () => {
      if (state.loginTime && state.loginTime.length) {
        state.form.checkBeginDate = timeStr(state.loginTime[0])
        state.form.checkEndDate = timeStr(state.loginTime[1])
      } else {
        delete state.form.checkBeginDate
        delete state.form.checkEndDate
      }
      let param = { ...state.form }
        state.loading = true

      try {
        let { data: { list, total } } = await getBreachPrincipleList(param)
        state.dataList = list
        state.total = total

        state.loading = false
      }
      catch{
        state.loading = false
      }
    };

    const handleDel = (val) => {
      ElMessageBox.confirm(`确认删除这条记录吗？`, {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        const { code, message } = await deleteBreachPrincipleInfo(val.id)
        if (code === 0) {
          ElMessage.success(message),
            getList()
        }
      })
    }
    const handleSelectChange = (val) => {
      state.selectList = val
    }
    const batchDel = () => {
      if (!state.selectList.length) {
        return ElMessage.error("请先选择需要删除的信息！");
      }
      ElMessageBox.confirm(`确认删除已选择信息？`,`提示`,{
        type:'warning',
        confirmButtonText:'确定',
        cancelButtonText:'取消'
      }).then(async()=>{
        let param = state.selectList.map((item)=>item.id).join(',')
        let { code,message } = await batchBreachPrinciple(param)
        if(code === 0){
          ElMessage.success(message)
          getList()
        }
      })
    }
    const close = (val) => {
      if (val) {
        getList()
      }
      state.isShow = false
    };
    const handleSizeChange = val => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = val => {
      state.form.currentPage = val
       getList()
    };
    onMounted(() => {
      getList();
    });
    return {
      linkageData,
      state,
      close,
      linkageChange,
      processingList,
      breachTypeList,
      edit,
      handleSearch,
      column,
      handleReset,
      handleSizeChange,
      whetherNotifyList,
      handleDel,
      batchDel,
      handleSelectChange,
      handleCurrentChange,
    };
  }
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}
:deep(.el-dialog) {
  border-radius: 8px;
}
:deep(.el-form-item__content) {
  .el-select {
    width: 200px;
  }
  .el-input__inner {
    width: 200px;
  }
}
:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__footer) {
  border: 0;
  text-align: center;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 15px;
}
:deep(.el-textarea__inner) {
  width: 540px;
}
</style>
