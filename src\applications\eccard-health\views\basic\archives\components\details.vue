<template>
  <el-dialog v-loading="state.loading" :model-value="modelValue" title="人员信息详情" width="1200px" :before-close="handleClose">
    <div class="content-box">
      <kade-tab-wrap :tabs="tabs" style="margin-top: 20px" v-model="state.tab">
        <template #info><health-info :rowData="rowData" @success="success" /> </template>
        <template #illness> <health-illness :rowData="rowData" /></template>
      </kade-tab-wrap>
    </div>
  </el-dialog>
</template>
<script>
import { ElDialog } from "element-plus"
import { reactive, watch } from 'vue'
import info from "./info/info"
import illness from "./illness/illness"
const tabs = [
  {
    name: "info",
    label: "基础健康信息",
  },
  {
    name: "illness",
    label: "既往病史/手术史",
  },
];
export default {
  components: {
    ElDialog,
    "health-info": info,
    "health-illness": illness
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  setup(props, context) {
    watch(() => props.modelValue, async val => {
      if (val) {
        state.tab = "info"
      }
    })
    const state = reactive({
      tab: "info"
    });
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    const success = () => {
      context.emit("success")
      handleClose()
    }
    return {
      tabs,
      state,
      handleClose,
      success
    }
  }
}
</script>

<style lang="scss" scoped>
.content-box {
  padding: 0 0 20px 0px;
}
</style>