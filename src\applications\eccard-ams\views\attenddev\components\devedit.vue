<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          设备基本信息
        </div>
      </template>
      <el-form ref="formRef" :label-width="labelWidth" :inline="true" :rules="rules" :model="state.model" size="small">
        <el-form-item label="设备型号" prop="atdModel">
          <el-input placeholder="" v-model="state.model.atdModel" />
        </el-form-item>
        <el-form-item label="所属区域" prop="areaName">
          <el-input placeholder="" v-model="state.model.areaName" />
        </el-form-item>
        <el-form-item label="设备机号" prop="atdNum">
          <el-input placeholder="" v-model="state.model.atdNum" />
        </el-form-item>
        <el-form-item label="设备名称" prop="atdName">
          <el-input placeholder="" v-model="state.model.atdName" />
        </el-form-item>
        <el-form-item label="设备使用状态" prop="atdStatus">
          <el-input placeholder="" v-model="state.model.atdStatus" />
        </el-form-item>
        <el-form-item label="所属工作站" prop="wstName">
          <el-input placeholder="" v-model="state.model.wstName" />
        </el-form-item>
        <el-form-item label="连接类型" prop="wstConType">
          <el-input placeholder="" v-model="state.model.wstConType" />
        </el-form-item>
        <el-form-item label="设备IP" prop="atdIp">
          <el-input placeholder="" v-model="state.model.atdIp" />
        </el-form-item>
        <el-form-item label="固件版本" prop="atdHversion">
          <el-input placeholder="" v-model="state.model.atdHversion" />
        </el-form-item>
        <el-form-item label="设备SN号" prop="atdSn">
          <el-input placeholder="" v-model="state.model.atdSn" />
        </el-form-item>
        <el-form-item label="设备添加日期" prop="createTime">
          <el-input placeholder="" :model-value="getDateTimeStr(state.model.createTime)" />
        </el-form-item>
        <el-form-item label="设备位置" prop="atdAddress">
          <el-input placeholder="" v-model="state.model.atdAddress" />
        </el-form-item>
        <el-form-item label="备注" prop="atdDesc">
          <el-input type="textarea" :rows="2" placeholder="" v-model="state.model.atdDesc" />
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="box-card" style="margin-top:20px;margin-bottom:20px">
      <template #header>
        <div class="card-header">
          设备个性化信息
        </div>
      </template>
      <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
        <el-form-item label="端口号" prop="atdPort">
          <el-input placeholder="" v-model="state.model.atdPort" />
        </el-form-item>
      </el-form>
    </el-card>

  </div>
</template>
<script>

import { computed, reactive, ref, watch } from "vue";
import { timeStr } from "@/utils/date.js"

import {
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElCard
} from "element-plus";
import { addAttentionAttendDevGroupAll, editAttentionAttendDevGroupAll } from "@/applications/eccard-ams/api";
// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    type:{
      type: String,
      default: "",
    },
    data:{
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    ElForm,
    ElFormItem,
    ElInput,
    ElCard,
  },
  setup(props, context) {
    const formRef = ref(null);
    const btnLoading = ref(false);
    const state = reactive({
      model: props.data,
    });
    const rules = {
      degName: [
        { required: true, message: "请输入设备分组名称" },
        { max: 50, message: "类别名称不能超过50个字符" },
      ],
    };
    const cancel = () => {
      formRef.value?.resetFields();
      context.emit("update:modelValue", false);
    };
    const getDateTimeStr=(vla)=>{
      if(vla){
        return timeStr(vla);
      }else{
        return "";
      }
    };
    const submit = () => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          try {
            btnLoading.value = true;
            const fn = props.data?.degId ? addAttentionAttendDevGroupAll : editAttentionAttendDevGroupAll;
            const { message, code } = await fn(state.model);
            if (code === 0) {
              ElMessage.success(message);
            } else {
              ElMessage.error(message);
            }
            context.emit("update:modelValue", false);
            context.emit("change");
          } catch (e) {
            throw new Error(e.message);
          } finally {
            btnLoading.value = false;
          }
        }
      });
    };
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, role, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit("close", v);
    };
    watch(
      () => props.data,
      (n) => {
        if (n) {
          state.model = props.data;
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      cancel,
      submit,
      rules,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      btnLoading,
      themes: THEMEVARS,
      getDateTimeStr,
    };
  },
};
</script>
