<template>
  <!-- 余额汇总报表 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="汇总时间:">
          <el-date-picker v-model="state.dateTime" type="datetime" placeholder="请选择日期时间" />
        </el-form-item>
        <el-form-item label="按入账时间:">
          <el-checkbox v-model="state.timeType"></el-checkbox>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="余额汇总报表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
        <el-button @click="printClick()" size="mini" class="btn-purple" icon="el-icon-printer">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElCheckbox,
  ElTable,
  ElTableColumn
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import { downloadXlsx, print } from "@/utils/index.js"
import { reactive } from "@vue/reactivity";
import {
  balanceReportList,
  balanceReportExport,
  balanceReportPrint
} from "@/applications/eccard-finance/api";
import { onMounted } from "@vue/runtime-core";
const column = [
  { label: "汇总部门", prop: "deptName", width: "" },
  { label: "现金余额汇总", prop: "cashBalance", width: "" },
  { label: "补助余额汇总", prop: "subsidyBalance", width: "" },
  { label: "总计", prop: "totalBalance", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    ElDatePicker,
    ElCheckbox,

  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      dateTime: new Date(
        new Date(new Date().toLocaleDateString()).getTime() +
        24 * 60 * 60 * 1000 -
        1
      ),
      timeType: true,
      detailList: [],

    });
    const getList = async () => {

      let params
      if (state.timeType) {
        params = {
          endDate: timeStr(state.dateTime)
        }
      } else {
        params = {
          startDate: timeStr(state.dateTime)
        }
      }
      state.loading = true;
      try {
        let { code, data } = await balanceReportList(params);
        if (code === 0) {
          state.detailList = data;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };

    const exportClick = async () => {

      let params
      if (state.timeType) {
        params = {
          endDate: timeStr(state.dateTime)
        }
      } else {
        params = {
          startDate: timeStr(state.dateTime)
        }
      }
      state.loading = true
      try {
        let res = await balanceReportExport(params);
        downloadXlsx(res, "余额汇总报表.xlsx")
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const printClick = async () => {
      let params
      if (state.timeType) {
        params = {
          endDate: timeStr(state.dateTime)
        }
      } else {
        params = {
          startDate: timeStr(state.dateTime)
        }
      }
      state.loading = true;
      try {
        let { data, code } = await balanceReportPrint(params);
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const reset = () => {
      state.dateTime = new Date(
        new Date(new Date().toLocaleDateString()).getTime() +
        24 * 60 * 60 * 1000 -
        1
      )
      state.timeType = true
    };
    onMounted(() => {
      // getList();
    });
    return {
      column,
      state,
      timeStr,
      exportClick,
      printClick,
      getList,
      reset,

    };
  },
};
</script>