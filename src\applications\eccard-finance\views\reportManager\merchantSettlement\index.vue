<template>
  <!-- 商户结算 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="商户名称:">
          <el-input placeholder="商户名称搜索" v-model="state.form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="结算类型:">
          <el-select clearable v-model="state.form.settlementType" placeholder="请选择">
            <el-option v-for="(item, index) in state.systemUserList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算日期:">
          <el-date-picker v-model="state.requestDate" type="daterange" unlink-panels range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="商户结算报表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出查询明细</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="(item) in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total" @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import {
  ElButton,
  ElForm,
  ElInput,
  ElFormItem,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElDatePicker,
  ElMessage
} from "element-plus";
import { downloadXlsx } from "@/utils"
import { timeStr, dateStr } from "@/utils/date.js";
import { reactive } from "@vue/reactivity";
import {
  getMerchantSettlementInfoPage,
  merchantSettlementInfoExport,
  tradeMode,
} from "@/applications/eccard-finance/api";
import { onMounted, } from "@vue/runtime-core";
import { requestDate, defaultTime } from "@/utils/reqDefaultDate";
const column = [
  { label: "商户名称", prop: "merchantName", width: "" },
  { label: "结算日期", prop: "settlementDate", width: "170", render: (val) => val && dateStr(val) },
  { label: "结算类型", prop: "settlementType", width: "" },
  { label: "期初结转", prop: "openingBalance", width: "" },
  { label: "本期收入", prop: "currentIncome", width: "" },
  { label: "本期支出", prop: "currentExpend", width: "" },
  { label: "本期结余", prop: "currentBalance", width: "" },
  { label: "累计结余", prop: "totalBalance", width: "" },
  { label: "结算时间", prop: "createTime", width: "170", render: (val) => val && timeStr(val) },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    ElInput,
    ElDatePicker,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {
        pageNum: 1,
        pageSize: 6,
      },
      detailList: [],
      total: 0,
      requestDate: requestDate(),
      tradeModeList: [],
      systemUserList: [
        { label: "金额结算", value: "CASH" },
        { label: "次数结算", value: "FREQUENCY" },
      ],
    });
    //获取交易方式
    const getTradeModeList = () => {
      tradeMode().then((res) => {
        state.tradeModeList = res.data;
      });
    };


    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.startTime = timeStr(state.requestDate[0]);
        params.endTime = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择结算日期")
        return false
      }
      return params
    }

    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data } = await getMerchantSettlementInfoPage(params);
        if (code === 0) {
          let {
            page: { list, total },
            currentBalanceCount,
            currentExpendCount,
            currentIncomeCount,
            endtermBalanceCount,
            openingBalanceCount,
            totalBalanceCount,
          } = data;

          state.detailList = list;

          if (state.detailList.length) {
            state.detailList.push({
              openingBalance: openingBalanceCount,
              endtermBalance: endtermBalanceCount,
              currentIncome: currentIncomeCount,
              currentExpend: currentExpendCount,
              currentBalance: currentBalanceCount,
              totalBalance: totalBalanceCount,
              merchantName: "合计",
            });
          }
          state.total = total;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;

      }
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await merchantSettlementInfoExport(params);
        downloadXlsx(res, '商户结算报表.xlsx')
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };

    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    const reset = () => {
      state.form = {
        pageNum: 1,
        pageSize: 6,
      };
      state.requestDate = requestDate()
    };
    onMounted(() => {
      getTradeModeList();
    });
    return {
      defaultTime,
      column,
      state,
      timeStr,
      exportClick,
      getList,
      handlePageChange,
      handleSizeChange,
      reset,
    };
  },
};
</script>