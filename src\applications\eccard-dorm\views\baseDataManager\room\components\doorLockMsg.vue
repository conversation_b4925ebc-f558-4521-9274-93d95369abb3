<template>
  <el-dialog :model-value="modelValue" title="门锁信息" width="1000px" :before-close="handleClose">
    <div class="padding-box" v-loading="state.loading">
      <el-table :data="state.dataList" border>
        <el-table-column show-overflow-tooltip prop="userName" label="人员姓名" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip prop="value" label="卡号" align="center">
          <template #default="scope">
            {{ scope.row.keyType==2?scope.row.value:''  }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="value" label="密码" align="center">
          <template #default="scope">
            {{ scope.row.keyType==35?scope.row.value:''  }}
          </template>
        </el-table-column>
        <el-table-column  prop="value" label="授权时间" align="center" width="320px">
          <template #default="scope">
            {{ timeStr( scope.row.startTime) }} &nbsp;~ &nbsp;{{ timeStr( scope.row.endTime) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize" :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ElDialog, ElTable, ElTableColumn, ElPagination, } from "element-plus"
import { reactive, watch } from 'vue'
import { timeStr } from "@/utils/date.js"
import { LockDeviceAuthQuery, } from "@/applications/eccard-dorm/api";

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    rowData: {
      types: Object,
      default: null
    },
  },
  components: {
    ElDialog,
    ElTable,
    ElTableColumn,
    ElPagination,
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      form: {
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      total: 0,
      ImportRoomInfoRes: {}
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = {
          currentPage: 1,
          pageSize: 10,
          roomId: props.rowData.id
        }
        state.dataList = []
        state.total = 0
        getList()
      }
    })
    const getList = async () => {
      state.loading = true
      try {
        let { data: { dataList, totalCount } } = await LockDeviceAuthQuery(state.form)
        state.dataList = dataList
        state.total = totalCount
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      timeStr,
      state,
      handleSizeChange,
      handleCurrentChange,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
</style>