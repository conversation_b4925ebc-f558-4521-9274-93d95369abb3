<template>
  <div class="notice-msg">
    <div class="notive-title">通知公告</div>
    <el-carousel height="100%" direction="vertical" :autoplay="true" :interval="5000" indicator-position="none">
      <el-carousel-item v-for="item in 1" :key="item" style="color:#fff">
        <div v-html="state.noticeData.msgContent"></div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { reactive, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from "vue-router"
import { screenNotice } from "@/applications/eccard-dorm/api.js"
import { ElCarousel, ElCarouselItem } from "element-plus"
export default {
  components: {
    ElCarousel, ElCarouselItem
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      timer: null,
      noticeData: {},
    })
    const getData = async () => {
      let { data } = await screenNotice(route.params)
      state.noticeData = data
    }

    onMounted(() => {
      getData()
      state.timer = setInterval(() => {
        getData()
      }, 10000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      state
    }
  }
}
</script>\
<style lang="scss" scoped>
.notice-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .notive-title {
    font-weight: 700;
    font-size: 18px;
    color: #0EE4F9;
    margin: 5px 0 10px;
  }
}

.el-carousel {
  width: 100%;
  height: 100%;
}
</style>