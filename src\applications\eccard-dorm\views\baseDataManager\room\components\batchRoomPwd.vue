<template>
  <el-dialog :model-value="modelValue" :title="type=='add'?'批量设置房间密码':'批量删除房间密码'" width="1400px" :before-close="handleClose">
    <div class="padding-box">
      <kade-route-card>
        <template #header>
          <span class="header-title">选择房间</span>
        </template>
        <kade-select-table :isShow="modelValue" :value="[]" :column="column" :linkage="linkage" :selectCondition="selectCondition" :params="params" :reqFnc="getRoomList" :isMultiple="true" :isCurrentSelect="true" @change="selectChange" />
      </kade-route-card>
      <kade-route-card>
        <template #header>
          <span class="header-title">密码</span>
        </template>
        <el-form inline label-width="150px" size="mini" :model="state.form" :rules="rules" ref="formRef">
          <el-form-item label="密码" prop="pwd">
            <el-input placeholder="请输入" v-model="state.form.pwd" show-password></el-input>
          </el-form-item>
        </el-form>
      </kade-route-card>
    </div>
    <template #footer v-if="editType!=='details'">
      <span class="dialog-footer">
        <el-button type="primary" @click="submit" size="mini" :loading="state.loading">保存</el-button>
        <el-button @click="handleClose" size="mini">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElMessage } from "element-plus"
import { reactive, watch, nextTick, ref } from 'vue'
import { getRoomList, batchRoomPwdAdd, batchRoomPwdDel } from "@/applications/eccard-dorm/api";
import selectTable from '@/components/table/selectTable'



const column = [
  { label: '所属区域', prop: 'areaName' },
  { label: '房间类型', prop: 'roomTypeName' },
  {
    label: '房间', prop: 'roomName', isRow: true, render: (val) => {
      return `${val.buildName}>${val.unitNum}单元>${val.floorNum}楼>${val.roomName}`
    }
  },
]
const linkage = {
  linkageData: {
    area: { label: '区域', valueKey: 'areaId', key: 'id' },
    building: { label: '楼栋', valueKey: 'buildId' },
    unit: { label: '单元', valueKey: 'unitNum' },
    floor: { label: '楼层', valueKey: 'floorNum' },
  }
}


const params = {
  currentPageKey: "currentPage",
  pageSizeKey: "pageSize",
  resListKey: "list",
  resTotalKey: "total",
  value: {
  },
  tagNameKey: "roomName",
  valueKey: "id",
}

export default {
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: String
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    "kade-select-table": selectTable
  },
  setup(props, context) {
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      roomList: [],
      form: {
        pwd: ""
      }
    });
    watch(() => props.modelValue, val => {
      if (val) {
        state.form = { pwd: "" }
        state.roomList = []
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })

    const rules = {
      pwd: [
        {
          required: true,
          message: "请输入密码",
        },
        {
          pattern: /^\d{6,10}$/,
          message: '密码为6-10为数字',
          trigger: 'blur'
        }
      ],
    }
    const selectChange = val => {
      state.roomList = val.list

    }
    const submit = () => {
      formRef.value.validate(async valid => {
        if (valid) {
          state.loading = true
          let params = {
            ...state.form,
            roomIdList: state.roomList.map(item => item.id)
          }
          let fn = props.type == 'add' ? batchRoomPwdAdd : batchRoomPwdDel
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("update:modelValue", false)
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }
        } else {
          return false
        }
      })
    }
    const handleClose = () => {
      context.emit("update:modelValue", false)
    };
    return {
      getRoomList,
      linkage,
      column,
      params,
      formRef,
      state,
      rules,
      selectChange,
      submit,
      handleClose,
    }
  }
}
</script>

<style lang="scss" scoped>
</style>