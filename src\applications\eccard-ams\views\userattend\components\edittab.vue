<template>
  <el-dialog append-to-body="true" :model-value="modelValue" title="人员信息详情" width="1200px" :before-close="update" :close-on-click-modal="false" :destroy-on-close="true">
    <el-tabs :model-value="'info'">
      <el-tab-pane label="个人资料" name="info" >
        <user-edit :data="state.model" :type="1"  />
      </el-tab-pane>
      <el-tab-pane label="考勤记录" name="record">
        <attend-info ref="attendInfo" :data="state.model"/>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
import { reactive, ref, computed, watch , onMounted } from 'vue';
// import Modal from '@/components/modal';
import attendInfo from "@/applications/eccard-ams/views/userattend/components/attendinfo.vue"
import userEdit from "@/applications/eccard-basic-data/views/personInfo/components/details/info.vue"

// const getDefaultModel = () => ({
//   roleName: "",
//   status: "ENABLE_TRUE",
// });
import {
  ElTabPane,
  ElTabs,
  ElDialog,
} from "element-plus";
export default {
  emits: ["update:modelValue", "change"],
  props: {
    title: {
      type: String,
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({}),
    },
    data:{
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    userEdit,
    attendInfo,
    ElTabPane,
    ElTabs,
    ElDialog,
  },
  setup(props, context) {
    const formRef = ref(null);
    const attendInfo = ref();
    const state = reactive({
      model: props.data,
    });
    const attrs = computed(() => {
      // eslint-disable-next-line no-unused-vars
      const { modelValue, ...attrs } = props;
      return attrs;
    });
    const update = (v) => {
      context.emit('update:modelValue', v);
      attendInfo.value.close();
    };
    onMounted(()=>{
    })
    watch(
      () => props.data,
      (n) => {
        if (n) {
          state.model = props.data;
        }else{
          state.model = {};
        }
      }
    );
    return {
      attrs,
      update,
      formRef,
      attendInfo,
      labelWidth: THEMEVARS.formLabelWidth,
      state,
      themes: THEMEVARS,
    };
  },
};
</script>
