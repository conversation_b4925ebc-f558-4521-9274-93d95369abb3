<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true" width="1000px">
    <el-row :gutter="15">
      <el-col :sm="11" style="position:relative">
        <div style="border: 1px ridge #ccc;position: absolute;top:30px;left:10px;z-index:99999;width:380px;height:300px;background-color:white" v-if="state.showConditon">
          <span @click="state.showConditon=false;" style="position: absolute;top:10px;right:10px;">X</span>
          <el-form label-width="120px" size="mini" style="width: 280px;text-align: center;z-index:99999;margin-top:20px;">
            <el-form-item label="部门">
              <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                     @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
            </el-form-item>
            <el-form-item label="身份类别">
              <el-select v-model="state.form.userIde" :clearable="true">
                <el-option v-for="(item,index) in state.ideTypeList" :key="index" :label="item.roleName" :value="item.roleName"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否住校">
              <el-select v-model="state.form.userLiveSchool" :clearable="true">
                <el-option v-for="(item,index) in isSchoolList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="人员状态">
              <el-select v-model="state.form.userPostStatus" :clearable="true">
                <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关键字">
              <el-input v-model="state.form.keyWord" placeholder="请输入关键字搜索" :clearable="true"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">搜索</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div style="height:35px;">
          待选择人员（{{state.dataList.length}}）：<el-button @click="state.showConditon=!state.showConditon;" size="small" type="primary">搜索</el-button>
        </div>
        <el-table style="width: 100%" size="mini" :data="state.dataList" ref="multipleTable" @selection-change="handleFristSelectionChange" v-loading="state.loading" height="45vh" border stripe>
          <el-table-column type="selection" width="55" />
          <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
          <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
          <el-table-column label="部门" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="人员状态" prop="userPostStatus" align="center" show-overflow-tooltip>
            <template #default="scope">
              <el-tag size="small" :type=" scope.row.userPostStatus === 'STATE_IN' ? 'primary' : 'warning' ">{{
                dictionaryFilter(scope.row.userPostStatus) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否住校" prop="userLiveSchool" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            :current-page="state.form.pageNum"
            :page-size="state.form.pageSize"
            layout="total, sizes, prev, next, jumper"
            :page-sizes="[6, 10, 20, 50, 100]"
            :total="state.total"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          >
          </el-pagination>

        </div>
      </el-col>
      <el-col :sm="2" style="text-align: center;margin-top:200px">
        <el-button @click="handleSelectionChangeAdd" size="small" type="primary">&gt;&gt;</el-button>
        <br><br><br>
        <el-button @click="handleSelectionChangeRemove" size="small" type="primary">&lt;&lt;</el-button>
      </el-col>
      <el-col :sm="11">
        <div style="height:35px;">
          已选择人员（{{state.selectData.length}}）：
        </div>
        <el-table style="width: 100%" size="mini" :data="state.selectData" ref="multipleTable"  @selection-change="handleSecondSelectionChange" v-loading="state.loading" height="45vh" border stripe>
          <el-table-column type="selection" width="55" />
          <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
          <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
          <el-table-column label="部门" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="人员状态" prop="userPostStatus" align="center" show-overflow-tooltip>
            <template #default="scope">
              <el-tag size="small" :type=" scope.row.userPostStatus === 'STATE_IN' ? 'primary' : 'warning' ">{{
                dictionaryFilter(scope.row.userPostStatus) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否住校" prop="userLiveSchool" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
      </el-col>
    </el-row>


      <!--<el-transfer-->
        <!--v-model="state.selectData" style="text-align:initial;display: inline-block;"-->
        <!--filterable-->
        <!--:titles="['选择考勤人员', '已选考勤人员']"-->
        <!--:format="{-->
          <!--noChecked: '${total}',-->
          <!--hasChecked: '${checked}/${total}',-->
        <!--}"-->
        <!--:data="state.dataList"-->
      <!--&gt;-->
      <!--</el-transfer>-->

    <template #footer>
      <p style="text-align: center">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>

</template>
<script>
  import { reactive , onMounted  ,watch} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { postAttentionUserAttendPage , postSelectAttentionUserAttendPage , getIdeTypeList } from "@/applications/eccard-ams/api";
  import deptSelectTree from "@/components/tree/deptSelectTree.vue";
  // import userEdit from "@/applications/eccard-ams/views/userattend/components/edit.vue"
  import {
    ElRow,
    ElCol,
    ElTable,
    ElTableColumn,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    // ElPagination,
    ElMessage,
    // ElMessageBox,
    ElDialog,
    ElPagination
  } from 'element-plus';

  export default {
    components: {
      ElRow,
      ElCol,
      ElDialog,
      // ElTransfer,
      ElSelect,
      ElOption,
      ElInput,
      ElForm,
      ElFormItem,
      ElButton,
      ElTable,
      ElTableColumn,
      "kade-dept-select-tree":deptSelectTree,
      ElPagination,
    },
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      type:{
        type: String,
        default: "",
      },
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    setup(props, context) {
      const statusList = useDict("BASE_USER_STATE");
      // const ideTypeList = useDict("ATTENTION_USER_IDE");
      const isSchoolList = useDict("SYS_BOOL_STRING");
      // const ideTypeList = useDict("ATTENTION_USER_IDE");
      // let {data1} = await getAttentionAttendDevGroupAll('');
      // const atgIdList = data1;
      const state = reactive({
        loading: false,
        isEdit:false,
        isFresh:'0',
        isEdit1:false,
        form:{
          userDisType:2,
          orgNameList:null,
          userIde:null,
          userLiveSchool:null,
          userPostStatus:null,
          atgId:null,
          orgId:null,
          keyWord:null,
          currentPage:1,
          pageSize:100,
          userIdSelectedList:null,
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        showConditon:false,
        selectData:[],
        ideTypeList:[],
        firstSelectedData:[],
        secondSelectedData:[],
        selectIndexList:[],
      });

      //分页
      const getList=async (flag)=>{
        state.loading=true;
        // alert(flag)
        if(flag) {
          state.form.userIdSelectedList = props.data.map((item) => {
            return item.userId;
          });
        }else{
          state.form.userIdSelectedList = state.selectData.map((item) => {
            return item.userId;
          });
        }
        if(state.form.userIdSelectedList.length == 0){
          state.form.userIdSelectedList = null;
        }
        let {data}=await postAttentionUserAttendPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false;

        //右侧选中元素
        /*
        key: number
        label: string
        disabled: boolean
         */
        state.dataList.forEach((item , index)=>{
          item['index'] = index;
          item['key'] = item.userId;
          item['label'] = item.userNum + '--' + item.userName;
          item['disabled'] = false;
        });
        if(flag) {
          if(!state.form.userIdSelectedList){
            state.selectData = [];
          }else {
            let {data} = await postSelectAttentionUserAttendPage(state.form)
            state.selectData = data.list;
            // alert(state.selectData.length);
          }

          // let rt = [];
          // state.dataList.forEach((item)=>{
          //   if(props.data.filter((sitem)=>{return sitem.userId == item.userId}).length == 0){
          //     rt.push(item);
          //   }
          // });
          // state.dataList = rt;

        }
      }
      const reset=()=>{
        state.form={
          userDisType:2,
          orgNameList:null,
          userIde:null,
          userLiveSchool:null,
          userPostStatus:null,
          atgId:null,
          orgId:null,
          keyWord:null,
          currentPage:1,
          pageSize:100,
          userIdSelectedList:null,
        }
      }
      const search=()=>{
        getList();
        state.showConditon=false;
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleFristSelectionChange = (val)=>{
        state.firstSelectedData = val;
      }
      const handleSecondSelectionChange = (val)=>{
        state.secondSelectedData = val;
      }
      const handleSelectionChangeAdd = ()=>{
        //state.selectData = [];
        state.firstSelectedData.forEach((item)=>{
          if(state.selectData.filter((sitem)=>{return sitem.userId == item.userId}).length == 0){
            state.selectData.push(item);
          }
        });
        // let rt = [];
        // state.dataList.forEach((item)=>{
        //   if(state.firstSelectedData.filter((sitem)=>{return sitem.userId == item.userId}).length == 0){
        //     rt.push(item);
        //   }
        // });
        // //state.dataList = rt;
        getList();
        state.firstSelectedData = [];
      }
      const handleSelectionChangeRemove = ()=>{
        // state.secondSelectedData.forEach((item)=>{
        //   if(state.dataList.filter((sitem)=>{return sitem.userId == item.userId}).length == 0){
        //     state.dataList.push(item);
        //   }
        // });
        let rt = [];
        state.selectData.forEach((item)=>{
          if(state.secondSelectedData.filter((sitem)=>{return sitem.userId == item.userId}).length == 0){
            rt.push(item);
          }
        });
        state.selectData = rt;
        state.secondSelectedData = [];
        getList();
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const cancel = () => {
        context.emit("update:modelValue", '');
      };
      const submit = () => {
        if(state.selectData.length == 0){
          ElMessage.warning('请选择至少一个考勤人员！');return;
        }
        let selectedUsers = new Array();
        state.selectData.forEach((item)=>{
          if(selectedUsers.filter((titem)=>{return titem.userId == item.userId}).length == 0) {
            selectedUsers.push({userId: item.userId, userNum: item.userNum, userName: item.userName})
          }
        });
        // alert(JSON.stringify(selectedUsers))
        if(state.selectData){
          context.emit("update:modelValue", selectedUsers);
        }
      };

      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        //getList(1)
      })

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            state.form.pageNum = 1;
            reset();
            let objs = await getIdeTypeList();
            state.ideTypeList = objs.data;

            getList(1);
          }
        }
      );

      return {
        state,
        // statusList,
        // ideTypeList,
        // add,
        // edit,
        statusList,
        // ideTypeList,
        isSchoolList,
        reset,
        search,
        handleSelectionChangeAdd,
        handleFristSelectionChange,
        handleSecondSelectionChange,
        handleSelectionChangeRemove,
        handlePageChange,
        handleSizeChange,
        cancel,
        close,
        submit,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
  :deep(.el-transfer){
    height:490px;
  }
  :deep(.el-transfer-panel){
    height:490px;
    width: 300px;
  }
  :deep(.el-transfer-panel__body){
    height:380px;
  }
  :deep(.el-transfer-panel__list){
    height:380px;
  }
</style>
