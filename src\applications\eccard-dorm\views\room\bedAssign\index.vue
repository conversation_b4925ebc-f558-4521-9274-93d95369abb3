<template>
  <div class="padding-box" style="overflow-x: auto;">
    <kade-table-wrap>
      <template #extra>
        <el-button icon="el-icon-plus" @click="state.isExport = true" size="small" class="btn-blue">导入分配</el-button>
        <el-button icon="el-icon-daorutupian" @click="handleHandAssign" size="small" class="btn-green">
          手动分配</el-button>
        <el-button icon="el-icon-daorutupian" @click="handleAutoAssign()" size="small" class="btn-blue">自动分配</el-button>
      </template>
      <el-divider></el-divider>
      <div class="bed-assign-box">
        <div class="area">
          <el-tree class="area-tree" ref="treeRef" :data="state.areaCheckTreeList" :props="defaultProps" node-key="id" @node-click="handleNodeClick">
            <template #default="{ node }">
              <div class="custom-tree-node hasbtn">
                <span class="left">
                  <i class="el-icon-map-location"></i>
                  <span style="margin-left: 5px; color: #333">{{ node.label }}</span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
        <div class="room-box" v-if="state.roomList.length">
          <el-checkbox v-model="state.checkedAll" label="全选" size="large" @change="checkedAllChange" style="margin-left:11px;margin-bottom:10px">
          </el-checkbox>
          <div class="room-item" v-for="(item, index) in state.roomList" :key="index">
            <div class="item-header" @click="handleRoomBed(item, index)">
              <div class="item-left">
                <el-checkbox v-model="item.isSelect" :label="item.roomName" size="large" @change="checkedChange" :disabled="item.deptName == '未分配'"></el-checkbox>
                <div style="margin-left:50px">床位：{{ item.bedCount - item.surplusBedCount }}/{{ item.bedCount }}</div>
                <div style="margin-left:50px">{{ item.deptName }}</div>
              </div>
              <div class="el-icon-arrow-down" :class="item.isShow ? 'icon-down-show' : 'icon-down'"></div>
            </div>
            <div class="item-body" v-show="item.isShow" v-loading="state.bedLoading">
              <div class="body-item" v-for="(v, i) in item.bedList" :key="i">
                <div class="item-msg" :style="{ backgroundColor: v.isPerson ? '#3399ff19' : '#aaaaaa19' }">
                  <div :style="{ color: v.isPerson ? '#3399FF' : '#AAAAAA' }">{{ i + 1 }}号床位</div>
                  <img :src="v.isPerson ? bedActiveIcon : bedIcon" alt="">
                  <div class="userName" v-if="v.isPerson">{{ v.userName }}</div>
                  <div class="userName" v-if="v.isPerson">{{ v.userCode }}</div>
                  <el-tooltip v-if="v.isPerson" effect="dark" :content="v.deptName" placement="top-start">
                    <div class="deptName">{{ v.deptName }}</div>
                  </el-tooltip>
                  <div class="no-assign" v-else>未分配</div>
                </div>
                <div class="flex-box">
                  <div :class="v.isPerson ? 'btn-yellow' : 'btn-green'" class="assign-person-btn" @click="allocatePersonClick(v)">{{ v.isPerson ? '调配人员' : '分配人员' }}</div>
                  <div class="assign-person-btn btn-pink" v-show="!v.isPerson" @click="handleDeleteBed(v)">减少床位</div>

                  <!-- <el-button :class="v.isPerson ? 'btn-yellow' : 'btn-green'" size="mini" @click="allocatePersonClick(v)">
                    {{ v.isPerson ? '调配人员' : '分配人员' }}</el-button> -->
                  <!-- <el-button size="mini" @click="handleDeleteBed(v)" type="danger" icon="el-icon-delete-solid"></el-button> -->
                </div>
              </div>
              <div class="body-item" @click="handleAddBed(item)">
                <div class="item-msg" :style="{ backgroundColor: '#aaaaaa19' }">
                  <i class="el-icon-plus bed-plus-icon"></i>
                  <div class="plus-bed">添加床位</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-empty style="width:100%" v-else description="暂无数据"></el-empty>
      </div>
    </kade-table-wrap>
    <kade-allocate-person :isShow="state.isAllocatePerson" :data="state.rowBedData" @close="closeAllocatePerson" />
    <kade-room-export v-model="state.isExport" @update:modelValue="state.isExport = false" />
    <kade-manual-allocate-pweson :isShow="state.isManualAllocatePweson" :roomList="state.roomList" :data="state.form" @close="closeManualAllocatePweson" />
  </div>
</template>
<script>
import { ElButton, ElDivider, ElCheckbox, ElTree, ElEmpty, ElMessageBox, ElMessage, ElTooltip } from "element-plus";
import { onMounted, reactive, ref } from '@vue/runtime-core';
import { makeTree } from "@/utils/index.js";
import { getUserAreaPurview } from "@/applications/eccard-basic-data/api";
import { queryBuildingList, getBuildRoomInfo, getRoomBedAssignData, addRoomBed, removeRoomBed, autoAllocateUserBed } from "@/applications/eccard-dorm/api";

import allocatePerson from "./components/allocatePerson"
import roomExport from "./components/export"
import manualAllocatePweson from "./components/manualAllocatePweson"
import bedIcon from "@/assets/bed.png";
import bedActiveIcon from "@/assets/bed-active.png";
const defaultProps = {
  children: "children",
  label: "areaName",
};
export default {
  components: {
    ElButton,
    ElDivider,
    ElCheckbox,
    ElTree,
    ElEmpty,
    ElTooltip,
    "kade-allocate-person": allocatePerson,
    "kade-room-export": roomExport,
    "kade-manual-allocate-pweson": manualAllocatePweson
  },
  setup() {
    const treeRef = ref(null)
    const state = reactive({
      loading: false,
      bedLoading: false,
      isExport: false,
      isAllocatePerson: false,
      isManualAllocatePweson: false,
      checkedAll: false,
      areaCheckTreeList: [],
      form: {},
      roomList: [],
      checkRoomList: [],
      rowBedData: "",
      rowIndex: 0,
    })
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = res.data.map(item => {
          return {
            ...item,
            isArea: true
          }
        })
        let arrTree = makeTree(arr, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arrTree];
      });
    };
    const handleNodeClick = (val) => {
      console.log(val)
      state.form.areaId = val.id
      state.loading = true
      state.roomList = []
      if (val.isArea) {
        queryBuildingList({ areaId: val.id }).then((res) => {
          //区域拼接楼栋单元楼层
          let arr = res.data.map((item, index) => {
            let childArr1 = []
            for (let i = 0; i < item.unitCount; i++) {
              let childArr2 = []
              for (let j = 0; j < item.floorCount; j++) {
                childArr2.push({
                  ...item,
                  areaParentId: (i + 1) + new Date().getTime(),
                  id: (j + 2) + new Date().getTime(),
                  isArea: false,
                  floorNum: j + 1,
                  unitNum: i + 1,
                  areaName: j + 1 + '楼',
                })
              }
              childArr1.push({
                ...item,
                isArea: false,
                id: (i + 1) + new Date().getTime(),
                areaParentId: (index + 1) + new Date().getTime(),
                areaName: i + 1 + '单元',
                unitNum: i + 1,
                children: childArr2
              })
            }
            let obj = {
              ...item,
              isArea: false,
              areaName: item.buildName,
              id: (index + 1) + new Date().getTime(),
              areaParentId: val.id,
            }
            if (childArr1.length > 0) {
              obj.children = childArr1
            }
            return obj
          })
          if (val.children) {
            arr.push(...val.children.filter(item => item.isArea))
          }

          console.log(arr);
          val.children = arr
          // treeRef.value.updateKeyChildren(val.id, arr);
        });
      } else {
        //请求房间列表
        state.form.buildId = val.buildId
        if (val.unitNum) {
          state.form.unitNum = val.unitNum
        } else {
          delete state.form.unitNum
        }
        if (val.floorNum) {
          state.form.floorNum = val.floorNum
        } else {
          delete state.form.floorNum
        }
        getRoomList()
      }
    }
    //获取房间列表
    const getRoomList = () => {
      state.loading = true
      getBuildRoomInfo(state.form).then(({ data }) => {
        state.roomList = data.map(item => {
          return {
            ...item,
            isShow: false,
            roomList: [],
            isSelect: false
          }
        })
        state.loading = false
      }).catch(() => {
        state.loading = false
      });
    }

    const getRoomPersonList = (row) => {
      state.bedLoading = true
      getRoomBedAssignData({ roomId: row.roomId }).then(({ data: { roomStayDtoList, surplusBedCount, bedCount } }) => {
        roomStayDtoList = roomStayDtoList.map(item => {
          return {
            ...item,
            roomId: row.roomId,
            bedCount: row.bedCount,
            isPerson: true
          }
        })
        let bedList = []
        for (let i = 0; i < row.bedCount; i++) {
          bedList.push({
            isPerson: false,
            bedNum: i + 1,
            roomId: row.roomId,
            bedCount: row.bedCount,
          })
        }
        roomStayDtoList.forEach(item => {
          bedList[item.bedNum - 1] = item
        })
        state.roomList = state.roomList.map(item => {
          if (item.roomId == row.roomId) {
            return {
              ...item,
              bedCount,
              surplusBedCount,
              bedList,
              // isShow: true,
            }
          } else {
            return {
              ...item,
              // isShow: false
            }
          }
        })
        state.bedLoading = false
      }).catch(() => {
        state.bedLoading = false
      });
    }
    const handleRoomBed = async (row) => {

      if (!row.isShow) {
        await getRoomPersonList(row)
      }
      row.isShow = !row.isShow
    }
    const checkedAllChange = (val) => {
      if (val) {
        state.roomList = state.roomList.map(item => {
          if (item.deptName == '未分配') {
            return {
              ...item,
              isSelect: false
            }
          } else {
            return {
              ...item,
              isSelect: true
            }
          }

        })
      } else {
        state.roomList = state.roomList.map(item => {
          return {
            ...item,
            isSelect: false
          }
        })
      }
      state.checkRoomList = state.roomList.filter(item => item.isSelect)
    }
    const checkedChange = () => {

      state.checkRoomList = state.roomList.filter(item => item.isSelect)
      let arr = state.roomList.filter(item => item.deptName !== '未分配')
      console.log(arr, state.checkRoomList);
      if (state.checkRoomList.length == arr.length) {
        state.checkedAll = true
      }
      else {
        state.checkedAll = false
      }
    }
    const handleHandAssign = () => {
      /* if (!state.checkRoomList.length) {
        return ElMessage.error('请先选择需要手动分配的房间！')
      } */
      state.isManualAllocatePweson = true
    }
    const handleAutoAssign = async () => {
      if (!state.checkRoomList.length) {
        return ElMessage.error('请先选择需要自动分配的房间！')
      }
      ElMessageBox.confirm(`确定对${state.checkRoomList.map(item => item.roomName).join(",")}进行自动分配吗？`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        state.loading = true
        try {
          let { code, message } = await autoAllocateUserBed({ roomIds: state.checkRoomList.map(item => item.roomId) })
          if (code === 0) {
            ElMessage.success(message)
            state.loading = false
            state.checkRoomList = []
            getRoomList()
          }
        }
        catch {
          state.loading = false
        }
      });
    }
    const handleDeleteBed = (bed) => {
      ElMessageBox.confirm(`确定删除当前床位吗？`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        console.log(bed);
        let { message, code } = await removeRoomBed(bed.roomId, bed.bedNum)
        if (code === 0) {
          ElMessage.success(message);
          getRoomList()
        }
      });
    }
    const allocatePersonClick = (row) => {
      console.log(row)
      state.rowBedData = row
      state.isAllocatePerson = true
    }
    const closeAllocatePerson = val => {
      if (val) {
        getRoomList()
      }
      state.isAllocatePerson = false
    }
    const closeManualAllocatePweson = val => {
      if (val) {
        getRoomList()
      }
      state.isManualAllocatePweson = false
    }
    const handleAddBed = item => {
      ElMessageBox.confirm(`确定要给${item.roomName}房间添加1个床位吗？`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { code, message } = await addRoomBed(item.roomId);
        if (code === 0) {
          ElMessage.success(message);
          getRoomList()
        }
      });
    }

    onMounted(() => {
      queryAreaCheckList()
    })
    return {
      bedIcon,
      bedActiveIcon,
      defaultProps,
      treeRef,
      state,
      handleNodeClick,
      getRoomPersonList,
      handleRoomBed,
      checkedAllChange,
      checkedChange,
      handleHandAssign,
      allocatePersonClick,
      handleDeleteBed,
      handleAutoAssign,
      closeAllocatePerson,
      closeManualAllocatePweson,
      handleAddBed
    };
  },
};
</script>
<style lang="scss" scoped>
.kade-table-wrap {
  padding-bottom: 0px;
  min-width: 1170px;
}

.form-box {
  padding-top: 20px;
  border-bottom: 1px solid #eeeeee;
}

.bed-assign-box {
  display: flex;
  height: 75vh;

  .area {
    width: 240px;
    height: 100%;
    border-right: 1px solid #eeeeee;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .room-box {
    box-sizing: border-box;
    flex: 1;
    height: 100%;
    padding: 10px 20px;
    overflow-y: scroll;

    .room-item {
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 10px;
        background: #f2f2f2;
        border: 1px solid #d7d7d7;
        margin-bottom: 10px;

        .item-left {
          display: flex;
          align-items: center;
        }
      }

      .item-body {
        display: flex;
        flex-wrap: wrap;
        transition: all 2s linear;

        .body-item {
          box-sizing: border-box;
          text-align: center;
          margin-right: 20px;
          margin-bottom: 10px;
          width: 150px;
          // height: 10px;

          .item-msg {
            text-align: center;
            padding: 5px 20px;
            background: #3399ff19;
            border-radius: 20px;
            margin-bottom: 10px;
            height: 130px;
            font-size: 12px;
            img {
              width: 64px;
              height: 36px;
              margin: 10px 0;
            }
          }

          .userName {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 5px;
          }

          .deptName {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .no-assign {
            line-height: 36px;
            color: #7f7f7f;
          }

          .bed-plus-icon {
            font: 50px arial;
            color: #7f7f7f;
            margin-top: 20px;
          }

          .plus-bed {
            color: #7f7f7f;
            line-height: 36px;
          }
          .flex-box {
            display: flex;
            justify-content: space-between;
            .assign-person-btn {
              flex: 1;
              border-radius: 5px;
              padding: 10px;
              font-size: 12px;
              &:last-child {
                margin-left: 10px;
              }
            }
          }
        }
      }
    }
  }
}

.icon-down {
  transform: rotate(180deg);
  transition: all 0.3s linear;
}

.icon-down-show {
  transform: rotate(0deg);
  transition: all 0.3s linear;
}

.el-divider--horizontal {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-dialog__body) {
  padding-bottom: 20px !important;
}
</style>