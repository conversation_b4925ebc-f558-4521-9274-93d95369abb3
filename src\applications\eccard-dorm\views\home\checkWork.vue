<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">今日考勤结果</span>
    </template>
    <div id="checkWorkCharts" class="chartline" v-loading="state.loading"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted, nextTick, reactive } from "vue";
import { getDormAttendanceResultStatistics } from "@/applications/eccard-dorm/api"

export default {
  setup() {
    const state = reactive({
      loading: false,
      statisticsData: {}
    })


    const echartInit = async () => {
      var chartDom = document.getElementById('checkWorkCharts');
      var myChart = echarts.init(chartDom);
      var option;
      state.loading = true
      try {
        let { data } = await getDormAttendanceResultStatistics()
        if (data) {
          state.statisticsData = data
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
      option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '0%',
          top: '30%'
        },

        series: [
          {
            name: '考勤结果',
            type: 'pie',
            radius: '80%',
            right: "40%",
            label: {
              position: "inside",
              formatter: '{b}: {c}'
            },
            data: [
              { value: state.statisticsData.normalReturn ? state.statisticsData.normalReturn : 0, name: '正常' },
              { value: state.statisticsData.attendanceLeave ? state.statisticsData.attendanceLeave : 0, name: '请假' },
              { value: state.statisticsData.lateReturn ? state.statisticsData.lateReturn : 0, name: '晚归' },
              { value: state.statisticsData.notReturn ? state.statisticsData.notReturn : 0, name: '未归' },
              { value: state.statisticsData.notLeave ? state.statisticsData.notLeave : 0, name: '未离' },
              { value: state.statisticsData.lateLeave ? state.statisticsData.lateLeave : 0, name: '晚离' },
            ],
            emphasis: {
              scale: false,

              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      nextTick(() => {
        myChart.resize();
        myChart.setOption(option, true);
      });
    };

    onMounted(async () => {
      echartInit();
    });
    return {
      state,
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 260px;
  width: 100%;
}
</style>