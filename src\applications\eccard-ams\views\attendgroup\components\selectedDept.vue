<template>
  <el-dialog :modelValue="modelValue" title="选择组织机构" :before-close="cancel" :append-to-body="true" width="500px">
    <div class="padding-box content">
      <el-tree ref="treeRef" :data="state.departCheckTreeList" :props="defaultProps" :check-strictly="true" lazy
        :load="handleLoad" show-checkbox @check-change="handleCheckChange">
      </el-tree>
    </div>
    <template #footer>
      <p style="text-align: center">
        <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
        <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small"
          type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, onMounted, watch } from "vue";
import {
  ElButton,
  ElMessage,
  ElDialog,
  ElTree
} from 'element-plus';
// import { makeTree } from "@/utils/index.js";
import { getUserOrgPurview } from '@/applications/eccard-basic-data/api';
const defaultProps = {
  children: "children",
  label: "deptName",
};
export default {
  components: {
    ElDialog,
    ElButton,
    ElTree
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      departCheckTreeList: [],
      deptList: [],
      deptSelectList:[],
    });

    watch(
      () => props.modelValue,
      async (n) => {
        if (n) {
          state.deptSelectList=[]
          queryDepartCheckList()
        }
      }
    );
    const queryDepartCheckList = () => {
      getUserOrgPurview().then((res) => {
        // let arr = makeTree(res.data, "id", "deptParentId", "children");
        state.deptList = res.data
        state.departCheckTreeList = res.data.filter(item => item.deptParentId === 0)
      });
    };
    const handleLoad = (node, resolve) => {
      let arr = state.deptList.filter(item => item.deptParentId == node.data.id)
      resolve(arr)
    }
    const handleCheckChange = (val, isSelect) => {
      if (isSelect) {
        state.deptSelectList.push({ deptName: val.deptName, id: val.id })
      } else {
        state.deptSelectList = state.deptSelectList.filter(item => item.id != val.id)
      }
    }
    const cancel = () => {
      context.emit("update:modelValue", '');
    };
    const submit = () => {
      if (state.deptSelectList.length) {
        context.emit("update:modelValue", state.deptSelectList);
      } else {
        ElMessage.warning("请选择组织机构！");
      }
    };

    onMounted(() => {
    })



    return {
      defaultProps,
      state,
      handleLoad,
      handleCheckChange,
      cancel,
      submit,
    };
  },
};
</script>
<style lang="scss" scoped>
.content {
  height: 500px;
  overflow-y: auto;
}


:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #efefef;
}

:deep(.el-overlay) {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
