<template>
  <el-dialog :model-value="modelValue" title="房间分配" width="1451px" :before-close="beforeClose"
    :close-on-click-modal="false">
    <div class="box" v-loading="state.loading">
      <div class="filter-msg">
        <el-form size="mini" inline label-width="80px">
          <el-form-item label="组织机构">
            <kade-dept-select-tree style="width: 100%" :value="state.form.deptId" valueKey="id" :multiple="false"
              @valueChange="deptChange" />
          </el-form-item>
        </el-form>
        <div class="msg">
          {{ state.form.deptName }}房间可分配（男生{{ state.assignData.maleDorm.totalPersonCount }}人，女生{{
            state.assignData.femaleDorm.totalPersonCount
          }}人，共{{ state.assignData.maleDorm.totalPersonCount +
  state.assignData.femaleDorm.totalPersonCount
}}人）
        </div>
      </div>
      <div class="assign-box">
        <div class="assign-left-box">
          <div class="top-box">
            <div class="box-type">
              <div class="box-title">已分配男生宿舍：{{ state.assignData.maleDorm.totalRoomCount }}个房间，已入住{{
                state.assignData.maleDorm.checkPersonCount
              }}人</div>
              <el-button size="mini" type="primary" :disabled="isBoyCancelDisabled" @click="handleCancel('boy')">撤销
              </el-button>
            </div>
            <div class="assign-room-box">
              <kade-room @click="selectItem(item, 'boy')" v-for="(item, index) in state.assignData.maleDorm.roomList"
                :key="index" :data="item" />
            </div>
          </div>
          <div class="bottom-box">
            <div class="box-type">
              <div class="box-title">已分配女生宿舍：{{ state.assignData.femaleDorm.totalRoomCount }}个房间，已入住{{
                state.assignData.femaleDorm.checkPersonCount
              }}人</div>
              <el-button size="mini" type="primary" :disabled="isGirlCancelDisabled" @click="handleCancel('girl')">撤销
              </el-button>
            </div>
            <div class="assign-room-box">
              <kade-room @click="selectItem(item, 'girl')" v-for="(item, index) in state.assignData.femaleDorm.roomList"
                :key="index" :data="item" />
            </div>
          </div>
          <div class="bottom-box">
            <div class="box-type">
              <div class="box-title">已分配教师宿舍：{{ state.assignData.teacherDorm.totalRoomCount }}个房间，已入住{{
                state.assignData.teacherDorm.checkPersonCount
              }}人</div>
              <el-button size="mini" type="primary" :disabled="isTeacherCancelDisabled" @click="handleCancel('teacher')">撤销
              </el-button>
            </div>
            <div class="assign-room-box">
              <kade-room @click="selectItem(item, 'girl')" v-for="(item, index) in state.assignData.teacherDorm.roomList"
                :key="index" :data="item" />
            </div>
          </div>
        </div>
        <div class="assign-right-box">
          <div class="box-type">
            <div class="box-title">
              楼栋：<el-select v-model="state.buildId" size="mini" clearable @change="buildChange">
                <el-option v-for="(item, index) in state.buildingList" :key="index" :label="item.buildName"
                  :value="item.buildId">
                </el-option>
              </el-select> 未分配宿舍：{{ state.noAssignData.totalRoomCount }}个房间，已入住{{ state.noAssignData.checkPersonCount
              }}人
            </div>

            <el-button size="mini" type="primary" :disabled="isAssignDisabled" @click="handleAssignRoom">分配</el-button>
          </div>
          <div class="assign-room-box">
            <kade-room @click="selectItem(item, 'all')" v-for="(item, index) in state.noAssignData.roomList" :key="index"
              :data="item" />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { ElDialog, ElForm, ElFormItem, ElSelect, ElOption, ElButton, ElMessage } from "element-plus";
import { onMounted, reactive, watch, computed } from 'vue';
import { roomtDeptAllocationRoomInfo, queryBuildingList, getNotAllocatedRoom, allocDeptRoom, revokeDeptAllocatedRoom } from "@/applications/eccard-dorm/api";

import deptSelectTree from "@/components/tree/deptSelectTree";
import room from "@/applications/eccard-dorm/components/room.vue"
export default {
  emits: ["update:modelValue"],
  components: {
    ElDialog, ElForm, ElFormItem, ElButton, ElSelect, ElOption,
    "kade-dept-select-tree": deptSelectTree,
    "kade-room": room
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false,
      form: {},
      buildId: "",
      assignData: {
        maleDorm: {
          roomList: [],
        },
        femaleDorm: {
          roomList: [],
        },
        teacherDorm: {
          roomList: [],
        }
      },
      noAssignData: {
        roomList: []
      },
      buildingList: []
    })

    const isAssignDisabled = computed(() => {
      let arr = state.noAssignData.roomList.filter(item => item.isSelect)
      return arr.length ? false : true
    })
    const isBoyCancelDisabled = computed(() => {
      let arr = state.assignData.maleDorm.roomList.filter(item => item.isSelect)
      return arr.length ? false : true
    })
    const isGirlCancelDisabled = computed(() => {
      let arr = state.assignData.femaleDorm.roomList.filter(item => item.isSelect)
      return arr.length ? false : true
    })
    const isTeacherCancelDisabled = computed(() => {
      let arr = state.assignData.teacherDorm.roomList.filter(item => item.isSelect)
      return arr.length ? false : true
    })
    watch(() => props.modelValue, (val) => {
      if (val) {
        state.form = { ...props.data };
        Promise.all([
          getAssignList(),
          getNoAssignList()
        ]).then(() => {
          state.loading = false;
        }).catch(() => {
          state.loading = false;
        })
      }
    })
    const getBuildingList = async () => {
      let { data } = await queryBuildingList()
      state.buildingList = data
    }
    const getAssignList = async () => {
      try {
        state.loading = true
        let { data } = await roomtDeptAllocationRoomInfo({ deptId: state.form.deptId })
        data.maleDorm.roomList = data.maleDorm.roomList.map(item => {
          return {
            ...item,
            isSelect: false,
            isPopover: false,
          }
        })
        data.femaleDorm.roomList = data.femaleDorm.roomList.map(item => {
          return {
            ...item,
            isSelect: false,
            isPopover: false,
          }
        })
        data.teacherDorm.roomList = data.teacherDorm.roomList.map(item => {
          return {
            ...item,
            isSelect: false,
            isPopover: false,
          }
        })
        state.assignData = data;
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    }
    const getNoAssignList = async () => {
      let params = {}
      if (state.buildId) {
        params.buildId = state.buildId
      }
      state.loading = true;
      try {
        let { data } = await getNotAllocatedRoom(params);
        data.roomList = data.roomList.map(item => {
          return {
            ...item,
            isSelect: false,
            isPopover: false,
          }
        })
        state.noAssignData = data;

        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    }
    const deptChange = (val) => {
      console.log(val);
      state.form.deptId = val.id
      state.form.deptName = val.deptName
      getAssignList()
    }
    const buildChange = () => {
      getNoAssignList();
    }
    const handleAssignRoom = async () => {
      if (!state.form.deptId) {
        return ElMessage.error("请选择需要分配房间的组织机构！")
      }
      state.loading = true
      let params = {
        buildId: state.buildId,
        deptId: state.form.deptId,
        roomIds: state.noAssignData.roomList.filter(item => item.isSelect).map(item => item.roomId),
      }
      try {
        let { message, code } = await allocDeptRoom(params)
        if (code === 0) {
          ElMessage.success(message)
          Promise.all([
            getAssignList(),
            getNoAssignList()
          ]).then(() => {
            state.loading = false;
          }).catch(() => {
            state.loading = false;
          })
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const handleCancel = async (type) => {
      let params = {
        deptId: state.form.deptId,
        roomIds: state.assignData.maleDorm.roomList.filter(item => item.isSelect).map(item => item.roomId)
      }
      if (type == 'boy') {
        params.roomIds = state.assignData.maleDorm.roomList.filter(item => item.isSelect).map(item => item.roomId)
      } else if (type == 'girl') {
        params.roomIds = state.assignData.femaleDorm.roomList.filter(item => item.isSelect).map(item => item.roomId)
      } else if (type == 'teacher') {
        params.roomIds = state.assignData.teacherDorm.roomList.filter(item => item.isSelect).map(item => item.roomId)
      }
      try {
        state.loading = true
        let { code, message } = await revokeDeptAllocatedRoom(params)
        if (code === 0) {
          ElMessage.success(message)
          Promise.all([
            getAssignList(),
            getNoAssignList()
          ]).then(() => {
            state.loading = false;
          }).catch(() => {
            state.loading = false;
          })
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }


    const selectItem = (item, type) => {
      console.log(type);
      item.isSelect = !item.isSelect

    }
    const beforeClose = () => {
      context.emit("update:modelValue", false)
    }
    onMounted(() => {
      getBuildingList()
    })
    return {
      state,
      isAssignDisabled,
      isBoyCancelDisabled,
      isGirlCancelDisabled,
      isTeacherCancelDisabled,
      selectItem,
      deptChange,
      handleAssignRoom,
      handleCancel,
      buildChange,
      beforeClose
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0 !important;
}

.box {
  .filter-msg {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;

    .msg {
      border-bottom: 1px solid #d7d7d7;
    }
  }

  .assign-box {
    height: 800px;
    display: flex;
    justify-content: space-between;

    .assign-left-box {
      width: 49%;
      height: 100%;
      border: 1px solid #d7d7d7;

      .top-box {
        height: 33.3%;
        // border-bottom: 1px solid #d7d7d7;
        display: flex;
        flex-direction: column;
      }

      .bottom-box {
        height: 33.3%;
        display: flex;
        flex-direction: column;
        border-top: 1px solid #d7d7d7;

      }
    }

    .assign-right-box {
      width: 49%;
      height: 100%;
      border: 1px solid #d7d7d7;
      display: flex;
      flex-direction: column;
    }

    .box-type {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      border-bottom: 1px solid #d7d7d7;
      padding: 0 10px;

      .box-title {
        font: bold 14px arial;
      }
    }

    .assign-room-box {

      flex: 1;
      padding: 0 20px 0px;
      overflow-y: auto;
      box-sizing: border-box;
      margin: 20px 0;
    }
  }
}
</style>