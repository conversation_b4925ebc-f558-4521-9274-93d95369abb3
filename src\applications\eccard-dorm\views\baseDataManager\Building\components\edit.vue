<template>
  <el-dialog v-loading="state.loading" :model-value="dialogVisible" :title="title" width="700px"
    :before-close="handleClose">
    <el-form inline label-width="120px" :model="state.form" ref="formRef" size="small" :rules="!isReadOnly && rules">
      <el-form-item label="楼栋号：" prop="buildNo">
        <el-input v-if="isReadOnly" :model-value="state.form.buildNo" readonly></el-input>
        <el-input v-else v-model="state.form.buildNo" placeholder="请输入楼栋号"></el-input>
      </el-form-item>
      <el-form-item label="楼栋名称：" prop="buildName">
        <el-input v-if="isReadOnly" :model-value="state.form.buildName" readonly></el-input>
        <el-input v-else v-model="state.form.buildName" placeholder="请输入楼栋名称"></el-input>
      </el-form-item>
      <el-form-item label="楼栋类型：" prop="buildType">
        <el-input v-if="isReadOnly" :model-value="state.form.buildTypeName" readonly></el-input>
        <el-select v-else v-model="state.form.buildType" placeholder="请选择">
          <el-option v-for="(item, index) in buildTypeList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属区域：" prop="areaId">
        <el-input v-if="isReadOnly" :model-value="state.form.areaName" readonly></el-input>
        <kade-area-select-tree v-else style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
          @valueChange="(val) => (state.form.areaId = val.id)" />
      </el-form-item>
      <el-form-item label="总单元数：" prop="unitCount">
        <el-input v-if="isReadOnly" :model-value="state.form.unitCount" readonly></el-input>
        <el-input-number v-else v-model="state.form.unitCount" :min="1" :max="50" @change="handleChange" />
      </el-form-item>
      <el-form-item label="总楼层数：" prop="floorCount">
        <el-input v-if="isReadOnly" :model-value="state.form.floorCount" readonly></el-input>
        <el-input-number v-else v-model="state.form.floorCount" :min="1" :max="50" @change="handleChange" />
      </el-form-item>

      <div class="last-input" v-if="isReadOnly" >
        <el-form-item label="经纬度：">
          <el-input
            :model-value="`经度：${state.form.lngPoint ? state.form.lngPoint : '无'}，纬度：${state.form.latPoint ? state.form.latPoint : '无'}`"
            readonly></el-input>
        </el-form-item>
      </div>
      <el-form-item v-else label="经纬度：">
        <el-input v-model="state.form.latAndLng" placeholder="请输入楼栋经纬度" style="display:inline" readonly
          @click="location()"></el-input><span v-if="!isReadOnly" class="el-icon-map-location location-icon"
          @click="location()"></span>
      </el-form-item>

      <div class="last-input">
        <el-form-item label="详细地址：" prop="address">
          <el-input v-if="isReadOnly" :model-value="state.form.address" readonly></el-input>
          <el-input v-else v-model="state.form.address" maxlength="200" placeholder="请输入楼栋详细地址"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template #footer v-if="!isReadOnly">
      <span class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button type="primary" @click="submit" size="small">确认</el-button>
      </span>
    </template>
  </el-dialog>
  <kade-edit-location :dialogLocation="state.dialogLocation" :latAndLng="state.form.latAndLng" @close="close">
  </kade-edit-location>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElInputNumber,
  ElMessage,
} from "element-plus";
import { reactive, ref } from "@vue/reactivity";
import { computed, nextTick, watch } from "@vue/runtime-core";
import { useDict } from "@/hooks/useDict.js";
import { buildingAdd, buildingEdit } from "@/applications/eccard-dorm/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
import location from "./location.vue"
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    rowData: {
      type: Object,
      default: null,
    },
  },
  components: {
    ElDialog,
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElInputNumber,
    "kade-edit-location": location,
    "kade-area-select-tree": areaSelectTree,
  },
  setup(props, context) {
    const buildTypeList = useDict("DORM_BUILDING_TYPE");
    const formRef = ref(null)
    const state = reactive({
      loading: false,
      dialogLocation: false,
      form: {},
    });
    const title = computed(() => {
      if (props.type === "add") {
        return "新建楼栋";
      } else if (props.type === "edit") {
        return "编辑楼栋";
      } else if (props.type === "detail") {
        return "楼栋详情"
      }
    });
    const isReadOnly = computed(() => {
      return props.type === 'detail' ? true : false
    })
    watch(() => props.dialogVisible, val => {
      if (val) {
        if (props.type == 'add') {
          state.form = {}
        } else {
          state.form = { ...props.rowData }
          state.form.latAndLng =  (state.form.lngPoint&&state.form.latPoint)?state.form.lngPoint + ',' + state.form.latPoint:""
        }
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })
    const rules = {
      buildNo: [
        {
          required: true,
          message: "请输入楼栋号",
        },
        {
          pattern: /^[0-9a-zA-Z]+$/,
          message: "请输入字母+数字",
        },
        {
          max: 20,
          message: "楼栋号长度不能超过20字符",
        },
      ],
      buildName: [
        {
          required: true,
          message: "请输入楼栋名称",
        },
        {
          max: 20,
          message: "楼栋名称长度不能超过20字符",
        },
        {
          pattern: /^\S.*\S$|(^\S{0,1}\S$)/,
          message: "楼栋名称首尾不能包含空格",
        },
      ],
      buildType: [
        {
          required: true,
          message: "请选择楼栋类型",
          trigger: "change",
        },
      ],
      areaId: [
        {
          required: true,
          message: "请选择所属区域",
          trigger: "change",
        },
      ],
      unitCount: [
        {
          required: true,
          message: "请输入总单元数",
        },
      ],
      floorCount: [
        {
          required: true,
          message: "请输入总楼层数",
        },
      ],
      /*       latAndLng: [
              {
                required: true,
                message: "请输入经纬度",
              },
            ] */
    }
    const location = () => {
      state.dialogLocation = true
    }

    const submit = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          let params = state.form
          params.lngPoint = params.latAndLng && params.latAndLng.split(",").length && params.latAndLng.split(",")[0]
          params.latPoint = params.latAndLng && params.latAndLng.split(",").length && params.latAndLng.split(",")[1]
          let fn = props.type == 'add' ? buildingAdd : buildingEdit
          state.loading = true
          try {
            let { code, message } = await fn(params)
            if (code === 0) {
              ElMessage.success(message)
              context.emit("close", true);
            }
            state.loading = false
          }
          catch {
            state.loading = false
          }

        } else {
          return false
        }
      })
    }

    const handleClose = () => {
      context.emit("close", false);

    };
    const close = (val) => {
      if (val) {
        console.log(val);
        state.form.latAndLng = val.lng.toFixed(6) + ',' + val.lat.toFixed(6)
      }
      state.dialogLocation = false

    };

    return {
      buildTypeList,
      formRef,
      state,
      rules,
      title,
      location,
      isReadOnly,
      submit,
      handleClose,
      close,
    };
  },
};
</script>

<style lang="scss" scoped>
.el-dialog {
  padding-bottom: 20px;

  .el-form {
    margin-top: 20px;

    .last-input {
      :deep(.el-input__inner) {
        width: 500px;
      }
    }

    :deep(.el-input__inner) {
      width: 178px;
    }

    :deep(.el-input-number) {
      width: 178px;
    }
  }
}

.location-icon {
  display: inline-block;
  font-size: 26px;
  line-height: 25px;
  color: #1296db;
  margin-left: 10px;
}
</style>