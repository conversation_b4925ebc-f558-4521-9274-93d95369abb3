<template>
  <div class="progress-box">
    <el-progress type="circle" :percentage="50" :stroke-width="100" color="#66eeff" :width="230" stroke-linecap="butt" />
    <img src="@/assets/progress-big.svg" alt="" class="big-img">
    <div class="round-big"></div>
    <div class="dashed-line"></div>
    <img src="@/assets/progress-small.svg" alt="" class="small-img">
    <div class="round-middle"></div>
    <div class="round-small"></div>
    <div class="solid-line"></div>
    <div class="percentage">{{ details.dueNumber ? (details.actuaNumber / details.dueNumber).toFixed(2) : 0 }}%</div>
  </div>
</template>
<script>
import { ElProgress } from "element-plus"
export default {
  components: {
    ElProgress,
  },
  props: {
    details: {
      type: Object,
      default: () => {
        return {
          dueNumber: 0,
          actuaNumber: 0,
          notReturnNumber: 0,
          lateNumber: 0,
          leaveNumber: 0,
        }
      }
    }
  },
  setup() {
    return {

    }
  }

}
</script>
<style lang="scss" scoped>
.progress-box {
  width: 230px;
  height: 230px;
  position: relative;

  .big-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    animation: rotate 5s linear infinite;
  }

  .round-big {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
  }

  .dashed-line {
    box-sizing: border-box;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    border: 1px dashed #75afff;
    position: absolute;
    top: 25px;
    left: 25px;
    z-index: 3
  }

  .small-img {
    width: 180px;
    height: 180px;
    position: absolute;
    top: 25px;
    left: 25px;
    z-index: 4;
    animation: rotateBack 4s linear infinite;
  }

  .round-middle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: #315080;
    position: absolute;
    top: 40px;
    left: 40px;
    z-index: 5;
  }

  .round-small {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    background: #000;
    position: absolute;
    top: 45px;
    left: 45px;
    z-index: 6;
  }

  .solid-line {
    box-sizing: border-box;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    border: 1px solid #75afff;
    position: absolute;
    top: 50px;
    left: 50px;
    z-index: 7
  }

  .percentage {
    width: 100px;
    height: 100px;
    font: 700 40px/100px arial;
    text-align: center;
    color: #FE7007;
    position: absolute;
    top: 65px;
    left: 65px;
    z-index: 8;
  }
}

:deep(.el-progress-circle__track) {
  stroke: #041036;
}

@keyframes rotate {
  0% {
    transform: rotateZ(0deg);
  }

  100% {
    transform: rotateZ(360deg);

  }
}

@keyframes rotateBack {
  0% {
    transform: rotateZ(0deg);
  }

  100% {
    transform: rotateZ(-360deg);

  }
}
</style>