<template>
  <el-dialog :model-value="modelValue" title="导入人员部门信息" width="440px" :before-close="handleClose">
    <div class="padding-box">
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="state.headers" :action="state.url" :disabled="state.loading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <span style="font-size:12px;vertical-align: baseline;color: #409EFF;cursor: pointer;" @click="importTemplate">下载模板</span>
          </div>
        </template>
      </el-upload>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button type="primary" @click="handleSubmit" size="small" :loading="state.loading">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, ref, watch, nextTick } from "vue";
import { onMounted } from "@vue/runtime-core";
import { getToken, downloadXlsx } from '@/utils';
import { updateBatchUserDeptTemplate } from "@/applications/eccard-basic-data/api";

import {
  ElButton,
  ElDialog,
  ElUpload,
  ElMessageBox
} from "element-plus";
export default {
  components: {
    ElDialog,
    ElButton,
    ElUpload,
  },
  props: {
    modelValue: Boolean,
  },
  setup(props, context) {
    const uploadRef = ref(null)
    const state = reactive({
      loading: false,
      open: false,
      // 设置上传的请求头部
      headers: { Authorization: "bearer " + getToken() },
      // 上传的地址
      url: CONFIG.BASE_API_PATH + ('eccard-basic-data/UserInfo/updateBatchUserDeptImport')
    });
    watch(
      () => props.modelValue,
      (val) => {
        if (val) {
          nextTick(() => {
            uploadRef.value.clearFiles()
          })
        }
      }
    );
    /** 下载模板操作 */
    const importTemplate = async () => {
      let res = await updateBatchUserDeptTemplate()
      downloadXlsx(res, "人员部门信息导入模板.xlsx")
    }
    // 文件上传中处理
    const handleFileUploadProgress = () => {
      state.loading = true;
    }
    // 文件上传成功处理
    const handleFileSuccess = (response) => {
      state.loading = false;
      uploadRef.value.clearFiles();
      ElMessageBox.alert(response.message, '导入结果', {
        confirmButtonText: '确定',
      })
      context.emit('success')
      context.emit('update:modelValue', false)
    }
    // 提交上传文件
    const handleSubmit = () => {
      uploadRef.value.submit();
    }
    const handleClose = () => {
      context.emit("update:modelValue", false);
    };
    onMounted(() => {
    });
    return {
      uploadRef,
      state,
      importTemplate,
      handleFileUploadProgress,
      handleFileSuccess,
      handleSubmit,
      handleClose,
    };
  },
};
</script>
<style lang="scss" scoped></style>