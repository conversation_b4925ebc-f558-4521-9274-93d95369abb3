<template>
  <el-dialog :modelValue="modelValue" :title="title" :width="'1200px'"  :before-close="cancel" :append-to-body="true">
    <el-row :gutter="5">
      <el-col :sm="12">
        <el-row :gutter="5">
          <el-col :sm="24">
            <el-card class="box-card">
              <template #header>
                请假申请信息
              </template>
              <el-form ref="formRef" :label-width="labelWidth" :rules="rules" :model="state.model" size="small">
                <el-form-item label="请假类型">
                  <el-input v-model="state.model.leaType" :readonly="state.model.disType=='info'" />
                </el-form-item>
                <el-form-item label="选择人员">
                  <el-input :modelValue="state.model.orgNameList + ' ' + state.model.userNum + ' ' + state.model.userName"  :readonly="state.model.disType=='info'" />
                </el-form-item>
                <el-form-item label="开始时间">
                  <el-input :value="state.model.leaStarttime"  :readonly="state.model.disType=='info'" />
                </el-form-item>
                <el-form-item label="结束时间">
                  <el-input :value="state.model.leaEndtime"  :readonly="state.model.disType=='info'" />
                </el-form-item>
                <el-form-item label="请假时长">
                  <el-input :value="state.model.leaTime"  :readonly="state.model.disType=='info'" />
                </el-form-item>
                <el-form-item label="请假原因">
                  <el-input :value="state.model.leaReson"  :readonly="state.model.disType=='info'" />
                </el-form-item>
                <el-form-item label="图片">
                  <div v-if="state.model.leaPath" style="margin:10px;float:left;height:80px;width:100%">
                    <img style="width: 70px; height: 70px;margin:5px;" v-for="(item,index) in (state.model.leaPath ? state.model.leaPath.toString().split(',') : [])" :key="index + 'ddd'" :src="item"/>
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="5">
          <el-col :sm="24">
            <el-card class="box-card">
              <template #header>
                审核进度
              </template>
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in state.model.examInfoList"
                  :key="index + 'eee'"
                  :type="'primary'"
                  :color="activity.exmResult == '0' ? '#67C23A' : '#E6A23C'"
                  :size="'large'"
                  :hollow="true"
                  :timestamp="activity.exmDate"
                >
                  <p>{{ activity.userName + ' ' +  activity.exmRole }}</p>
                  <p>{{ exaContent(activity) }}</p>
                </el-timeline-item>
              </el-timeline>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :sm="12">
        <el-row :gutter="5">
          <el-col :sm="24">
            <el-card class="box-card">
              <template #header>
                出入记录
              </template>
              <p style="margin:10px auto 0px 10px;">
                <el-form inline size="mini">
                  <el-form-item label="开始时间">
                    <el-date-picker v-model="state.form.usaStarttime" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" />
                  </el-form-item>
                  <el-form-item label="结束时间">
                    <el-date-picker v-model="state.form.usaEndtime" type="date" placeholder="开始时间搜索"  format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="reset">重置</el-button>
                  </el-form-item>
                </el-form>
              </p>
              <el-table style="width: 100%" :data="state.form.dataList" ref="multipleTable" v-loading="state.form.loading" height="55vh" border stripe>
                <el-table-column label="通行门名称" prop="acgName" align="center"></el-table-column>
                <el-table-column label="所属区域" prop="areaName" align="center"></el-table-column>
                <el-table-column label="通行时间" prop="usaDatetime" align="center"></el-table-column>
                <el-table-column label="通行方向" prop="usaDire" align="center"></el-table-column>
              </el-table>
              <div class="pagination">
                <el-pagination
                  background
                  :current-page="state.form.pageNum"
                  :page-size="state.form.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :page-sizes="[6, 10, 20, 50, 100]"
                  :total="state.form.total"
                  @current-change="handlePageChange"
                  @size-change="handleSizeChange"
                >
                </el-pagination>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
  import { reactive, watch , ref , computed , onMounted} from "vue";
  // import { dateStr ,hourMinStr ,timeStr } from "@/utils/date.js"
  import { getAttentionUserAccessPage , getAttentionLeaveApplyDetail} from "@/applications/eccard-ams/api";
  // import selectedManageuser from "@/applications/eccard-ams/views/attendaddinfo/components/selectedmanageuser.vue"
  import {
    ElRow,
    ElCol,
    ElTimeline,
    ElTimelineItem,
    ElCard,
    // ElSelect,
    // ElOption,
    // ElTimePicker,
    ElDatePicker,
    ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessage,
    // ElImage,
    // ElMessageBox,
    ElDialog,
  } from 'element-plus';

  const getDefaultModel = () => ({
    manageUser:{},
    isShowSelectedManagerUser:false,
    atgType:'',
    disType:'info'
  });
  export default {
    emits: ["close"],
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      data: {
        type: Object,
        default: () => ({}),
      },
      type :{
        type: String,
        default: "",
      },
    },
    components: {
      // selectedUsers,
      ElRow,
      ElCol,
      ElCard,
      ElTimeline,
      ElTimelineItem,
      ElDatePicker,
      // ElSelect,
      // ElOption,
      // ElTimePicker,
      ElInput,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      // ElImage,
      ElDialog,
    },
    setup(props, context) {
      const formRef = ref(null);
      const btnLoading = ref(false);
      const disType = ref(props.type);
      const state = reactive({
        model: getDefaultModel(),
        form:{
          usaStarttime:'',
          usaEndtime:'',
          pageNum:'',
          currentPage:1,
          pageSize:10,
          dataList:[],
          total:0,
          loading:false,
        }
      });
      const rules = {
      };
      const cancel = () => {
        formRef.value?.resetFields();
        context.emit("update:modelValue", false);
      };
      const reset=()=>{
        state.form={
          currentPage:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      //分页
      const getList=async ()=>{
        state.form.loading=true;
        let objs = {};
        objs["userId"] = state.model.userId;
        objs["usaStarttime"] = state.form.usaStarttime;
        objs["usaEndtime"] = state.form.usaEndtime;
        objs["currentPage"] = state.form.currentPage;
        objs["pageSize"] = state.form.pageSize;
        let {data}=await getAttentionUserAccessPage(objs)
        state.form.dataList=data.list
        state.form.total=parseInt(data.count)
        state.form.loading=false
      };

      const exaContent = (val) => {
        let str = '';
        if(val.exmResult==1){
          str = '审核通过';
        }else {
          str = '不通过';
          str += val.exmDesc;
        }
        return str;
      };

      const attrs = computed(() => {
        // eslint-disable-next-line no-unused-vars
        const { modelValue, role, ...attrs } = props;
        return attrs;
      });

      onMounted(() => {
      });

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            if (props.data?.leaId) {
              const { code, message , data } = await getAttentionLeaveApplyDetail(props.data.leaId);
              if(code != 0){
                ElMessage.error(message);return;
              }
              state.model = Object.assign(data);
              state.model["userId"] = props.data.userId;
              getList();
            } else {
              state.model = getDefaultModel();
              state.model["userId"] = "-1";
            }



            state.model.disType = props.type;
          }
        }
      );

      return {
        attrs,
        formRef,
        cancel,
        // submit,
        rules,
        labelWidth: THEMEVARS.formLabelWidth,
        state,
        disType,
        btnLoading,
        themes: THEMEVARS,
        exaContent,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
      };
    },
  };
</script>
