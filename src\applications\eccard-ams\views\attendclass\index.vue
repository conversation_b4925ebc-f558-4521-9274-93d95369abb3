<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="班次名称">
          <el-input v-model="state.form.atcClassname" placeholder="班次名称搜索" clearable="true"></el-input>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="班次列表1（备注：系统预置三个班次、一天一次班、一天两次班、一天三次班）">
      <template #extra>
        <el-button size="mini" type="success" icon="el-icon-plus" @click="edit('','add')">新增</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="班次名称1" prop="atcClassname" align="center"></el-table-column>
        <el-table-column label="班次" prop="atcType" align="center"></el-table-column>
        <el-table-column label="考勤时间" prop="atcTimes" align="center"></el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="edit(scope.row , 'info')" type="text" size="mini">详情</el-button>
            <el-button @click="edit(scope.row , 'edit')" type="text" size="mini">编辑</el-button>
            <el-button @click="del(scope.row , 'edit')" type="text" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <attend-group-edit :modelValue="state.isEdit" :title="state.type == 'add' ? '新增班次' : (state.type == 'edit' ? '编辑班次' : '班次详情')"
                       :type="state.type" :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
  </kade-route-card>
</template>
<script>
  import { reactive, onMounted} from "vue";
  import { useDict } from "@/hooks/useDict";
  import { getAttentionClassInfoPage , delAttentionClassInfoPage } from "@/applications/eccard-ams/api";
  import attendGroupEdit from "./components/edit.vue"
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    ElMessage,
    ElMessageBox
  } from 'element-plus';

  export default {
    components: {
      attendGroupEdit,
      ElForm,
      ElFormItem,
      ElInput,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
    },
    setup() {
      const atgTypeList = useDict("ATTENTION_GROUP_TYPE");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          atcClassname :'',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionClassInfoPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }

      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          currentPage:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const del=async (row)=>{
        ElMessageBox.confirm(`确定删除当前班次信息？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let {code,message}=await delAttentionClassInfoPage(row.atcId)
          if(code===0){
            ElMessage.success(message)
            getList()
          }
        });
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        atgTypeList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        del,
        close
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
