<template>
  <div class="income-detail border-box">
    <div class="padding-form-box grant-search-box">
      <el-form inline size="small" label-width="100px">
        <el-form-item label="充值项目名称:">
          <el-input
            placeholder="关键字搜索"
            v-model="state.form.projectName"
            :maxlength="20"
          ></el-input>
        </el-form-item>
        <el-form-item label="次数类型:">
          <el-select
            clearable
            v-model="state.form.frequencyType"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in frequencyTypeList"
              :key="index"
              :label="item.ftName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型:">
          <el-select
            clearable
            v-model="state.form.tradeType"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in state.tradeTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择日期:">
          <el-col :span="24">
            <el-date-picker
              v-model="state.requestDate"
              type="datetimerange"
              :default-time="state.defaultTime"
              range-separator="~"
              start-placeholder="请选择日期"
              end-placeholder="请选择日期"
              unlink-panels
              @change="changeDate"
            >
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-button
          @click="search()"
          size="small"
          type="primary"
          icon="el-icon-search"
          >查询</el-button
        >
        <el-button icon="el-icon-refresh-right" @click="reset()" size="small"
          >重置</el-button
        >
      </el-form>
    </div>

    <el-table
      style="width: 100%"
      :data="state.dataList"
      v-loading="state.listLoading"
      @row-click="rowClick"
      highlight-current-row
      border
      stripe
    >
      <el-table-column
        width="150"
        label="项目名称"
        prop="projectName"
        align="center"
      ></el-table-column>
      <el-table-column
        label="次数类型"
        prop="frequencyTypeName"
        align="center"
      ></el-table-column>
      <el-table-column
        label="交易类型"
        prop="costTypeName"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="auditStatus"
        label="状态"
        align="center"
        width="100"
      >
        <template #default="scope">
          {{ filterDictionary(scope.row.auditStatus, state.auditStatusList) }}
        </template>
      </el-table-column>
      <el-table-column
        label="发放总次数（次）"
        prop="totalAmount"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        label="发放总人数"
        prop="totalPerson"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column label="人员清单" align="center">
        <template #default="scope">
          <el-button
            type="text"
            @click="handleBtnClick(scope.row, true)"
            size="mini"
            >查看清单</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="导入人员"
        prop="createUserName"
        align="center"
      ></el-table-column>
      <el-table-column
        label="审核人员"
        prop="auditPerson"
        align="center"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        prop="createTime"
        align="center"
        width="200"
      >
        <template #default="scope">
          <div>
            {{ scope.row.createTime && timeStrDate(scope.row.createTime) }}
          </div>
        </template></el-table-column
      >
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button
            type="text"
            @click="listDetails(scope.row, 'see')"
            size="mini"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.currentPage"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.dataListTotal"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <el-divider></el-divider>
    <kade-dialog-list
      :projectId="state.projectId"
      :personListData="state.personListData"
      :isShow="state.isShow"
      @off="off"
    />
    <kade-edit-frequency-grant @offEdit="offEdit" />
    <kade-frequency-detail
      :projectId="state.projectDetailId"
      :frequencyDetailData="state.frequencyDetailData"
      :isShow="state.isReversalDetail"
      @offDetail="offDetail"
    />
  </div>
</template>
<script>
import { computed, onMounted, reactive, watch } from "vue";
import {
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDivider,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElMessage,
} from "element-plus";
import DialogList from "./components/DialogList.vue";
import editFrequencyGrant from "./components/editFrequencyGrant.vue";
import frequencyDetail from "./components/frequencyDetail.vue";
import { timeStr } from "@/utils/date.js";
import { requestDate,requestDefaultTime } from "@/utils/reqDefaultDate.js";
import {
  getFrequencyGrantListByPage,
  getFrequencyListByPage,
} from "@/applications/eccard-finance/api";

import { useStore } from "vuex";

export default {
  components: {
    "el-divider": ElDivider,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-date-picker": ElDatePicker,
    "el-col": ElCol,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "kade-dialog-list": DialogList,
    "kade-frequency-detail": frequencyDetail,
    "kade-edit-frequency-grant": editFrequencyGrant,
  },
  setup() {
    const store = useStore();
    const state = reactive({
      isShow: false,
      dialogVisibleAdd: false,
      isImportReversal: false,
      isReversalDetail: false,
      dataList: [],
      dataListTotal: 0,
      personListData: "",
      projectDetailId: "",
      projectId: "",
      frequencyDetailData: "",
      form: {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
      },
      requestDate: requestDate(),
      defaultTime:requestDefaultTime(),
      auditStatusList: JSON.parse(
        localStorage.getItem("kade_cache_dictionary")
      ).filter((item) => item.dictType == "SYS_AUDIT_STATUS"), //状态类型
      tradeTypeList: [
        { label: "充值", value: 101 },
        {
          label: "冲正",
          value: 102,
        },
      ],
      roleList: [],
      departCheckList: [],
    });

    watch(
      () => state.form,
      (val) => {
        console.log(val);
        store.commit("frequencyData/updateState", {
          key: "exportParam",
          payload: val,
        });
      },
      { deep: true }
    );

    const timeStrDate = computed(() => {
      return timeStr;
    });

    const frequencyTypeList = computed(() => {
      return store.state.frequencyData.frequencyTypeList;
    });

    const filterDictionary = (val, dictionaryList) => {
      for (let i of dictionaryList) {
        if (val == i.dictCode) {
          return i.dictValue;
        }
      }
    };

    watch(
      () => store.state.frequencyData.addSuccess,
      (val) => {
        if (val) {
          getList();
          store.commit("frequencyData/updateState", {
            key: "addSuccess",
            payload: false,
          });
        }
      }
    );

    const getList = () => {
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      store.commit("frequencyData/updateState", {
        key: "selectRow",
        payload: "",
      });
      state.listLoading = true;
      getFrequencyGrantListByPage(state.form)
        .then((res) => {
          state.dataList = res.data.list;
          state.dataListTotal = res.data.total;
          state.listLoading = false;
        })
        .catch(() => {
          state.listLoading = false;
        });
    };

    const changeDate = (val) => {
      state.form.beginDate = timeStr(val[0]);
      state.form.endDate = timeStr(val[1]);
    };
    const handleBtnClick = (val) => {
      state.projectId = val.id;
      let data = {
        currentPage: 1,
        pageSize: 6,
        projectId: val.id,
      };
      getFrequencyListByPage(data).then((res) => {
        state.personListData = res.data.pageInfo;
        state.isShow = true;
      });
    };
    const listDetails = (val, type) => {
      console.log(val, type);
      state.projectDetailId = val.id;
      state.frequencyDetailData = val;
      state.isReversalDetail = true;
    };

    const search = () => {
      getList();
    };

    const rowClick = (row) => {
      store.commit("frequencyData/updateState", {
        key: "selectRow",
        payload: row,
      });
    };
    const importRighting = () => {
      if (store.state.frequencyData.selectRow) {
        state.isImportReversal = true;
      } else {
        ElMessage.error("请选择次数项目！");
      }
    };

    const reset = () => {
      state.form = {
        beginDate: requestDate()[0],
        endDate: requestDate()[1],
        currentPage: 1,
        pageSize: 6,
        auditStatus: null,
        projectName: null,
        walletCode: null,
        tenantId: null,
      };
      state.requestDate = requestDate()
    };
    const off = () => {
      state.isShow = false;
    };
    const offEdit = (val) => {
      val && getList();
      store.commit("frequencyData/updateState", {
        key: "isEdit",
        payload: false,
      });
    };
    const offDetail = () => {
      state.isReversalDetail = false;
    };

    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };

    onMounted(() => {
      getList();
    });
    return {
      state,
      timeStrDate,
      frequencyTypeList,
      search,
      reset,
      off,
      rowClick,
      filterDictionary,
      changeDate,
      importRighting,
      offEdit,
      offDetail,
      listDetails,
      handleBtnClick,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  min-height: 680px;
  // overflow-y: scroll;
}
.grant-search-box {
  :deep(.el-input__inner) {
    width: 160px;
  }
  :deep(.el-date-editor) {
    width: 400px !important;
  }
}
</style>
