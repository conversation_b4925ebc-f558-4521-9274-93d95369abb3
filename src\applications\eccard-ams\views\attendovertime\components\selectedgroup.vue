<template>
  <el-dialog :modelValue="modelValue" :title="title"  :before-close="cancel" :append-to-body="true">
    <p style="margin:10px auto 0px 10px;">
      <el-form inline size="mini">
        <el-form-item label="考勤组名称">
          <el-input v-model="state.form.atgName" placeholder="班次名称搜索" clearable="true"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </p>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading"
                height="55vh" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="55" />
        <el-table-column label="考勤组名称" prop="atgName" align="center"></el-table-column>
        <el-table-column label="人数" prop="userCount" align="center"></el-table-column>
        <el-table-column label="考勤类型" prop="atgType" align="center"></el-table-column>
        <el-table-column label="考勤时间" prop="atcTimes" align="center">
          <template #default="scope">
            {{ scope.row.atgType == '自由考勤' ? '不设置班次，随时打卡' : scope.row.atcTimes }}
          </template>
        </el-table-column>
      </el-table>
    <template #footer>
      <p style="text-align: center">
      <el-button icon="el-icon-circle-close" @click="cancel" size="small">取消</el-button>
      <el-button icon="el-icon-circle-check" :loading="btnLoading" @click="submit" size="small" type="primary">保存</el-button>
      </p>
    </template>
  </el-dialog>

</template>
<script>
  import { reactive , onMounted , ref ,  watch , nextTick } from "vue";
  import {
    ElInput,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    // ElPagination,
    ElButton,
    // ElMessage,
    ElDialog
  } from 'element-plus';
  import {getAttentionGroupInfoPage } from "@/applications/eccard-ams/api";

  export default {
    components: {
      ElInput,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      // ElPagination,
      ElDialog,
      ElButton,
    },
    props: {
      title: {
        type: String,
        default: "",
      },
      modelValue: {
        type: Boolean,
        default: false,
      },
      type:{
        type: String,
        default: "",
      },
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    setup(props, context) {
      const multipleTable = ref(null);
      // const ideTypeList = useDict("ATTENTION_USER_IDE");
      const state = reactive({
        loading: false,
        isEdit:false,
        isFresh:'0',
        isEdit1:false,
        form:{
          atgName:'',
          currentPage:1,
          pageSize:10000
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
        selectData:[],
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionGroupInfoPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }
      const reset=()=>{
        state.form={
          currentPage:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSelectionChange = (val)=>{
        state.selectData = val;
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const cancel = () => {
        context.emit("update:modelValue", '');
      };
      const submit = () => {
        if(state.selectData){
          context.emit("update:modelValue", state.selectData);
        }
      };

      onMounted(()=>{
        getList();
      });

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            //getList();
            state.dataList.forEach((item) => {
              let row = props.data.filter(sitem => {
                return sitem.atgId == item.atgId
              });
              if(row.length > 0) {
                nextTick(() => {
                  multipleTable.value.toggleRowSelection(item, true);
                });
              }else{
                nextTick(() => {
                  multipleTable.value.toggleRowSelection(item, false);
                });
              }
            });
          }
        }
      );

      return {
        state,
        multipleTable,
        reset,
        search,
        cancel,
        submit,
        handlePageChange,
        handleSizeChange,
        handleSelectionChange,
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
