<template>
  <div class="violation-msg">
    <div class="violation-title">违规违纪</div>
    <div class="table-head">
      <div class="table-head-item">房间号</div>
      <div class="table-head-item">违规人员</div>
      <div class="table-head-item">违规时间</div>
      <div class="table-head-item">违规类型</div>
    </div>
    <div style="height: 155px;">
      <el-carousel height="155px" direction="vertical" :autoplay="true" :interval="5000" indicator-position="none" v-if="state.dataList.length">
        <el-carousel-item v-for="(item, index) in Math.ceil(state.dataList.length / 5)" :key="index" style="color:#fff">
          <div class="table-body" v-for="(v, i) in state.dataList.slice(index * 5, (index + 1) * 5) " :key="i">
            <div class="table-body-item">{{ v.roomName }}</div>
            <div class="table-body-item">{{ v.userName }}</div>
            <div class="table-body-item">{{ dateStr(v.breachTime) }}</div>
            <div class="table-body-item">{{ dictionaryFilter(v.breachType) }}</div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <div class="none-data" v-else>
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { reactive, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from "vue-router"
import { timeStr, dateStr } from "@/utils/date.js"
import { requestDate } from "@/utils/reqDefaultDate.js"
import { ElCarousel, ElCarouselItem, } from "element-plus"
import { getBreachPrinciple } from "@/applications/eccard-dorm/api";
export default {
  components: {
    ElCarousel, ElCarouselItem,
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      timer: null,
      dataList: [],
    })
    const getList = async () => {
      let params = {
        currentPage: 1,
        pageSize: 30,
        ...route.params,
        checkBeginDate: timeStr(requestDate()[0]),
        checkEndDate: timeStr(requestDate()[1]),
      }
      let { data: { list, } } = await getBreachPrinciple(params)
      state.dataList = list
    }
    onMounted(() => {
      getList()
      state.timer = setInterval(() => {
        getList()
      }, 10000)
    })
    onBeforeUnmount(() => {
      if (state.timer) {
        clearInterval(state.timer)
      }
    })
    return {
      dateStr,
      state
    }
  }
}
</script>\
<style lang="scss" scoped>
.violation-msg {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .violation-title {
    font-weight: 700;
    font-size: 18px;
    color: #0ee4f9;
    margin: 5px 0 10px;
  }

  .table-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    background: #1e408a;

    .table-head-item {
      text-align: center;
      flex: 1;
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .table-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 10px 0;
    box-sizing: border-box;
    height: 31px;
    .table-body-item {
      text-align: center;
      flex: 1;
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.el-carousel {
  width: 100%;
  height: 100%;
}

.none-data {
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>