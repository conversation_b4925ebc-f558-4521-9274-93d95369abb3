<template>
  <kade-route-card style="height: auto">
    <el-form ref="formRef" :label-width="'150px'" inline="true" :rules="rules" :model="state.model" size="default">
      <el-row :gutter="15">
        <el-col :sm="24">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <el-row :gutter="0">
                  <el-col :sm="12">
                    <i class="el-icon-quanxianshezhi" style="font-size: 15px;margin-top:10px">规则设置</i>
                  </el-col>
                  <el-col :sm="12" style="text-align: right">
                    <el-button :icon="Refresh" type="primary" @click="rest" v-if="state.model.type == 'info'">重置</el-button>&nbsp;&nbsp;
                  </el-col>
                </el-row>
              </div>
            </template>
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>考勤规则</span>
                </div>
              </template>
              <el-row :gutter="15">
                <el-col :sm="24">
                  <el-row :gutter="0">
                    <el-col :sm="24">
                      <el-icon><MessageBox />考勤规则</el-icon>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <el-col :sm="24">
                      <el-form-item label="迟到设置" prop="ovrName">
                        <el-input placeholder="请输入迟到设置信息" :modelValue="'迟到' + state.model.ruleLateMin + '分钟算缺勤'" v-if="state.model.type == 'info'" />
                        <div v-if="state.model.type == 'edit'">
                          迟到<el-input-number v-model="state.model.ruleLateMin" :min="1" :max="500" controls-position="right"/>分钟算缺勤
                        </div>
                      </el-form-item>
                      <el-form-item label="考勤开始时间" prop="ruleAttentTime" class="c1">
                        <el-input placeholder="请输入考勤开始时间信息" :modelValue="analydate4" v-if="state.model.type == 'info'" />
                        <div v-if="state.model.type == 'edit'">
                          每天 <el-time-picker v-model="state.model.ruleAttentTime" placeholder="请选择时间" format="HH:mm"></el-time-picker>开始新一天的考勤打卡
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>考勤记录分析规则</span>
                </div>
              </template>
              <el-row :gutter="0">
                <el-col :sm="24">
                  <el-form-item label="每日自动分析时间" prop="ovrName">
                    <el-input placeholder="请输入每日自动分析时间信息" :modelValue="analydate1" v-if="state.model.type == 'info'" />
                    <el-select v-model="state.model.ruleDayStartDay"  v-if="state.model.type == 'edit'">
                      <el-option v-for="(item,index) in ruleDayList" :key="index" :label="item.label" :value="item.label"></el-option>
                    </el-select>
                    <el-time-picker v-model="state.model.ruleDayStartTime" placeholder="请选择时间" format="HH:mm" v-if="state.model.type == 'edit'"></el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :sm="24">
                  <el-form-item label="每月自动分析时间" prop="ovrName"  class="c1">
                    <el-input placeholder="请输入每月自动分析时间信息" :modelValue="analydate2"
                              v-if="state.model.type == 'info' && state.model.ruleMonthStartDay=='当月最后一天'" />
                    <el-input placeholder="请输入每月自动分析时间信息" :modelValue="analydate3"
                              v-if="state.model.type == 'info' && state.model.ruleMonthStartDay=='次月自定义'" />
                    <el-select v-model="state.model.ruleMonthStartDay"  v-if="state.model.type == 'edit'">
                      <el-option v-for="(item,index) in ruleMonthList" :key="index + '11'" :label="item.label" :value="item.label"></el-option>
                    </el-select>

                    <el-select v-model="state.model.selectedDay"  v-if="state.model.type == 'edit' && state.model.ruleMonthStartDay == '次月自定义'">
                      <el-option v-for="(item,index) in state.model.listDays" :key="index + '22'" :label="item" :value="item"></el-option>
                    </el-select><span v-if="state.model.type == 'edit' && state.model.ruleMonthStartDay == '次月自定义'">号</span>
                    <el-time-picker v-model="state.model.ruleMonthStartTime" placeholder="请选择时间" format="HH:mm"
                                    v-if="state.model.type == 'edit'"></el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>报表符号规则</span>
                </div>
              </template>
                <el-row :gutter="0">
                  <el-col :sm="24">
                    <el-form-item label="早加班符号" prop="ruleEovertime">
                      <el-input placeholder="请输入早加班符号信息" v-model="state.model.ruleEovertime" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="晚加班符号" prop="ruleLovertime">
                      <el-input placeholder="请输入晚加班符号信息" v-model="state.model.ruleLovertime" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="节假日加班符号" prop="ruleHovertime">
                      <el-input placeholder="请输入节假日加班符号" v-model="state.model.ruleHovertime" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="上班未刷符号" prop="ruleWorkNflag">
                      <el-input placeholder="请输入上班未刷符号" v-model="state.model.ruleWorkNflag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="迟到符号" prop="ruleLateNflag">
                      <el-input placeholder="请输入迟到符号" v-model="state.model.ruleLateNflag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="休息日符号" prop="ruleRestFlag">
                      <el-input placeholder="请输入休息日符号" v-model="state.model.ruleRestFlag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="下班未刷符号" prop="ruleGoNflag">
                      <el-input placeholder="请输入下班未刷符号" v-model="state.model.ruleGoNflag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="早退符号" prop="ruleLeaveFlag">
                      <el-input placeholder="请输入早退符号" v-model="state.model.ruleLeaveFlag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="节假日符号" prop="ruleHolidayFlag">
                      <el-input placeholder="请输入节假日符号" v-model="state.model.ruleHolidayFlag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                    <el-form-item label="周日加班符号" prop="ruleWaddinfoFlag">
                      <el-input placeholder="请输入周日加班符号" v-model="state.model.ruleWaddinfoFlag" :readonly="state.model.type == 'info'" />
                    </el-form-item>
                  </el-col>
                </el-row>
            </el-card>
          </el-card>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="15">
      <el-col :sm="24">
        <p style="text-align: center;margin-top:20px">
          <el-button :icon="Document" type="primary"  v-if="state.model.type == 'info'" @click="state.model.type='edit'">编辑</el-button>
          <el-button :icon="Document" type="primary" @click="submit" v-if="state.model.type == 'edit'">确定</el-button>
          <el-button :icon="Document" type="primary" @click="cancel" v-if="state.model.type == 'edit'">取消</el-button>
        </p>
      </el-col>
    </el-row>
  </kade-route-card>
</template>
<script>
  import { reactive, ref, onMounted , computed , watch} from "vue";
  import { hourMinStr } from "@/utils/date.js"
  // import { useDict } from "@/hooks/useDict";
  import { getAttentionRuleDetail , updateAttentionRuleInfo , addAttentionRuleInfo } from "@/applications/eccard-ams/api";
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    // ElTable,
    // ElTableColumn,
    ElButton,
    // ElPagination,
    ElMessage,
    ElTimePicker,
    // ElDatePicker,
    ElIcon,
    ElMessageBox,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElRow,
    ElCol,
    ElCard
    // ElMessageBox
  } from 'element-plus';

  const getDefaultModel = () => ({
    ruleId:'',
    type:'info',
    listDays:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],
    selectedDay:1,
    ruleLateMin:60,
    ruleAttentTime:new Date(2022,11,4 ,0, 0),
    ruleDayStartDay:'当天',
    ruleDayStartTime:new Date(2022,11,4 ,0, 0),
    ruleMonthStartDay:'当月最后一天',
    ruleMonthStartTime:new Date(2022,11,4 ,23, 0),
    ruleEovertime:'早',
    ruleLovertime:'晚',
    ruleHovertime:'节',
    ruleWorkNflag:'<',
    ruleLateNflag:'迟',
    ruleRestFlag:'休',
    ruleGoNflag:'>',
    ruleLeaveFlag:'退',
    ruleHolidayFlag:'节',
    ruleWaddinfoFlag:'周',
  });
  export default {
    emits: ["close"],
    props: {
    },
    components: {
      ElForm,
      ElFormItem,
      ElInput,
      // ElTable,
      // ElTableColumn,
      ElButton,
      // ElPagination,
      ElTimePicker,
      // ElDatePicker,
      ElIcon,
      // ElMessageBox,
      ElInputNumber,
      ElSelect,
      ElOption,
      ElRow,
      ElCol,
      ElCard
    },
    setup(props) {
      const formRef = ref(null);
      const btnLoading = ref(false);
      const state = reactive({
        model: getDefaultModel(),
      });
      const ruleDayList = [
        {label:'当天',value:'当天'},
        {label:'次日',value:'次日'},
      ];
      const ruleMonthList= [
        {label:'当月最后一天',value:'当月最后一天'},
        {label:'次月自定义',value:'次月自定义'},
      ];
      const rules = {
        ruleEovertime: [
          { required: true, message: "请输入早加班符号信息信息" },
          { max: 5, message: "早加班符号信息不能超过5个字符" },
        ],
        ruleLovertime: [
          { required: true, message: "请输入晚加班符号信息信息" },
          { max: 5, message: "晚加班符号信息不能超过5个字符" },
        ],
        ruleHovertime: [
          { required: true, message: "请输入节假日加班符号信息" },
          { max: 5, message: "节假日加班符号不能超过5个字符" },
        ],
        ruleWorkNflag: [
          { required: true, message: "请输入上班未刷符号信息" },
          { max: 5, message: "上班未刷符号不能超过5个字符" },
        ],
        ruleLateNflag: [
          { required: true, message: "请输入请输入早加班符号信息" },
          { max: 5, message: "请输入早加班符号信息不能超过5个字符" },
        ],
        ruleRestFlag: [
          { required: true, message: "请输入休息日符号信息" },
          { max: 5, message: "休息日符号不能超过5个字符" },
        ],
        ruleGoNflag: [
          { required: true, message: "请输入下班未刷符号信息" },
          { max: 5, message: "下班未刷符号不能超过5个字符" },
        ],
        ruleLeaveFlag: [
          { required: true, message: "请输入早退符号信息" },
          { max: 5, message: "早退符号信息不能超过5个字符" },
        ],
        ruleHolidayFlag: [
          { required: true, message: "请输入节假日符号信息" },
          { max: 5, message: "节假日符号信息不能超过5个字符" },
        ],
        ruleWaddinfoFlag: [
          { required: true, message: "请输入周日加班符号信息" },
          { max: 5, message: "周日加班符号信息不能超过5个字符" },
        ],
      };
      const selectedManagerUserClose = (val)=>{
        if(val){
          state.model.manageUser.userId = val.userId;
          state.model.manageUser.userNum = val.userAccount;
          state.model.manageUser.userName = val.userName;
        }
        state.model.isShowSelectedManagerUser = false;
      };
      const selectedUsersClose = (vals)=>{
        if(vals){
          state.model.attendGroupList = [];
          vals.forEach((item) =>{
            state.model.attendGroupList.push({
              atgId:item.atgId ,
              atgName:item.atgName ,
            });
          });
        }
        state.model.isShowSelectedGroup = false;
      };
      const rest = () =>{
        ElMessageBox.confirm(`确认重置当前规则设置吗？？`,`提示`,{
          type:'warning',
          confirmButtonText:'确定',
          cancelButtonText:'取消'
        }).then(async()=>{
          let ruleId = state.model.ruleId;
          state.model = getDefaultModel();
          state.model.ruleId = ruleId;
          state.model.type = 'edit';
        });
      };
      const cancel = () => {
        pageLoad();
      };
      const submit = () => {
        formRef.value?.validate(async (valid) => {
          if (valid) {
            if(state.model.ruleDayStartTime == '' || state.model.toString().trim() == ''){
              ElMessage.error('每日自动分析时间信息不能为空！');return;
            }
            if(state.model.ruleMonthStartTime == '' || state.model.ruleMonthStartTime.toString().trim() == ''){
              ElMessage.error('每月自动分析时间信息不能为空！');return;
            }
            try {
              btnLoading.value = true;
              if(!state.model.ruleId){
                ElMessage.success("考勤规则未初始化，请与管理员联系！");return ;
              }
              let objs = {};
              objs = Object.assign(objs, state.model);
              objs["ruleAttentTime"] = hourMinStr(objs["ruleAttentTime"]);
              objs["ruleDayStartTime"] = hourMinStr(objs["ruleDayStartTime"]);
              if(objs.ruleMonthStartDay == '当月最后一天'){
                objs["ruleMonthStartTime"] = hourMinStr(objs["ruleMonthStartTime"]);
              }else{
                objs["ruleMonthStartTime"] = (objs["selectedDay"].toString().length == 1 ? '0' + objs["selectedDay"] : objs["selectedDay"]) + " " +  hourMinStr(objs["ruleMonthStartTime"]);
              }
              const fn = state.model.ruleId ? updateAttentionRuleInfo : addAttentionRuleInfo;
              const { message, code } = await fn(objs);
              if (code === 0) {
                ElMessage.success(message);
              } else {
                ElMessage.error(message);
              }
              //context.emit("close", true);
              state.model.type = 'info';
            } catch (e) {
              throw new Error(e.message);
            } finally {
              btnLoading.value = false;
            }
          }
        });
      };
      const attrs = computed(() => {
        // eslint-disable-next-line no-unused-vars
        const { modelValue, role, ...attrs } = props;
        return attrs;
      });

      const analydate1 = computed(()=>{
        return state.model.ruleDayStartDay + hourMinStr(state.model.ruleDayStartTime);
      });

      const analydate2 = computed(()=>{
        return state.model.ruleMonthStartDay +' ' + hourMinStr(state.model.ruleMonthStartTime);
      });

      const analydate3 = computed(()=>{
        return state.model.ruleMonthStartDay + state.model.selectedDay + "日" + hourMinStr(state.model.ruleMonthStartTime);
      });

      const analydate4 = computed(()=>{
        return '每天' +  hourMinStr(state.model.ruleAttentTime) + '开始新的一天考勤打卡';
      });

      const pageLoad = async ()=>{
        const { code, message , data } = await getAttentionRuleDetail(0);
        if(code != 0){
          ElMessage.error(message);return;
        }
        let objbase = getDefaultModel();
        objbase.type = "info";
        if(!data) {
          state.model = getDefaultModel();
        }else {
          if (!data.ruleAttentTime) {
            data.ruleAttentTime = new Date(2022, 11, 4, 0, 0);
          }else{
            data.ruleAttentTime = new Date(2022, 11, 4, parseInt(data.ruleAttentTime.toString().substr(0 ,2)) , parseInt(data.ruleAttentTime.toString().substr(3 , 2)));
          }
          if (!data.ruleDayStartTime) {
            data.ruleDayStartTime = new Date(2022, 11, 4, 0, 0);
          }else{
            data.ruleDayStartTime = new Date(2022, 11, 4, parseInt(data.ruleDayStartTime.toString().substr(0 ,2)) , parseInt(data.ruleDayStartTime.toString().substr(3 , 2)));
          }

          if(data.ruleMonthStartDay == '当月最后一天' && data.ruleMonthStartTime){
            data.ruleMonthStartTime = new Date(2022, 11, 4, parseInt(data.ruleMonthStartTime.toString().substr(0 ,2)) , parseInt(data.ruleMonthStartTime.toString().substr(3 , 2)));
          }else if(data.ruleMonthStartDay == '次月自定义' && data.ruleMonthStartTime){
            data["selectedDay"] = parseInt(data.ruleMonthStartTime.toString().substr(0 , 2));
            data.ruleMonthStartTime = new Date(2022, 11, 4, parseInt(data.ruleMonthStartTime.toString().substr(3 ,2)) , parseInt(data.ruleMonthStartTime.toString().substr(6 , 2)));
          }
          data.ruleLateMin = parseInt(data.ruleLateMin);


          state.model = Object.assign(objbase, data);
        }
      };

      onMounted(() => {
        pageLoad();
      });

      watch(
        () => props.modelValue,
        async (n) => {
          if (n) {
            pageLoad();
          }
        }
      );
      return {
        attrs,
        // update,
        formRef,
        cancel,
        submit,
        rules,
        labelWidth: THEMEVARS.formLabelWidth,
        state,
        // disType,
        btnLoading,
        themes: THEMEVARS,
        selectedUsersClose,
        selectedManagerUserClose,
        ruleDayList,
        ruleMonthList,
        pageLoad,
        rest,
        analydate1,
        analydate2,
        analydate3,
        analydate4
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.c1 .el-input__inner) {
    width:300px;
  }
  :deep(.c1 .el-input){
    width:300px;
  }
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
