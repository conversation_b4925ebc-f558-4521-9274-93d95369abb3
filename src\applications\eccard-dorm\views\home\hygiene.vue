<template>
  <kade-route-card>
    <template #header>
      <span class="header-title">近一周卫生检查不合格情况</span>
    </template>
    <div id="hygieneCharts" class="chartline" v-loading="state.loading"></div>
  </kade-route-card>
</template>
<script>
import * as echarts from "echarts";
import { onMounted, reactive } from "vue";
import { dayStr } from "@/utils/date.js"
import { getDormHygieneCheckNoPass } from "@/applications/eccard-dorm/api"

export default {
  setup() {
    const state = reactive({
      loading: false,
      statisticsData: []
    })
    const echartInit = () => {
      var chartDom = document.getElementById('hygieneCharts');
      var myChart = echarts.init(chartDom);
      var option;
      const arr = []
      for (let i = 7; i > 0; i--) {
        arr.push(dayStr(new Date().getTime() - 86400000 * i))
      }
      option = {
        xAxis: {
          type: 'category',
          data: arr
        },
        yAxis: {
          type: 'value'
        },
        grid: {
          top: 20,
          bottom: 20,
          left: 40,
          right: 0
        },
        series: [
          {
            data: state.statisticsData,
            type: 'line',
            label: {
              show: true,
              distance: 0,
              formatter: '{c}间'
            },
          }
        ]
      };

      option && myChart.setOption(option);
    };

    const getData = async () => {
      try {
        state.loading = true
        let { data } = await getDormHygieneCheckNoPass()
        console.log(data);
        state.statisticsData = data.map(item=>item.noPassCount)
        state.loading = false
      }
      catch {
        state.loading = false
      }
      echartInit();

    }

    onMounted(() => {
      getData()
    });
    return {
      state,
      echartInit,
    };
  }
}
</script>
<style scoped lang="scss">
.chartline {
  height: 300px;
  width: 100%;
}
</style>