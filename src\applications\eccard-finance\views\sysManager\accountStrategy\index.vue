<template>
  <kade-route-card style="height: auto">
    <kade-table-wrap title="开户策略列表">
      <template #extra>
        <el-button icon="el-icon-daoru" size="mini" type="success" @click="edit('add')">新增</el-button>
        <el-button icon="el-icon-daoru" size="mini" type="primary" @click="edit('edit')">编辑</el-button>
        <el-button icon="el-icon-daoru" size="mini" type="danger" @click="handleDel">删除</el-button>
      </template>
      <el-table style="width: 100%" :data="state.dataList" v-loading="state.loading" height="70vh" highlight-current-row
        border stripe @row-click="rowClick">
        <el-table-column width="160" prop="strategyName" label="策略名称" align="center"></el-table-column>
        <el-table-column width="100" prop="cardType" label="卡片类型" align="center">
          <template #default="scope">
            {{ scope.row.cardType+'类卡' }}
          </template>
        </el-table-column>
        <el-table-column width="600" prop="walletName" label="钱包类型" align="center"></el-table-column>
        <el-table-column label="是否启用" property="useStatus" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.useStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE" disabled>
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="添加人" prop="createUserName" align="center"></el-table-column>
        <el-table-column label="创建时间" prop="createDate" align="center" width="160">
          <template #default="scope">
            {{ timeStr(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" property="stId" style="color: rgb(26, 188, 104)" @click="details(scope.row.strategyId)"
              size="mini">查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background :current-page="state.form.currentPage" :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 20, 50, 100]" :total="state.dataListTotal"
          @current-change="handlePageChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <el-dialog v-model="state.dialogVisible" :title="state.title" append-to-body width="850px"
      :before-close="() => (state.dialogVisible = false)" :close-on-click-modal="false">
      <el-form style="margin-top:20px" label-width="150px" ref="ruleForm" :model="state.submitForm" :rules="state.rules"
        size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="策略名称:" prop="strategyName">
              <el-input style="width: 200px" v-model="state.submitForm.strategyName" :maxlength="20" size="mini">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="卡片类型:" prop="cardType">
              <el-select style="width: 200px" v-model="state.submitForm.cardType" size="mini">
                <el-option v-for="(item, index) in state.cardTypeList" :key="index" :label="item.ctName"
                  :value="item.ctCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择钱包类型:">
              <template v-for="(item, index) in state.walletActiveList" :key="index">
                <el-row justify="start">
                  <el-col :span="9">
                    <el-checkbox v-model="state.walletList[index].checked" type="item" :label="item.walletCode">{{
                      item.walletName
                    }}</el-checkbox>
                  </el-col>
                  <el-col :span="15">
                    <el-form-item :label="item.walletName + '有效期:'">
                      <el-input-number style="width: 200px" v-model="state.walletList[index].effectiveYear"
                        :maxlength="20" size="small"></el-input-number>&nbsp;年
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否启用:">
              <el-switch v-model="state.submitForm.useStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisible = false" size="mini">取消</el-button>
          <el-button type="primary" size="mini" @click="AddOrUpdate(state.title)">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="state.dialogVisibleInfo" :title="state.title" width="30%"
      :before-close="() => (state.dialogVisibleInfo = false)" :close-on-click-modal="false">
      <el-form>
        <el-form-item label="策略名称:">
          <el-input disabled v-model="state.submitForm.strategyName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="开户钱包类型:">
          <el-input disabled v-model="state.submitForm.walletName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-switch v-model="state.submitForm.useStatus" active-value="ENABLE_TRUE" inactive-value="ENABLE_FALSE"
            disabled></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="edit('edit')" size="mini">编辑</el-button>
          <el-button type="primary" size="mini" @click="state.dialogVisibleInfo = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </kade-route-card>
</template>

<script>
import { onMounted, reactive, ref, watch } from "vue";
import {
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElTable,
  ElSwitch,
  ElTableColumn,
  ElPagination,
  ElMessageBox,
  ElMessage,
  ElDialog,
  ElCheckbox,
  ElSelect,
  ElInputNumber,
  ElOption,
} from "element-plus";
import { timeStr } from "@/utils/date.js";
import {
  getAccountStrategyInfoById,
  addAccountStrategy,
  updateAccountStrategyById,
  getAccountStrategyListByPage,
  deleteAccountStrategyById,
  getCardTypeList,
  getWalletActiveList,
} from "@/applications/eccard-finance/api";

export default {
  components: {
    "el-col": ElCol,
    "el-row": ElRow,
    "el-dialog": ElDialog,
    "el-button": ElButton,
    "el-input": ElInput,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-pagination": ElPagination,
    "el-switch": ElSwitch,
    "el-checkbox": ElCheckbox,
    "el-select": ElSelect,
    "el-input-number": ElInputNumber,
    "el-option": ElOption,
  },
  setup() {
    const ruleForm = ref(null);
    const state = reactive({
      loading: false,
      dialogVisible: false,
      dialogVisibleInfo: false,
      title: "",
      isShow: false,
      dataList: [],
      dataListTotal: 0,
      selectRow: "",
      form: {
        currentPage: 1,
        pageSize: 10,
      },
      submitForm: {
        cardType: "",
        strategyName: "",
        useStatus: "ENABLE_TRUE",
        walletList: [
          {
            effectiveYear: "",
            walletCode: "",
          },
        ],
      },
      rules: {
        cardType: [
          {
            required: true,
            message: "请选择卡片类型",
            trigger: "change",
          },
        ],
        strategyName: [
          {
            required: true,
            message: "请输入策略名称",
            trigger: "blur",
          },
        ],
      },

      walletList: [],
      getORDeleteForm: {
        strategyId: "",
      },
      cardTypeList: [], //卡片类型列表
      walletActiveList: [], //钱包列表
    });

    watch(
      () => state.dialogVisible,
      (val) => {
        if (!val) {
          state.walletList.forEach((item) => {
            item.checked = false;
          });
        }
      }
    );

    const getList = () => {
      state.selectRow = "";
      state.loading = true;
      getAccountStrategyListByPage(state.form)
        .then((res) => {
          state.dataList = res.data.list;
          state.dataListTotal = res.data.total;
          state.loading = false;
        })
        .catch(() => {
          state.loading = false;
        });
    };
    const details = (val) => {
      console.log(val);
      state.getORDeleteForm.strategyId = val;
      getAccountStrategyInfoById(state.getORDeleteForm).then((res) => {
        state.submitForm = res.data;
      });
      state.title = "开户策略详情";
      state.dialogVisibleInfo = true;
    };
    const rowClick = (row) => {
      state.selectRow = row;
    };

    //获取交易钱包
    const queryWalletActiveList = async () => {
      let { data } = await getWalletActiveList();
      state.walletActiveList = data;
      state.walletList = state.walletActiveList.map((item) => {
        return {
          effectiveYear: 5,
          walletCode: item.walletCode,
          checked: false,
        };
      });
    };
    //获取卡片类型
    const queryCardTypeList = () => {
      getCardTypeList().then((res) => {
        state.cardTypeList = res.data;
      });
    };
    const edit = async (val) => {
      !state.walletActiveList.length && queryWalletActiveList();
      !state.cardTypeList.length && queryCardTypeList();
      if (val === "edit") {
        if (state.selectRow) {
          state.title = "编辑开户策略";

          let { data } = await getAccountStrategyInfoById({
            strategyId: state.selectRow.strategyId,
          });
          let {
            strategyWallets,
            strategyName,
            strategyId,
            cardType,
            useStatus,
          } = data;
          state.submitForm = { strategyName, strategyId, cardType, useStatus };
          let arr = strategyWallets.map((item) => {
            return {
              walletName: item.walletName,
              effectiveYear: item.termMonth / 12,
              walletCode: item.walletCode,
              checked: true,
            };
          });
          for (let i of state.walletList) {
            for (let j of arr) {
              if (i.walletCode == j.walletCode) {
                console.log(i, j);
                i.checked = true;
                i.effectiveYear = j.effectiveYear;
              }
            }
          }
          console.log(state.walletList, arr);
          state.dialogVisible = true;
        } else {
          ElMessage.error("请选择开户策略！");
        }
      } else if (val === "add") {
        state.submitForm = { useStatus: "ENABLE_TRUE" };
        state.title = "新增开户策略";
        state.dialogVisible = true;
      }
    };
    const AddOrUpdate = (val) => {
      state.submitForm.walletList = state.walletList.filter(
        (item) => item.checked
      );
      if (val === "编辑开户策略") {
        ruleForm.value.validate(async (valid) => {
          if (valid) {
            if (!state.submitForm.walletList.length) {
              return ElMessage.error("请选择钱包类型！");
            }
            let { code, message } = await updateAccountStrategyById(
              state.submitForm
            );
            if (code === 0) {
              ElMessage.success(message);
              state.dialogVisible = false;
              getList();
            } else {
              ElMessage.error(message);
            }
          } else {
            return false;
          }
        });
      } else if (val == "新增开户策略") {
        console.log(state.submitForm);
        ruleForm.value.validate(async (valid) => {
          if (valid) {
            if (!state.submitForm.walletList.length) {
              return ElMessage.error("请选择钱包类型！");
            }
            let { code, message } = await addAccountStrategy(state.submitForm);
            if (code === 0) {
              ElMessage.success(message);
              state.dialogVisible = false;
              getList();
            } else {
              ElMessage.error(message);
            }
          } else {
            return false;
          }
        });
      }
    };

    const search = () => {
      getList();
      state.selectRow = "";
    };
    const reset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10,
      };
    };
    const off = () => {
      state.isShow = false;
    };
    const handlePageChange = (val) => {
      state.form.currentPage = val;
      getList();
    };
    const handleSizeChange = (val) => {
      state.form.currentPage = 1;
      state.form.pageSize = val;
      getList();
    };
    const handleDel = () => {
      if (state.selectRow) {
        state.submitForm = { ...state.selectRow };
        ElMessageBox.confirm(
          `确认删除开户策略${state.submitForm.strategyName}?`,
          {
            type: "warning",
            closeOnPressEscape: false,
            closeOnClickModal: false,
          }
        ).then(async () => {
          try {
            state.getORDeleteForm.strategyId = state.submitForm.strategyId;
            const { message } = await deleteAccountStrategyById(
              state.getORDeleteForm
            );
            ElMessage.success(message);
            getList();
          } catch (e) {
            throw new Error(e.message);
          }
        });
      } else {
        ElMessage.error("请选择开户策略！");
      }
    };
    onMounted(() => {
      getList();
    });
    return {
      ruleForm,
      state,
      timeStr,
      details,
      rowClick,
      edit,
      search,
      reset,
      off,
      queryCardTypeList,
      AddOrUpdate,
      handleDel,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
.income-detail {
  height: 680px;
}

.padding-form-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-box {
  display: flex;
  justify-content: space-around;

  .grid-content {
    flex: 1;
  }

  .el-checkbox {
    height: 50px;
  }
}
</style>
