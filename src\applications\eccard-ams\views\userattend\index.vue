<template>
  <kade-route-card style="height: auto">
    <kade-table-filter @search="search()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="用户编号">
          <el-input v-model="state.form.userNum" placeholder="用户编号搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="用户名称">
          <el-input v-model="state.form.userName" placeholder="用户名称搜索" :clearable="true"></el-input>
        </el-form-item>
        <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.orgNameList" valueKey="deptPath" :multiple="false"
                                 @valueChange="(val) => (state.form.orgNameList = val.deptPath)" />
        </el-form-item>
        <el-form-item label="人员状态">
          <el-select v-model="state.form.userPostStatus" :clearable="true">
            <el-option v-for="(item,index) in statusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="人员信息列表">
      <template #extra>
        <!--<el-button size="mini" type="success" icon="el-icon-plus" @click="edit('','add')">发布</el-button>-->
        <!-- <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleClick(scope.row)">编辑</el-button>
        <el-button size="mini" type="danger" icon="el-icon-circle-close" @click="handleClick(scope.row)">删除</el-button> -->
      </template>
      <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
        <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
        <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
        <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
        <el-table-column label="性别" prop="userSex" align="center">
          <template #default="scope">
            {{ scope.row.userSex == 'SEX_MALE' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column label="手机号码" prop="userTel" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.userTel ?  scope.row.userTel.toString().substring(0 , 3) + '****' + scope.row.userTel.toString().substring(7) : '' }}
          </template>
        </el-table-column>
        <el-table-column label="组织机构" prop="orgNameList" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="身份类别" prop="userIde" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="人员状态" prop="userPostStatus" align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-tag size="small" :type=" scope.row.userPostStatus === 'STATE_IN' ? 'primary' : 'warning' ">{{
              dictionaryFilter(scope.row.userPostStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <template #default="scope">
            <el-button @click="edit(scope.row , 'edit')" type="text" size="mini">考勤记录</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="state.form.pageNum"
          :page-size="state.form.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[6, 10, 20, 50, 100]"
          :total="state.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-notify-publish-edit :modelValue="state.isEdit" :type="state.type" :data="state.rowData" @close="close" @edit="state.type='edit'" @update:modelValue="close"/>
  </kade-route-card>
</template>
<script>
  import {
    // ElSwitch,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
  } from 'element-plus';
  import { reactive, onMounted} from "vue";
  import { useStore } from "vuex";
  import deptSelectTree from '@/components/tree/deptSelectTree'
  // import {timeStr} from "@/utils/date"
  import { useDict } from "@/hooks/useDict";
  import { getAttentionUserAttendPage } from "@/applications/eccard-ams/api";
  import notifyPublishEdit from "@/applications/eccard-ams/views/userattend/components/edittab.vue"

  export default {
    components: {
      "kade-notify-publish-edit":notifyPublishEdit,
      // ElSwitch,
      ElForm,
      ElFormItem,
      ElInput,
      ElSelect,
      ElOption,
      'el-table': ElTable,
      'el-button': ElButton,
      'el-table-column': ElTableColumn,
      'el-pagination': ElPagination,
      "kade-dept-select-tree":deptSelectTree
    },
    setup() {
      const statusList = useDict("BASE_USER_STATE");
      const store = useStore();
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          selectedFlag:0,
          userNum : '',
          userName: '',
          orgId: '',
          userPostStatus : '',
          userDisType:2,
          pageNum:1,
          pageSize:10
        },
        dataList:[],
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true
        let {data}=await getAttentionUserAttendPage(state.form)
        state.dataList=data.list
        state.total=parseInt(data.count)
        state.loading=false
      }
      const edit=(row,type)=>{
        row["id"] = row.userId;
        store.commit("userInfo/updateState", {
          key: "rowData",
          payload: row,
        });
        store.commit("userInfo/updateState", {
          key: "isDetails",
          payload: true,
        });
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          userDisType:2,
          selectedFlag:0,
          pageNum:1,
          pageSize:10
        }
        getList();
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        statusList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        close
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
