<template>
  <div style="height: auto">
    <p>
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="时间">
          <el-date-picker
            v-model="state.form.dateRange"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </p>
    <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh" border stripe>
      <el-table-column label="用户编号" prop="userNum" align="center"></el-table-column>
      <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
      <el-table-column label="组织机构" prop="orgNameList" align="center"></el-table-column>
      <el-table-column label="所属考勤组" prop="atgName" align="center"></el-table-column>
      <el-table-column label="打卡时间" prop="atiCheckDate" align="center"></el-table-column>
      <el-table-column label="操作" prop="" align="center">
        <template #default="scope">
          <el-button @click="edit(scope.row , 'edit')" type="text" size="mini">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        :current-page="state.form.pageNum"
        :page-size="state.form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[6, 10, 20, 50, 100]"
        :total="state.total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <attend-edit :modelValue="state.isEdit" :type="state.type" :userinfo="{userName:state.rowData.userName , orgNameList :state.rowData.orgNameList , userPhoto:state.rowData.userPhoto }"
                 :data="state.rowData" @update:modelValue="close" @edit="state.type='edit'" />
  </div>
</template>
<script>
  import { reactive } from "@vue/reactivity";
  import { timeStr } from "@/utils/date.js";
  import { useDict } from "@/hooks/useDict";
  import { onMounted } from '@vue/runtime-core';
  import {
    ElDatePicker,
    ElForm,
    ElFormItem,
    ElTable,
    ElTableColumn,
    ElButton,
    ElPagination,
    // ElMessage,
    // ElMessageBox,
  } from 'element-plus';
  import { getAttentionInfoPage } from "@/applications/eccard-ams/api";
  // import deptSelectTree from "@/components/tree/deptSelectTree.vue";
  import attendEdit from "@/applications/eccard-ams/views/userattend/components/attendedit.vue"

  export default {
    props: {
      data:{
        type: Object,
        default: () => ({}),
      },
    },
    components: {
      ElDatePicker,
      ElForm,
      ElFormItem,
      ElTable,
      ElTableColumn,
      ElButton,
      ElPagination,
      attendEdit,
      // "kade-dept-select-tree": deptSelectTree,
    },
    setup(props) {
      const statusList = useDict("USER_ATTENDINFO_STATUS");
      const state = reactive({
        loading: false,
        isEdit:false,
        form:{
          atdId: parseInt(props.data.atdId),
          dateRange:'',
          atiStarttime:'',
          atiEndtime:'',
          atiAnalyResult : '',
          atdName: '',
          currentPage:1,
          pageSize:10
        },
        dataList:[],
        dataObj:props.data,
        total:0,
        rowData:"",
        type:"",
      });

      //分页
      const getList=async ()=>{
        state.loading=true;
        if(state.form.dateRange) {
          state.form.atiStarttime = timeStr(state.form.dateRange[0]);
          state.form.atiEndtime = timeStr(state.form.dateRange[1]);
        }else{
          state.form.atiStarttime = '';
          state.form.atiEndtime = '';
        }
        let {data}=await getAttentionInfoPage({atdId:state.form.atdId , atiStarttime:state.form.atiStarttime ,
          atiEndtime:state.form.atiEndtime , atdName : '' , currentPage : state.form.currentPage , pageSize:state.form.pageSize })
        state.dataList=data.list;
        state.total=parseInt(data.count);
        state.loading=false
      }
      const edit=(row,type)=>{
        state.type=type
        state.rowData=row
        state.isEdit=true
      }
      const reset=()=>{
        state.form={
          atdId: parseInt(props.data.atdId),
          currentPage:1,
          pageSize:10
        }
      }
      const search=()=>{
        getList()
      }
      const handlePageChange=(val)=>{
        state.form.pageNum=val
        getList()
      }
      const handleSizeChange=(val)=>{
        state.form.pageSize=val
        getList()
      }
      const del=async (row)=>{
        console.log(row)
        // let {code,message}=await delSysMessage(row.id)
        // if(code===0){
        //   ElMessage.success(message)
        //   getList()
        // }
      }
      const close=(val)=>{
        if(val){
          getList()
        }
        state.isEdit=false
      }

      onMounted(()=>{
        getList()
      })
      return {
        state,
        statusList,
        edit,
        reset,
        search,
        handlePageChange,
        handleSizeChange,
        del,
        close
      };
    },
  };
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    margin: 0;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #efefef;
  }

  :deep(.el-overlay) {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
  }

  :deep(.el-dialog__footer) {
    text-align: center;
  }
</style>
