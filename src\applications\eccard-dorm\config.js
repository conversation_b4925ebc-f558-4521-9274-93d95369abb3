window.CONFIG = {
  BASE_API_PATH: "http://*************:8121/",
  CACHE_PREFIX: 'kade_dorm_', //缓存前缀
  NAME: 'eccard-dorm', //应用相对路径，不包括host,port
  FETCH_TIMOUT: 50000, //请求过期时间
  ROUTE_BASE: '/', //路由头
  ROUTE_TYPE: 'hash', //路由模式，[ hash, history ]
  IS_ROUTE_BASE_REWRITE: false, //是否修改路由头
  APPID: 'eccard-dorm', //应用appId
  SECRECT: 'af28a85a546aa0390db3d31fee2c6db4', //应用秘钥
  TITLE: '智慧宿舍平台', //标题
  SCREEN_ROUTE_LIST: ["MonitorScreen", "CheckWorkScreen", "AIScreen"],//宿舍大屏路由列表
  SCREEN_TIMEOUT: 5000,//宿舍大屏切换时长
  SCREEN_CKECK_WORK_TIME: ['17:00:00', '24:00:00'],//考勤大屏展示时间段
};
