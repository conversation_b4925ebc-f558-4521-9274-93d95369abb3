<template>
  <el-button @click="handleClick">点点点</el-button>
  <div class="box1 " :class="active">
    <div class="child-box active" text="★"></div>
  </div>
</template>
<script>
import { ElButton } from "element-plus"
import { reactive, ref, onMounted } from 'vue'

import RSA from "@/utils/RSA.js"

export default {
  components: {
    ElButton,
  },
  setup() {
    const active = ref(null)
    const state = reactive({
    })
    onMounted(() => {
      let data = RSA.encrypt('针不戳！！！！！！！！！！！！！！')
      console.log(data);
    })
    const handleClick = async () => {
    }
    return {
      active,
      state,
      handleClick
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  width: 500px;
  display: flex;
  flex-wrap: wrap;
}

.box1 {
  position: relative;
  height: 500px;
  width: 500px;
  background: pink;

  .child-box {
    position: absolute;
    width: 300px;
    height: 300px;
    background: #fff;
    bottom: -0px;
    right: 0;
    color: #f00;

    &::before {
      content: attr(text);
      position: absolute;
      z-index: 10;
      color: pink;
      -webkit-mask: linear-gradient(to left, red, transparent);
    }
  }
}

.active {
  /* bottom: 300px !important;
  right: 300px !important; */
}
</style>