<template>
  <kade-route-card>
    <kade-table-filter @search="handleSearch" @reset="handleReset">
      <el-form inline label-width="90px" size="mini">
        <!--         <el-form-item label="组织机构">
          <kade-dept-select-tree style="width: 100%" :value="state.form.deptPath" valueKey="deptPath" :multiple="false"
            @valueChange="(val) => (state.form.deptPath = val.deptPath)" />
        </el-form-item> -->
        <el-form-item label="院级">
          <el-select clearable v-model="state.deptForm.deptFaculty" placeholder="请选择" @change="deptFacultyChange">
            <el-option v-for="(item, index) in state.deptFacultyList" :key="index" :label="item.deptName"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select clearable v-model="state.deptForm.deptGrade" placeholder="请选择" @change="deptGradeChange">
            <el-option v-for="(item, index) in state.deptGradeList" :key="index" :label="item.deptName"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="班级">
          <el-select clearable v-model="state.deptForm.deptGradeClass" placeholder="请选择">
            <el-option v-for="(item, index) in state.deptGradeClassList" :key="index" :label="item.deptName"
              :value="item.id" />
          </el-select>
        </el-form-item>


        <el-form-item label="考勤日期">
          <el-date-picker v-model="state.requestDate" type="daterange" range-separator="~" start-placeholder="请选择"
            end-placeholder="请选择" :size="size" />
        </el-form-item>
        <el-form-item label="考勤时段">
          <el-select clearable v-model="state.form.attendancePeriodId" placeholder="全部">
            <el-option v-for="(item, index) in state.periodIdList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="辅导员：">
          <el-input clearable v-model="state.assistant.teacherUserName"  @click="state.isPersoner = true"
            placeholder="请选择" @clear="handleClear"></el-input>
          <select-assistant-dialog :isMultiple="false" :isShow="state.isPersoner" @close="closePersonSelect" />
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="班级考勤汇总列表">
      <template #extra>
        <el-button class="btn-purple" icon="el-icon-bottom" type="" size="small" @click="exportClick">导出</el-button>
      </template>
      <el-table border :data="state.data" v-loading="state.loading">
        <el-table-column prop="fatherDeptName" label="院系" align="center"></el-table-column>
        <el-table-column prop="deptName" label="班级" align="center"></el-table-column>
        <el-table-column prop="totalStayPerson" label="入住人数" align="center"></el-table-column>
        <el-table-column prop="attendanceDate" label="考勤日期" align="center"></el-table-column>
        <el-table-column prop="attendancePeriodName" label="考勤时段" align="center"></el-table-column>
        <el-table-column prop="actuaNumber" label="正常考勤人数" align="center">
          <template #default="scope">
            <div :style="{ 'backgroundColor': scope.row.actuaNumber == scope.row.dueNumber ? '#03c316' : '' }">
              {{ scope.row.actuaNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="leaveNumber" label="请假人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': scope.row.leaveNumber > 0 ? '#aaaaaa' : '', color: scope.row.leaveNumber > 0 ? '#fff' : '' }">
              {{ scope.row.leaveNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lateNumber" label="晚归人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).lateReturnNumber > 0 ? '#f59a23' : '', color: attendanceNum(scope.row).lateReturnNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).lateReturnNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lateNumber" label="晚离人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).lateLeaveNumber > 0 ? '#f59a23' : '', color: attendanceNum(scope.row).lateLeaveNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).lateLeaveNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="未归人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).notReturnNumber > 0 ? '#d9001b' : '', color: attendanceNum(scope.row).notReturnNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).notReturnNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="notReturnNumber" label="未离人数" align="center">
          <template #default="scope">
            <div
              :style="{ 'backgroundColor': attendanceNum(scope.row).notLeaveNumber > 0 ? '#d9001b' : '', color: attendanceNum(scope.row).notLeaveNumber > 0 ? '#fff' : '' }">
              {{ attendanceNum(scope.row).notLeaveNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column label="详情" align="center" width="100px">
          <template #default="scope">
            <el-button v-if="scope.row.attendancePeriodName.includes('归')" type="text" @click="details('未归', scope.row)"
              size="mini" class="green">未归</el-button>
            <el-button v-if="scope.row.attendancePeriodName.includes('归')" type="text" @click="details('晚归', scope.row)"
              size="mini" class="green">晚归</el-button>

            <el-button v-if="scope.row.attendancePeriodName.includes('离')" type="text" @click="details('未离', scope.row)"
              size="mini" class="green">未离</el-button>
            <el-button v-if="scope.row.attendancePeriodName.includes('离')" type="text" @click="details('晚离', scope.row)"
              size="mini" class="green">晚离</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination :currentPage="state.form.currentPage" :page-size="state.form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100, 500]" background layout="total, sizes, prev, pager, next, jumper" :total="state.total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </kade-table-wrap>
    <kade-class-details :type="state.type" :dialogVisible="state.dialogVisible" :tableRow="state.tableRow"
      @close="() => state.dialogVisible = false"></kade-class-details>
  </kade-route-card>
</template>

<script>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElPagination,
} from "element-plus";
import { reactive } from "@vue/reactivity";
import details from "./components/details.vue";
import { dateStr } from "@/utils/date.js"
import { onMounted } from '@vue/runtime-core';
import { useDict } from "../../../../../hooks/useDict";
/* import deptSelectTree from "@/components/tree/deptSelectTree.vue"; */
import { attendanceClassSummaryList, exportAttendanceClassSummary, attendancePeriodList } from '@/applications/eccard-dorm/api.js'
import { userDeptFacultyList, userDeptGradeNewList, userDeptGradeClassList } from '@/applications/eccard-sys/api.js'
import selectAssistantDialog from "@/components/table/selectAssistantDialog.vue";

export default {
  components: {
    ElButton,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElTable,
    ElTableColumn,
    ElPagination,
    "kade-class-details": details,
    "select-assistant-dialog": selectAssistantDialog,
    /*     "kade-dept-select-tree": deptSelectTree, */
  },
  setup() {
    const lateFlagList = useDict('SYS_BOOL_STRING')
    const notReturnList = useDict('SYS_BOOL_STRING')
    const state = reactive({
      isPersoner: false,
      form: {
        pageSize: 10,
        currentPage: 1
      },
      deptForm: {
        deptFaculty: "",
        deptGrade: "",
        deptGradeClass: ""
      },
      assistant: {
        teacherUserName:"",
      },
      total: 0,
      data: [],
      loading: false,
      requestDate: [],
      type: "",
      dialogVisible: false,
      periodIdList: [],
      deptFacultyList: [],
      deptGradeList: [],
      deptGradeClassList: [],
      tableRow: ''
    });
    const getPeriod = async () => {
      let { data } = await attendancePeriodList()
      state.periodIdList = data
    }
    const getUserDeptFacultyList = async () => {
      let { data } = await userDeptFacultyList()
      state.deptFacultyList = data
    }
    const getUserDeptGradeNewList = async (params) => {
      let { data } = await userDeptGradeNewList(params)
      state.deptGradeList = data
    }
    const getUserDeptGradeClassList = async (params) => {
      let { data } = await userDeptGradeClassList(params)
      state.deptGradeClassList = data
    }


    const getList = async () => {
      if (state.requestDate && state.requestDate.length) {
        state.form.beginDate = dateStr(state.requestDate[0])
        state.form.endDate = dateStr(state.requestDate[1])
      } else {
        delete state.form.beginDate
        delete state.form.endDate
      }

      let params = { ...state.form }
      if (state.deptForm.deptGradeClass) {
        params.deptPath = state.deptGradeClassList.find(item => item.id == state.deptForm.deptGradeClass).deptPath
      } else if (state.deptForm.deptGrade) {
        params.deptPath = state.deptGradeList.find(item => item.id == state.deptForm.deptGrade).deptPath
      } else if (state.deptForm.deptFaculty) {
        params.deptPath = state.deptFacultyList.find(item => item.id == state.deptForm.deptFaculty).deptPath
      }

      if (state.assistant.teacherUserId) {
        params.deptDirectorId = state.assistant.teacherUserId
      }


      state.loading = true
      try {
        let { data: { list, total } } = await attendanceClassSummaryList(params)
        state.data = list
        state.total = total
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };


    const deptFacultyChange = (val) => {
      state.deptGradeList = []
      state.deptGradeClassList = []
      state.deptForm.deptGrade = ""
      state.deptForm.deptGradeClass = ""
      if (val) {
        getUserDeptGradeNewList(val)
      }
    }
    const deptGradeChange = (val) => {
      state.deptGradeClassList = []
      state.deptForm.deptGradeClass = ""
      if (val) {
        getUserDeptGradeClassList(val)
      }
    }
    const closePersonSelect = (val) => {
      console.log(val);
      if (val) {
        state.assistant = val
      }
      state.isPersoner = false
    }
    const handleClear=()=>{
      state.assistant={
        teacherUserName:""
      }
    }
    const handleSizeChange = (val) => {
      state.form.currentPage = 1
      state.form.pageSize = val
      getList()
    };
    const handleCurrentChange = (val) => {
      state.form.currentPage = val
      getList()
    }
    const attendanceNum = (row) => {
      if (row.attendancePeriodName.includes('归')) {
        return {
          lateReturnNumber: row.lateNumber,
          lateLeaveNumber: 0,
          notReturnNumber: row.notReturnNumber,
          notLeaveNumber: 0,
        }
      } else {
        return {
          lateReturnNumber: 0,
          lateLeaveNumber: row.lateNumber,
          notReturnNumber: 0,
          notLeaveNumber: row.notReturnNumber,
        }
      }
    }
    const details = (type, row) => {
      console.log(row);
      state.type = type
      state.tableRow = row
      state.dialogVisible = true;
    };
    const handleSearch = () => {
      getList()
    };
    const handleReset = () => {
      state.form = {
        currentPage: 1,
        pageSize: 10
      }
      state.deptForm = {
        deptFaculty: "",
        deptGrade: "",
        deptGradeClass: ""
      }
      state.assistant = {}
      state.requestDate = []
      getList()
    };
    const exportClick = async () => {
      let res = await exportAttendanceClassSummary(state.form)
      let url = window.URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      )
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '班级考勤汇总表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    onMounted(() => {
      getList()
      getPeriod()
      getUserDeptFacultyList()
    })
    return {
      state,
      details,
      lateFlagList,
      notReturnList,
      exportClick,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      attendanceNum,
      deptFacultyChange,
      deptGradeChange,
      closePersonSelect,
      handleClear
    };
  },
};
</script>

<style lang="scss" scoped>
.green :hover {
  text-decoration: underline;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eeeeee;
}

:deep(.el-divider--horizontal) {
  margin: 0;
}

:deep(.el-icon-date:before) {
  display: none;
}

:deep(.el-dialog__body) {
  padding-bottom: 20px !important;
}

:deep(.el-table--border) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-input--mini .el-input__inner) {
  width: 210px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  width: 100px;
}

:deep(.el-pagination__editor.el-input .el-input__inner) {
  width: 46px;
}

:deep(.el-table td) {
  margin: 0;
  padding: 0;
  height: 50px;
}

:deep(.el-table .cell) {
  margin: 0;
  padding: 0;
}

:deep(.el-table td div) {
  box-sizing: border-box;
  height: 50px;
  line-height: 50px;
}
</style>