<template>
  <!-- 电费充值报表 -->
  <div class="padding-box">
    <kade-table-filter @search="getList()" @reset="reset()">
      <el-form inline size="mini" label-width="100px">
        <el-form-item label="充值时间:">
          <el-date-picker v-model="state.requestDate" type="datetimerange" unlink-panels :default-time="defaultTime" range-separator="~" start-placeholder="请选择日期" end-placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </kade-table-filter>
    <kade-table-wrap title="电费充值报表" v-loading="state.loading">
      <template #extra>
        <el-button @click="exportClick()" icon="el-icon-daoru" size="mini" class="btn-blue">导出</el-button>
        <el-button @click="printClick()" size="mini" class="btn-purple" icon="el-icon-printer">打印</el-button>
      </template>
      <el-table style="width: 100%" :data="state.detailList" ref="multipleTable" height="55vh" highlight-current-row border stripe>
        <el-table-column v-for="item in state.column" :key="item.prop" :width="item.width" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip>
          <template v-if="item.render" #default="scope">
            {{ item.render(scope.row[item.prop]) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <kade-table-field-filter :column="column" @change="v=>state.column=v" />
      </div>
    </kade-table-wrap>
  </div>
</template>
<script>
import { reactive, onMounted } from "vue";
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElMessage
} from "element-plus";
import { downloadXlsx, print } from "@/utils/index.js"
import { timeStr, hasDateTimeRange } from "@/utils/date.js";
import { requestDate, defaultTime } from "@/utils/reqDefaultDate";
import {
  eleReportList,
  eleReportListExport,
  eleReportPrint
} from "@/applications/eccard-finance/api";
const column = [
  { label: "组织机构", prop: "deptName", width: "" },
  { label: "现金消费", prop: "cashRechargeEle", width: "" },
  { label: "补助消费", prop: "subsidyRechargeEle", width: "" },
  { label: "微信消费", prop: "weChatRechargeEle", width: "" },
  { label: "小计", prop: "totalRechargeEle", width: "" },
]
export default {
  components: {
    "el-button": ElButton,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    ElDatePicker,
  },
  setup() {
    const state = reactive({
      loading: false,
      column,
      form: {},
      detailList: [],
      requestDate: requestDate(),
    });

    const getParams = () => {
      let params = { ...state.form }
      if (state.requestDate && state.requestDate.length) {
        params.startDate = timeStr(state.requestDate[0]);
        params.endDate = timeStr(state.requestDate[1]);
      } else {
        ElMessage.error("请选择充值时间")
        return false
      }
      if (hasDateTimeRange([params.startDate, params.endDate], 6)) {
        ElMessage.error("查询日期范围不能操作6个月")
        return false
      }
      return params
    }
    const getList = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true;
      try {
        let { code, data } = await eleReportList(params);
        if (code === 0) {
          state.detailList = data;
        }
        state.loading = false;
      }
      catch {
        state.loading = false;
      }
    };
    const exportClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let res = await eleReportListExport(params);
        downloadXlsx(res, '电费充值报表.xlsx')
        state.loading = false
      }
      catch {
        state.loading = false
      }
    };
    const printClick = async () => {
      if (!getParams()) return
      let params = getParams()
      state.loading = true
      try {
        let { data, code } = await eleReportPrint(params);
        if (code === 0) {
          print(data)
        }
        state.loading = false
      }
      catch {
        state.loading = false
      }
    }
    const reset = () => {
      state.requestDate = requestDate()
      state.form = {}
    };
    onMounted(() => {
      // getList();
    });
    return {
      defaultTime,
      column,
      state,
      exportClick,
      printClick,
      getList,
      reset,
    };
  },
};
</script>

